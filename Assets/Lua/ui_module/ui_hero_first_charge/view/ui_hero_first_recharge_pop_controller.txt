local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local event = require "event"
local gw_total_recharge_pop_mgr = require "gw_total_recharge_pop_mgr"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"

--region Controller Life
module("ui_hero_first_recharge_pop_controller")
local controller = nil
local UIController = newClass("ui_hero_first_recharge_pop_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.CData.listData = {}
    self.CData.atyID = data.atyID
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:UpdateGiftView()
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    local limit_gift_define = require("limit_gift_define")
    self.buyBack = function(eventName, msg)
        if eventName == "GW_REFRESH_RECHARGE_GOODS" then
            local net_vip_module = require "net_vip_module"
            net_vip_module.Send_VIP_GETLIMITGIFTDETAIL_REQ()
        end
        self:UpdateGiftView()
    end
    self:RegisterEvent(event.GW_REFRESH_RECHARGE_GOODS, self.buyBack)
    self:RegisterEvent(limit_gift_define.RefreshPage, self.buyBack)
    --vip特殊处理
    self:RegisterEvent(event.GW_VIP_SPECIAL_GIFT_CHANGE, self.buyBack)
    self:RegisterEvent(event.GW_VIP_LEVEL_UP, self.buyBack)
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end
function UIController:UpdateGiftView()
    local listData = gw_total_recharge_pop_mgr.GetRechargeList(self.CData.atyID)

    --{ [1],[2],[3]}
     --JumpDetailUI
    
    
    self:TriggerUIEvent("UpdateGiftView", listData)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
