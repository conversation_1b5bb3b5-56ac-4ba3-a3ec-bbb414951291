local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local UIUtil = CS.Common_Util.UIUtil

local ui_window_mgr = require "ui_window_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
local game_scheme = require "game_scheme"
local log = require "log"
local color_palette = require "color_palette"
local reward_mgr = require "reward_mgr"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_hero_first_recharge_pop_binding"
local GameObject = CS.UnityEngine.GameObject
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
--region View Life
module("ui_hero_first_recharge_pop")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:UpdateGiftView(refreshData)
    self:ShowReward(refreshData)
    self:RefreshBottomGift()
end
-- 显示列表
function UIView:ShowReward(data)
    if not data then
        return
    end
    self.normalRewardList = self.normalRewardList or {}
    self.rechargeItemList = self.rechargeItemList or {}
    local index_normal = 0
    local index_recharge = 0
    for k, v in ipairs(data) do
        if v then
            -- log.Warning("[XQ] v.Type", k, v.cfg.Type, v.cfg.Id, v.isJump)
            if v.isJump == true then
                index_normal = index_normal + 1
                --self:ShowNormalItem(index_normal, v)
            else
                index_recharge = index_recharge + 1
                self:ShowRechargeItem(index_recharge, v)
            end
        end
    end
    local normaleNum = #self.normalRewardList
    for i = index_normal + 1, normaleNum do
        self:SetActive(self.normalRewardList[i].item, false)
    end
    local rechargeNum = #self.rechargeItemList
    for i = index_recharge + 1, rechargeNum do
        self:SetActive(self.rechargeItemList[i].item, false)
    end
end

-- 充值样式的item
function UIView:ShowRechargeItem(k, dataItem)
    if not self.rechargeItemList[k] then
        self.rechargeItemList[k] = {
            item = GameObject.Instantiate(self.rtf_ComGiftItem, self.rtf_ComGiftItem.parent)
        }
    end
    local rechargeItem = self.rechargeItemList[k]
    local item = rechargeItem.item
    if not item then
        log.Error("未出现预设 self.rtf_ComGiftItem", k)
        return
    end
    rechargeItem.scroll_rect_item = rechargeItem.scroll_rect_item or item.gameObject:GetComponent(typeof(ScrollRectItem))
    local scroll_rect_item = rechargeItem.scroll_rect_item
    if not scroll_rect_item then
        return
    end
    -- 获取UI组件
    rechargeItem.txtName = rechargeItem.txtName or scroll_rect_item:Get("txtName")
    rechargeItem.txtRecharge = rechargeItem.txtRecharge or scroll_rect_item:Get("txtRecharge")
    rechargeItem.btnRecharge = rechargeItem.btnRecharge or scroll_rect_item:Get("btnRecharge")
    rechargeItem.goodsParent = rechargeItem.goodsParent or scroll_rect_item:Get("goodsParent")
    rechargeItem.bgTitle = rechargeItem.bgTitle or scroll_rect_item:Get("bgTitle")
    rechargeItem.txtNameOutline = rechargeItem.txtNameOutline or scroll_rect_item:Get("txtNameOutline")
    rechargeItem.btnShowDetail = rechargeItem.btnShowDetail or scroll_rect_item:Get("btnShowDetail")

    scroll_rect_item.data = scroll_rect_item.data or {}
    -- 设置背景颜色和轮廓颜色
    self:SetTitleColors(rechargeItem.bgTitle, rechargeItem.txtNameOutline, dataItem.cfg)

    -- 设置按钮点击事件
    self:AddOnClick(rechargeItem.btnRecharge.onClick, function()
        dataItem.BuyRechargeFun(dataItem)
    end)
    self:AddOnClick(rechargeItem.btnShowDetail.onClick, function()
        --dataItem.JumpDetailUI(dataItem)
        self:JumpDetailUI(dataItem)
    end)

    -- 设置文本内容
    rechargeItem.txtName.text = lang.Get(dataItem.rechargeCfg.strGoodsNameID.data[0])
    rechargeItem.txtRecharge.text = dataItem.priceText

    -- 渲染奖励项
    self:RenderReward(scroll_rect_item, dataItem, rechargeItem.goodsParent)
    self:SetActive(item, true)
end

function UIView:SetTitleColors(bgTitle, txtNameOutline, cfg)
    if not string.empty(cfg.titleBgColor) then
        bgTitle.color = color_palette.HexToColor(cfg.titleBgColor)
    end
    if not string.empty(cfg.titleOutlineColor) then
        txtNameOutline.effectColor = color_palette.HexToColor(cfg.titleOutlineColor)
    end
end

function UIView:GetLimitText(count, limitNum, refreshType)
    if limitNum and limitNum > 0 then
        count = count == 0 and string.format("<color=#FF4D2A>%s</color>", count) or string.format("<color=#4BAA41>%s</color>", count)
        if refreshType == 1 then
            return string.format2(lang.Get(1001457), count, limitNum)
        elseif refreshType == 2 then
            return string.format2(lang.Get(1001353), count, limitNum)
        end
    end
    return ""
end

function UIView:RenderReward(scroll_rect_item, rechargeData, goodsParent)
    scroll_rect_item.data.itemList = scroll_rect_item.data.itemList or {}
    for k, rewardData in ipairs(rechargeData.rewardList) do
        local item = scroll_rect_item.data.itemList[k]
        if item and item.type ~= rewardData.nType then
            item:Dispose()
            item = nil
        end
        if item then
            item:SetRewardData(rewardData.id, rewardData.num, rewardData.nType)
        else
            item = reward_mgr.GetRewardItemData(rewardData, goodsParent, true, 0.6)
            table.insert(self.VData, item)
        end
        scroll_rect_item.data.itemList[k] = item
    end
end

---@public function 自定义特殊跳转
function UIView:JumpDetailUI(rechargeData)
    local systemSourceCfg = game_scheme:SystemSource_0(tonumber(rechargeData.cfg.Jump))
    if systemSourceCfg then
        local flag = systemSourceCfg.flag
        local jumpParam = rechargeData.cfg.ActivityMainID
        local param = {
            activityID = rechargeData.cfg.CumulativeRechargeActivityID,
            activityCenterBackBtnFun = function(entranceID,activityID)
                local returnActivityID = rechargeData.cfg.CumulativeRechargeActivityID
                festival_activity_mgr.OpenActivityUIByActivityID(returnActivityID)
                --ui_window_mgr:ShowModule("ui_hero_first_recharge_pop", nil, nil, {atyID = returnActivityID})
            end
        }
        local module_jumping = require "module_jumping"
        module_jumping.Jump(flag, { jumpParam, param })
        ui_window_mgr:UnloadModule("ui_hero_first_recharge_pop")
        if rechargeData.isLimit then
            --关闭活动中心
            festival_activity_mgr.CloseActivityCenterUI()
        end
    end
end
---@public function 刷新底部跳转
function UIView:RefreshBottomGift()
    self:AddOnClick(self.btn_Recharge.onClick, function()
        festival_activity_mgr.OpenActivityUIByActivityID(1401)
        ui_window_mgr:UnloadModule("ui_hero_first_recharge_pop")
    end)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
