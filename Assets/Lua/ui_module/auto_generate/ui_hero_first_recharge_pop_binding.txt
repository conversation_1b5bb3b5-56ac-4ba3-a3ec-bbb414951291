local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local RectTransform = CS.UnityEngine.RectTransform


module("ui_hero_first_recharge_pop_binding")

UIPath = "ui/prefabs/gw/gw_herofirstcharge/uiherofirstrechargepop.prefab"

WidgetTable ={
	btn_closeBtn = { path = "btn_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	ss_bg = { path = "ss_bg", type = SpriteSwitcher, },
	rtf_rewardNormal = { path = "rtf_rewardNormal", type = RectTransform, },
	rtf_ComGiftItem = { path = "rtf_rewardNormal/Viewport/reward/rtf_ComGiftItem", type = RectTransform, },
	rtf_BottomGift = { path = "rtf_BottomGift", type = RectTransform, },
	btn_Recharge = { path = "rtf_BottomGift/btn_Recharge", type = Button, event_name = "OnBtnRechargeClickedProxy"},

}
