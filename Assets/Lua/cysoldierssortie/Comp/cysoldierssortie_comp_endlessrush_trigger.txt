---@class cysoldierssortie_comp_endlessrush_trigger 凋落物对象
local elrush = bc_Class("cysoldierssortie_comp_endlessrush_trigger")
local cysoldierssortie_LayerName = cysoldierssortie_LayerName
local log = require "log"

elrush.DataSrc = nil

function elrush:__init(luaMono, referCol, luaData, ...)
    self.luaMono = luaMono
    self.DataSrc = {}
    referCol:Bind(self.DataSrc)

    self.gameObject = luaMono.gameObject
    self.transform = self.gameObject.transform

    self.DataSrc.ColListener:RegisterTriggerEnter(function(collider)
        self:OnTriggerEnter(collider)
    end)
end

function elrush:OnTriggerEnter(collider)
    local go = collider.gameObject
    if go.layer == cysoldierssortie_LayerName.Enemy then
        self.curLevel:EndlessRushEnemyDie(go)
    end
end

function elrush:Init(level)
    self.curLevel = level
end

--生命周期函数
function elrush:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true
end

function elrush:OnDisable()
end

function elrush:Start()
end

function elrush:OnDestroy()
end

return elrush
