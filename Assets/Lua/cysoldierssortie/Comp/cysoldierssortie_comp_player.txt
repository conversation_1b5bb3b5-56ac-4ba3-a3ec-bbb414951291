local cysoldierssortie_comp_player = bc_Class("cysoldierssortie_comp_player") --类名用小游戏名加后缀保证全局唯一
cysoldierssortie_comp_player.dataSrc = nil --gameobject 上 gameluabehaviour组件数据
local log = require("log")
local game_scheme = require "game_scheme"
local string = string
local tonumber = tonumber
local math = math
local table = table
local LayerMask           = CS.UnityEngine.LayerMask
local UnityEngine = CS.UnityEngine
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local ScreenHeight = UnityEngine.Screen.height
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_LevelMode = cysoldierssortie_LevelMode
local bc_Time = bc_Time
local minigame_mgr = require "minigame_mgr"
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local GWG = GWG
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local cysoldierssortie_PlaySfx = cysoldierssortie_PlaySfx
local cysoldierssortie_FxName = cysoldierssortie_FxName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_lua_util = require("cysoldierssortie_lua_util")
local cysoldierssortie_EventName = cysoldierssortie_EventName
local cysoldierssortie_player_action_state = cysoldierssortie_player_action_state
local SetTransformLocalPositionAndLocalRotation = ApiHelper.SetTransformLocalPositionAndLocalRotation
local SetTransformLocalScale = ApiHelper.SetTransformLocalScale
local cysoldierssortie_dotween_extern = require("cysoldierssortie_dotween_extern")
local isAsync = isAsync
local cysoldierssortie_CommonEffect = cysoldierssortie_CommonEffect
local cysoldierssortie_CampaignFR_TrialHeroID = cysoldierssortie_CampaignFR_TrialHeroID
local bc_CS_Quaternion = bc_CS_Quaternion
local DOTween = CS.DG.Tweening.DOTween
local DOVirtual = CS.DG.Tweening.DOVirtual
local Ease = CS.DG.Tweening.Ease

--在编辑器下只加载DefineList,不管其他部分
if ExecuteInEditorScript then
    return cysoldierssortie_comp_player
end

local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame

function cysoldierssortie_comp_player.__init(self, luaMono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
    self.cysoldierssortie_firstRecharge_Endless = cysoldierssortie_firstRecharge_Endless
    self.cysoldierssortie_firstRecharge_Normal = cysoldierssortie_firstRecharge_Normal
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_comp_player:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform

    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self.camMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)

end

function cysoldierssortie_comp_player:OnDisable()
    self:KillTween_ELRush()
    if self.eventMgr then
        self.eventMgr:UnRegisterEvt(self, cysoldierssortie_EventName.InputMgrOnDrag)

        self.eventMgr:UnRegisterEvt(self, cysoldierssortie_EventName.InputMgrOnBeginDrag)
        self.eventMgr:UnRegisterEvt(self, cysoldierssortie_EventName.InputMgrOnEndDrag)
    end
end

function cysoldierssortie_comp_player:Start()
    self._targetLayerMask =  LayerMask.GetMask("L15")
end

function cysoldierssortie_comp_player:Init(curLevel)

    self.curLevel = curLevel

    self.startPos = {x = 0,y = 0, z = 0}
    if curLevel and curLevel then
       local initPos = curLevel:GetPlayerInitPos()
        if initPos then
            self.startPos.x = initPos.x
            self.startPos.y = initPos.y
            self.startPos.z = initPos.z
            SetTransformLocalPositionAndLocalRotation(self.transform,initPos.x,initPos.y,initPos.z,0,0,0)
        else
            SetTransformLocalPositionAndLocalRotation(self.transform,0,0,0,0,0,0)
        end
    else
        SetTransformLocalPositionAndLocalRotation(self.transform,0,0,0,0,0,0)
    end
   
    
    self.eventMgr:RegisterEvt(self, cysoldierssortie_EventName.InputMgrOnDrag)

    self.eventMgr:RegisterEvt(self, cysoldierssortie_EventName.InputMgrOnBeginDrag)

    self.eventMgr:RegisterEvt(self, cysoldierssortie_EventName.InputMgrOnEndDrag)

    self.isPointerDown = false
    self.eventData = nil
    
    self._hero_lst = {}
    self._soldier_lst = {}
    self._soldier_pos_pool = {}
    
    --self.InitPos = bc_CS_Vector3(0, 0, -0.5)

    self.isMove = false--是否在移动
    self.isLastMove = false--上次移动状态
    self.curAniState = nil
    self.Init = true
    self.MoveX = 0
    self.isStopByBoss = false
    self.isInvincible = false  --是否无敌
    
    --当前关卡配置表
    self._level_config =  self.curLevel.MiniLevelCfg

    self._pointRoots = {}
    for i=1,5 do
        local pointRoot = self._heroRoot:Find("Point_"..i)
        self._pointRoots[#self._pointRoots+1]= pointRoot
    end
    
    self._targetPoints = {}
    for i=1,5 do
        local targetPoint = self._heroRoot:Find("TargetPoint_"..i)
        self._targetPoints[#self._targetPoints+1] = targetPoint
    end
    
    -- 最大生成士兵圈数
    local maxGenerateGroup =  cysoldierssortie_soldier_pos_config[self._level_config.LevelMode].maxGenerateGroup
    local pos_config = cysoldierssortie_soldier_pos_config[self._level_config.LevelMode]
    self._max_solider_num =  self:GetMaxSoldierNum(maxGenerateGroup,pos_config.baseRadius,pos_config.increasingRadius,
            pos_config.spacing)
    --初始化士兵
    local initialUnit = self._level_config.InitialUnit
    if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
        --- 【首充副本】塞薇亚拉—玩家本体
        self.FR_RealHeroUnitID = 9300004
        --- 【首充副本】塞薇亚拉—试用英雄ID 来自关卡配置
        if string.IsNullOrEmpty(initialUnit) then
            self.FR_TrialHeroUnitID = nil
        else
            self.FR_TrialHeroUnitID = tonumber(initialUnit)
        end
    else
        local array = string.split(initialUnit, ";")
        for i, v in ipairs(array) do
            local  array2=string.split(v, "#")
            local unit_id =  tonumber(array2[1])
            local unit_num = tonumber(array2[2])
            for a=1,unit_num do
                self:CreatePlayerCharacter(unit_id)
            end
        end
    end

    --初始化神兽
    local isUseSpecialSoldier = self.curLevel:IsUseSpecialSoldier()
    if not isUseSpecialSoldier and self.curLevel:IsOpenDrone() then --屏蔽士兵突围想系统
        local droneId = GWG.GWHomeMgr.droneData.GetDroneId()
        if  droneId and droneId>0 then
            local droneSkin = GWG.GWHomeMgr.droneData.GetCurAdroneSkin()
            if  not droneSkin or droneSkin==0 then
                local data_personalInfo = require "data_personalInfo"
                droneSkin = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.AnimalsID)
            end
            -- log.Error(droneSkin)
            local cfg = game_scheme:AccessoryAdorn_0(droneSkin)
            if cfg and cfg.modelID then
                -- log.Error(cfg.modelID)
                local unit_id =  self:GetUnitIDByHeroID(cfg.modelID,1)
                if unit_id then
                    --  log.Error(unit_id)
                    self:CreatePlayerDrone(unit_id)
                end
            end
        end
    end


    self.isPlayerWin = false
end

function cysoldierssortie_comp_player:GetEmptyHeroSlot()
    for i=1,#self._pointRoots do
        local slot =  self._pointRoots[i]
        if slot.childCount<2 then
            return i
        end
    end
    return nil
end

function cysoldierssortie_comp_player:GetUnitIDByHeroID(heroID,level)
    level=level or 1
    self._unitIDMapHeroID = self._unitIDMapHeroID or {}
    local levelKey=heroID.."@"..level
    local unitID = self._unitIDMapHeroID[levelKey]
    if not unitID then
        if self.cysoldierssortie_firstRecharge_Endless or self.cysoldierssortie_firstRecharge_Normal then
            if heroID == cysoldierssortie_CampaignFR_TrialHeroID then
                unitID = self.FR_RealHeroUnitID
                self._unitIDMapHeroID[levelKey] = unitID
                return unitID
            end
        end
        local nums =  game_scheme:MiniUnit_nums()
        for i = 0,nums-1 do
            local unit = game_scheme:MiniUnit(i)
            if unit.UnitType==4 or unit.UnitType==6 then-- 4—英雄
                if unit.ModelID and unit.ModelID>0 then
                    if not self._unitIDMapHeroID[unit.ModelID.."@"..unit.UnitLevel] then
                        self._unitIDMapHeroID[unit.ModelID.."@"..unit.UnitLevel] = unit.ID
                    end
                end
            end
        end
        unitID = self._unitIDMapHeroID[levelKey]
    end
    return unitID
end

--A B 交换
function cysoldierssortie_comp_player:SwitchPosition(parentA,parentB,characterA,characterB,indexA,indexB)
    local tmp_local_pos = characterB._localPos
    characterB:SetPosAndParent(characterA._localPos,parentA)
    characterA:SetPosAndParent(tmp_local_pos,parentB)
    
    local tmp_character = characterB
    self._soldier_lst[indexB] = characterA
    self._soldier_lst[indexA] = tmp_character
end
--创建神兽
function cysoldierssortie_comp_player:CreatePlayerDrone(unitId)

    local parent = self._droneRoot
    self._actor_instance_mgr = self._actor_instance_mgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    local localPos={x=0,y=0,z=0}
    self._playerDrone = self._actor_instance_mgr:CreateCharacter(unitId,localPos,parent,true,0, 0, GWG.GWHomeMgr.droneData.GetDroneAttribute(GWG.GWConst.enDroneProp.DRONE_PROP_ATTACK),self)

end
--unitId 单位id(士兵传nil)，hero英雄数据
function cysoldierssortie_comp_player:CreatePlayerCharacter(unitId, hero,slotIndex,playEffect)--CreateCharacter
    if self._soldier_lst  then
        if #self._soldier_lst >= (self._max_solider_num or 61) then
            return
        end
    end
    
    if not unitId then
        unitId= self.curLevel:GetUseSoldier(1)
        --新获得的士兵跟随等级
        if   self._soldier_lst and #self._soldier_lst > 0 then
            for i=1,#self._soldier_lst do
                if  self._soldier_lst[i]._level and self._soldier_lst[i]._unit_type==cysoldierssortie_unit_type.Soldier then
                    unitId = self._soldier_lst[i]._unitID
                    break
                end
            end
        end
    end
    
    local curHeroId=nil
    local hp=nil
    local atk=nil
    if hero then
        curHeroId = hero and hero.heroID
        hp = hero and hero.battleProp.hp
        atk = hero and hero.battleProp.attack
    end

    local localPos = nil
    local parent = nil
        parent = self.rfSoldierRoot.transform
        local nthSoldier = #self._soldier_lst
        local pos_config = cysoldierssortie_soldier_pos_config[self._level_config.LevelMode]
        if self._soldier_pos_pool and #self._soldier_pos_pool >0 then
            localPos = self._soldier_pos_pool[1].LocalPos
            parent = self._soldier_pos_pool[1].Parent and self._soldier_pos_pool[1].Parent or parent
            table.remove(self._soldier_pos_pool,1)
        else
            if self._level_config.LevelMode ==  cysoldierssortie_LevelMode.HeroRunnerMode or self._level_config.LevelMode == cysoldierssortie_LevelMode.HeroTowerDefenceMode then
                if nthSoldier < 5 then
                    localPos = {x=0,y=0,z=0}
                    slotIndex = slotIndex == nil and self:GetEmptyHeroSlot() or slotIndex
                    parent = self._pointRoots[slotIndex]
                else
                    nthSoldier =  nthSoldier - 5
                    localPos = self:GetSoldierPos(nthSoldier,
                            pos_config.baseRadius,pos_config.increasingRadius,
                            pos_config.spacing,pos_config.startAngle)
                end
            else
                localPos = self:GetSoldierPos(nthSoldier,
                        pos_config.baseRadius,pos_config.increasingRadius,
                        pos_config.spacing,pos_config.startAngle)
            end
        end
    
    self._actor_instance_mgr = self._actor_instance_mgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    local character = self._actor_instance_mgr:CreateCharacter(unitId,localPos,parent,true,curHeroId,hp,atk,self,playEffect)
    character.slotIndex=slotIndex
    character.heroData=hero
    local minigame_buff_mgr= require "minigame_buff_mgr"
    minigame_buff_mgr.AddBuffCfg(unitId,character)
    self._soldier_lst[#self._soldier_lst+1] = character
    if self._level_config.LevelMode == cysoldierssortie_LevelMode.HeroTowerDefenceMode or  self._level_config.LevelMode == cysoldierssortie_LevelMode.HeroRunnerMode then
        if character._unit_type == cysoldierssortie_unit_type.Hero and #self._soldier_lst > 5 then
            for i=1, 5 do
                local cur_character = self._soldier_lst[i]
                if cur_character then
                    if cur_character._unit_type == cysoldierssortie_unit_type.Soldier then
                        local parentA = cur_character.transform.parent
                        local parentB = self.rfSoldierRoot.transform
                        local characterA = cur_character
                        local characterB = character
                        local indexA = i
                        local indexB = #self._soldier_lst
                        --data.Parent = parentA
                        self:SwitchPosition(parentA,parentB,characterA,characterB,indexA,indexB)
                        break
                    end
                end
            end
        end
    end
    self:CreateAttackRange(unitId,character._attackRange)
    return character
end

--士兵死亡
-- dead_num 死亡数量 UndeadSoldiers>0 只扣40%血不直接死
function cysoldierssortie_comp_player:SoldierDead(dead_num,UndeadSoldiers)
    local remainingDeadNum = dead_num -- Keep track of how many soldiers still need to die
    for i = #self._soldier_lst, 1, -1 do
        local unit = self._soldier_lst[i]
        if unit._unit_type == cysoldierssortie_unit_type.Soldier then
            if UndeadSoldiers and UndeadSoldiers>0 then
                local takeDamagePointX, takeDamagePointY, takeDamagePointZ = GetTransformPositionXYZ(unit.transform)
                unit:BeHit(unit._hp*0.4,true,takeDamagePointX,takeDamagePointY,takeDamagePointZ)
            else
                unit:Dead() -- Kill the soldier
            end
            remainingDeadNum = remainingDeadNum - 1 -- Decrease the count of soldiers to kill
            if remainingDeadNum <= 0 then
                break
            end
        end
    end
end

function cysoldierssortie_comp_player:ChangeMoveSpeedBuff(speed)
    self.buffMoveSpeed = speed
end

--获取当前英雄列表
function cysoldierssortie_comp_player:GetCHeroList()
    local herList={}
    if   self._soldier_lst and #self._soldier_lst > 0 then
        for i=1,#self._soldier_lst do
            if  self._soldier_lst[i]._unit_type==cysoldierssortie_unit_type.Hero then
                table.insert(herList,self._soldier_lst[i])
            end
        end
    end
    -- 记录布阵时的英雄列表
    if self.cysoldierssortie_firstRecharge_Endless then
        self.oriHeroList = herList
    end
    return herList
end

function cysoldierssortie_comp_player:GetAllSoldierDps()
    if not self._soldier_lst then
        return 0
    end

    if not self._soldierDps or self._soldierDpsDirty then
        local soldierAttack = 0
        local soldierAttackSpeed = 0
        local soldierCriticalHit = 0
        for i = 1,#self._soldier_lst do
            local character = self._soldier_lst[i]
            if character._unit_type == cysoldierssortie_unit_type.Soldier or
                    character._unit_type == cysoldierssortie_unit_type.Soldier2 then
                soldierAttack = character._attack
                if character._weapons and #character._weapons > 0 then
                    local weapon = character._weapons[1]
                    soldierAttackSpeed = (1 / weapon._attackSpeed)
                    soldierCriticalHit = weapon._criticalHit / 10000
                end
                break
            end
        end
        self._soldierDps = soldierAttack * soldierAttackSpeed * (1+soldierCriticalHit)
        self._soldierDpsDirty = false
    end
  
    local allSoldierDpsNum = self._soldierDps *  (#self._soldier_lst)
    return allSoldierDpsNum
end

function cysoldierssortie_comp_player:GetSingleSoliderDps()
    return self._soldierDps
end

function cysoldierssortie_comp_player:CameraShake()

    if  self.DOShakePositionTween then
        self.DOShakePositionTween:Kill()
    end
    if  self.DOShakeRotationTween then
        self.DOShakeRotationTween:Kill()
    end
    
    self.DOShakePositionTween=   self.camMgr.mainCamParent:DOShakePosition(0.15,bc_CS_Vector3(0.05,0.05,0.05))
    self.DOShakeRotationTween=  self.camMgr.mainCamParent:DOShakeRotation(0.15,bc_CS_Vector3(0.05,0.05,0.05))
end

function cysoldierssortie_comp_player:GetDistance2D(pos1, pos2)
    local dx = pos2.x - pos1.x
    local dz = pos2.z - pos1.z
    return math.sqrt(dx * dx + dz * dz)
end

function cysoldierssortie_comp_player:GetDistance1D(pos1, pos2)
    local dz = pos2.z - pos1.z
    return math.abs(dz)
end

function cysoldierssortie_comp_player:SetCameraFollowData(data)
    self._cam_follow = data.follow
    self._camera_follow_threshold = data.follow_distance_threshold
end

local TowerDefenceCameraMoveSpeed = 3.4
local TowerDefenceCameraMoveState = 
{
    Idle = 1,
    Move = 2
}
function cysoldierssortie_comp_player:TowerDefenceCameraMove()
    local runnerMode = self.curLevel:IsRunnerMode()
    if runnerMode then
        return
    end
    if self.cysoldierssortie_firstRecharge_Endless and self.EL_CamToStart_Flag then
        self.EL_CamToStart_Flag = false
        self._initCam = true
        self:EndlessRushEnd(function()
            self.curLevel:ShowSceneItems()
            self.camMgr:CopyDepthBuffer()
        end)
        return
    end
    
    if self._level_config.LevelMode == cysoldierssortie_LevelMode.HeroTowerDefenceMode and not self._initCam then
        local camX,camY,camZ = GetTransformPositionXYZ(self.camMgr.mainCamParent)
        local followX,followY,followZ = GetTransformPositionXYZ(self.rfCameraFollow.transform)
        local dist =  cysoldierssortie_lua_util:GetDistance2D(camX,camZ,followX,followZ)
        if dist <= 0.5 then
            self._initCam = true
            SetTransformPositionXYZ(self.camMgr.mainCamParent,camX,camY,followZ)
            self.curLevel:ShowSceneItems()
            self.camMgr:CopyDepthBuffer()
            return
        end
        
        local xDir = followX - camX
        if xDir~=0 then
            xDir = xDir > 0 and 1 or -1
        end
        local speed =  bc_Time.deltaTime*TowerDefenceCameraMoveSpeed
        if (camX + (speed*xDir)) >= self._rightPosXLimit then
            local rz = math.lerp(camZ,followZ,speed)
            SetTransformPositionXYZ(self.camMgr.mainCamParent,camX,camY,rz)
            return
        elseif (camX + (speed*xDir)) <= self._leftPosXLimit then
            local rz = math.lerp(camZ,followZ,speed)
            SetTransformPositionXYZ(self.camMgr.mainCamParent,camX,camY,rz)
            return
        end
        
        local res = xpcall(function()
            ApiHelper.TransformLerpTransform(self.camMgr.mainCamParent,self.rfCameraFollow.transform,bc_Time.deltaTime*TowerDefenceCameraMoveSpeed,not self._cam_follow)
        end,debug.traceback)

        if not res then
            local rx = math.lerp(camX,followX, bc_Time.deltaTime*TowerDefenceCameraMoveSpeed)
            local rz = math.lerp(camZ,followZ,bc_Time.deltaTime*TowerDefenceCameraMoveSpeed)
            if not self._cam_follow then
                SetTransformPositionXYZ(self.camMgr.mainCamParent,camX,camY,rz)
            else
                SetTransformPositionXYZ(self.camMgr.mainCamParent,rx,camY,rz)
            end
        end
    else
        if not self._initCam then
            self.camMgr:CopyDepthBuffer()
            self._initCam = true
        end
        
        if not self._cam_follow then
            return
        end
        local camX,camY,camZ = GetTransformPositionXYZ(self.camMgr.mainCamParent)
        local followX,followY,followZ = GetTransformPositionXYZ(self.rfCameraFollow.transform)
        local xDir = followX - camX
        if xDir~=0 then
            xDir = xDir > 0 and 1 or -1
        end
        local speed =  bc_Time.deltaTime*TowerDefenceCameraMoveSpeed
        if (camX + (speed*xDir)) >= self._rightPosXLimit then
            self._towerDefenceCamState = TowerDefenceCameraMoveState.Idle
            SetTransformPositionXYZ(self.camMgr.mainCamParent,self._rightPosXLimit,camY,camZ)
            return
        elseif (camX + (speed*xDir)) <= self._leftPosXLimit then
            self._towerDefenceCamState = TowerDefenceCameraMoveState.Idle
            SetTransformPositionXYZ(self.camMgr.mainCamParent,self._leftPosXLimit,camY,camZ)
            return
        end
        
        local dist =  cysoldierssortie_lua_util:GetDistance2D(camX,camZ,followX,followZ)
        self._towerDefenceCamState = self._towerDefenceCamState or TowerDefenceCameraMoveState.Idle
        if (self._towerDefenceCamState == TowerDefenceCameraMoveState.Idle) and dist < self._camera_follow_threshold then
            return
        end
        if self._towerDefenceCamState == TowerDefenceCameraMoveState.Idle then
            self._towerDefenceCamState = TowerDefenceCameraMoveState.Move
        end
        if self._towerDefenceCamState == TowerDefenceCameraMoveState.Move then
            if dist <= 0.05 then
                SetTransformPositionXYZ(self.camMgr.mainCamParent,followX,followY,followZ)
                self._towerDefenceCamState = TowerDefenceCameraMoveState.Idle
                return
            end
            local res = xpcall(function()
                ApiHelper.TransformLerpTransform(self.camMgr.mainCamParent,self.rfCameraFollow.transform,speed,false)
            end,debug.traceback)
            
            if not res then
                local rx = math.lerp(camX,followX, speed)
                local rz = math.lerp(camZ,followZ,speed)
                SetTransformPositionXYZ(self.camMgr.mainCamParent,rx,camY,rz)
            end
        end
    end
end

function cysoldierssortie_comp_player:RunnerCameraMove()
    local runnerMode = self.curLevel:IsRunnerMode()
    if not runnerMode then
        return
    end
    
    local rX,rY,rZ = GetTransformPositionXYZ(self.rfCameraFollow.transform)
    local autoEnterFreeMove = self.curLevel:IsEnterAutoTowerDefence()
    if not self._cam_follow and not autoEnterFreeMove then
        rX = 0
    end
    SetTransformPositionXYZ(self.camMgr.mainCamParent,rX,rY,rZ)
    
    local isTrackDis = self.curLevel:LevelVictoryDistance()
    if isTrackDis then
        if not self._updateInfoCheck or self._updateInfoCheck <= bc_Time.time then
            self._updateInfoCheck = bc_Time.time + 0.5
            self.curLevel:UpdateDisVictoryState()
        end
    end
end

function cysoldierssortie_comp_player:LateUpdate()
    -- 无尽模式，冲锋。相机跟随
    if self.EL_RushFollow_Flag then
        local tarPos = self.transform.position + self.EL_FocusOff - self.EL_Rush_FocusDir * self.EL_Rush_FocusDis
        self.EL_CamParent.position = bc_CS_Vector3.Lerp(self.EL_CamParent.position, tarPos, bc_Time.deltaTime * 8)
    end

    if self.curLevel and self.curLevel.isStartGame then
        self:TowerDefenceCameraMove()
        self:RunnerCameraMove()
        self:ForwardMove()
        self:RefreshPos()
        self:MoveInertia()
    end
end

function cysoldierssortie_comp_player:GetTargetObjs(character)
    if not self.curLevel.isStartGame or not self._attack_ranges[character._unitID] then
        return {}
    end
    return self._attack_ranges[character._unitID].RangeEnemys
end

function cysoldierssortie_comp_player:GetTargetObjsByWeaponID(weaponID)
    if not self.curLevel.isStartGame or not self._attack_ranges[weaponID] then
        return {}
    end
    return self._attack_ranges[weaponID].RangeEnemys
end


function cysoldierssortie_comp_player:InputMgrOnBeginDrag(eventData)
    local dontAllowControl = self.curLevel:GetDontAllowControl()
    if dontAllowControl then
        return
    end
    
    self.beginDragScreenPos = eventData.position
    self.isPointerDown = true
    self.eventData = eventData
    self.curLevel:InputMgrOnBeginDrag(eventData)
    if self.curLevel.isStartGame then
        return
    end

    if self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierTowerDefenceMode or self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierRunnerMode then
        self.curLevel:SetStartGame()
    end
end


local MoveAcceleration = 0.07 --加速度
local moveSpeed = 1 --移动速度
local CheckInterval = 0.05 --惯性检测时间
function cysoldierssortie_comp_player:MoveInertia()
    if not self._inertialState and self._towerDefenceCamState == TowerDefenceCameraMoveState.Idle then
        return
    end
    if not self.isPointerDown then
        return
    end
    if not self._inputPointScreenPos then
        return
    end
    if not self._lastDirOffset then
        return
    end
    
    if self._inertiaMoveSpeed and self._inertiaMoveSpeed<=0 then
        self._actionState = cysoldierssortie_player_action_state.Stand
        self:ClearInertiaData()
        return
    end
    if not self._inertialTimer or bc_Time.time > self._inertialTimer  then
        self._inertialTimer = bc_Time.time + CheckInterval
        if self._lastFrameScreenPos and self._lastFrameScreenPos == self._inputPointScreenPos then
            if self._cam_follow then
                self._inertialState = true
                self._actionState = cysoldierssortie_player_action_state.Move 
            else
                self._actionState = cysoldierssortie_player_action_state.Stand
            end
        end
        self._lastFrameScreenPos = self._inputPointScreenPos
    end
    
    if self._inertialState and self._cam_follow then
        self._inertiaMoveSpeed = self._inertiaMoveSpeed or moveSpeed
        self._inertiaMoveSpeed = math.max(0, self._inertiaMoveSpeed - MoveAcceleration)
        self:MoveAction(self._lastDirOffset,self._inertiaMoveSpeed)
    end
end

function cysoldierssortie_comp_player:ClearInertiaData()
    if self._inertialState then
        self._inputPointScreenPos = nil
        self._lastDirOffset = nil
        self._lastFrameScreenPos = nil
        self._inertiaMoveSpeed = nil
        self._inertialState = false
    end
end

function cysoldierssortie_comp_player:GetActionState()
    return self._actionState
end

function cysoldierssortie_comp_player:InputMgrOnEndDrag(eventData)
    self.isPointerDown = false
    self._actionState = cysoldierssortie_player_action_state.Stand
    self:ClearInertiaData()
end

function cysoldierssortie_comp_player:InputMgrOnDrag(eventData)
    self:MoveNew2(eventData);
end

local ratio  = (1560/ScreenHeight)
local maxOffset = 25
function cysoldierssortie_comp_player:MoveNew2(eventData)
    if not self.isGameEnd and self.isPointerDown then
        local curScreenPos = eventData.position
        self._inputPointScreenPos = curScreenPos
        local xOffset = curScreenPos.x - self.beginDragScreenPos.x
        self.beginDragScreenPos = curScreenPos
        self._actionState = cysoldierssortie_player_action_state.Move
        xOffset = xOffset * ratio
        --[[if self._cam_follow then
            xOffset = math.clamp(xOffset,-maxOffset,maxOffset) 
        end]]--
        self._lastDirOffset = xOffset
        self:MoveAction(xOffset,moveSpeed)
        self:ClearInertiaData()
    end
end

function cysoldierssortie_comp_player:MoveAction(xOffset,speed)
    local dirX = xOffset*speed*bc_Time.deltaTime
    local validMove = self:ClampPlayerPosition(dirX)
    if validMove then
        ApiHelper.TransletTransformXYZ(self.transform,dirX,0,0)
    end
end

local direction = {}
local freeMoveSpeed = 11
function cysoldierssortie_comp_player:FreeMove(v)
    if v.magnitude ~= 0 then
        direction.x = v.x *bc_Time.deltaTime * freeMoveSpeed
        direction.y = 0
        direction.z = v.y *bc_Time.deltaTime * freeMoveSpeed
        
        local x,y,z = self:GetPositionXYZ()
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local isValidMoveX,isValidMovZ = levelMgr:IsValidFreeMove(x+ direction.x,z+direction.z)
        if not isValidMoveX then
            direction.x = 0
        end

        if not isValidMovZ then
            direction.z = 0
        end
        
        ApiHelper.TransletTransformXYZ(self.transform,direction.x,direction.y,direction.z)

    end
end

local sprintTime = 4 --冲刺总时间
local point2ZForward = 10 --转到规定轨道得距离
local endPointOffset  =  80 -- 最终要到达得距离
local sprintEffect = "art/effects/effects/effect_mini_chongci/prefabs/effect_mini_chongci.prefab"
function cysoldierssortie_comp_player:Sprint()
    local sprintModule = self.curLevel:GetCurMapSprintModule()
    if not sprintModule then
        return
    end
    
    local uiMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ui)
    if uiMgr and uiMgr._miniUIRoot then
        uiMgr._miniUIRoot:SetActive(false)
    end
    
    for i = 1,#self._soldier_lst do
        local character =  self._soldier_lst[i]
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:CreateEntity(sprintEffect,character._weaponRoot,function(effect)
                SetTransformLocalPositionAndLocalRotation(effect.transform,0,0,0,0,0,0)
            end)
        end
    end
    
    local startX,startY,startZ =  GetTransformPositionXYZ(self.transform)
    local point1 = bc_CS_Vector3(startX,startY,startZ)
    
    local point2 = bc_CS_Vector3(sprintModule.xStart,startY,startZ+point2ZForward)
    
    local point3 = bc_CS_Vector3(sprintModule.xStart,startY,startZ+endPointOffset)
    local pathPoints = {}
    pathPoints[1] = point1
    pathPoints[2] = point2
    pathPoints[3] = point3

    cysoldierssortie_dotween_extern.DoPath(self.transform , pathPoints , sprintTime)
end

--全部单位进入无限制索敌
function cysoldierssortie_comp_player:AllSoldierUnLimitDisLookAtTarget()
    if not self._soldier_lst or #self._soldier_lst<=0 then
        return
    end

    for i=1,#self._soldier_lst do
        local soldier = self._soldier_lst[i]
        if soldier then
            soldier:UnLimitRangeLookAtTarget()
        end
    end
end

function cysoldierssortie_comp_player:ForwardMove()
    local runnerMode = self.curLevel:IsRunnerMode()
    if not runnerMode then
        return
    end    
    
    local isEnterAutoTowerDefence =  self.curLevel:IsEnterAutoTowerDefence()
    --进入塔防模式
    if isEnterAutoTowerDefence then
        if not self._delay_open then
            minigame_mgr.ShowUiMiniGameInfo()
            self:AllSoldierUnLimitDisLookAtTarget()
            local dontAllowControl = self.curLevel:GetDontAllowControl()
            if not dontAllowControl then
                self.curLevel:SmoothToFreedomView(self.rfCameraFollow)
                local inputMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.input)
                inputMgr:SwitchTouchPanel(false)
                inputMgr:SwitchJoystick(true)
                inputMgr:RegisterJoystickMoveEvent(function(v)
                    self:FreeMove(v)
                end)
            end
            self._delay_open = true
        end
        return
    end
    
    local forwardSpeed
    if self.buffMoveSpeed and self.buffMoveSpeed ~= 0 then
        forwardSpeed = self.buffMoveSpeed
    else
        forwardSpeed = self.curLevel:GetLevelForwardSpeed()
    end
    ApiHelper.TransletTransformXYZ(self.transform,0,0,1*bc_Time.deltaTime*forwardSpeed)
end

function cysoldierssortie_comp_player:SetMapWidth(mapWidth)
    if not mapWidth then
        return
    end
    local extent =  mapWidth /2
    self._minX = -extent
    self._maxX = extent
end

function cysoldierssortie_comp_player:SetMapWidthRightLeft(leftWidth,rightWidth)
    if not leftWidth or not rightWidth then
        return
    end
    self._minX = -leftWidth
    self._maxX = rightWidth
end

function cysoldierssortie_comp_player:SetCamMovePosLimit(leftPosXLimit,rightPosXLimit)
    self._leftPosXLimit = leftPosXLimit or -4
    self._rightPosXLimit = rightPosXLimit or 4
end

function cysoldierssortie_comp_player:ClampPlayerPosition(dirX)
    local maxX= self._maxX or 4.5
    local minX= self._minX or -4.5
    local x,y,z = GetTransformPositionXYZ(self.transform)
    if (x + dirX) > maxX then
        return false
    elseif (x+dirX)<minX then
        return false
    end

    return true
end

function cysoldierssortie_comp_player:RecycleSoldierPos(localPos,parent)
    self._soldier_pos_pool = self._soldier_pos_pool or {}
    self._soldier_pos_pool[#self._soldier_pos_pool+1] =  {LocalPos = localPos,Parent = parent}
end

function cysoldierssortie_comp_player:GetMaxSoldierNum(maxGenerateGroup, baseRadius, IncreasingRadius,soldierSpacing)
    if not soldierSpacing then
        return
    end
    -- Base radius of the first circle
    baseRadius = baseRadius or 3
    -- Spacing between soldiers along the circle
    soldierSpacing = soldierSpacing

    IncreasingRadius = IncreasingRadius or 1
    

    local addNum = 0
    if self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierRunnerMode or self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierTowerDefenceMode then
        addNum = 1
    else
        addNum = 5
    end

    -- Calculate the circumference of a circle based on radius
    local function GetCircumference(radius)
        return 2 * math.pi * radius
    end

    -- Determine the radius of the current circle and the position on it
    local circleRadius = baseRadius
    local soldiersInCurrentCircle = 0
    local totalSoldiers = 0

    local index = 1
    while true do
        local spacing = soldierSpacing[index] or soldierSpacing[#soldierSpacing]
        -- Calculate the maximum number of soldiers that can fit on the current circle
        local circumference = GetCircumference(circleRadius)
        soldiersInCurrentCircle = math.floor(circumference / spacing)
        totalSoldiers = totalSoldiers + soldiersInCurrentCircle

        if index >= maxGenerateGroup then
            break
        else
            -- Move to the next circle
            circleRadius = circleRadius + IncreasingRadius
        end
        index = index + 1
    end

    totalSoldiers = totalSoldiers + addNum

    return totalSoldiers
end

function cysoldierssortie_comp_player:GetSoldierPos(nthSoldier, baseRadius, IncreasingRadius,soldierSpacing,startAngle)
    if not soldierSpacing then
        return
    end
    -- Base radius of the first circle
    baseRadius = baseRadius or 3
    -- Spacing between soldiers along the circle
    soldierSpacing = soldierSpacing 

    IncreasingRadius = IncreasingRadius or 1

    startAngle = startAngle or math.pi
    
    local center_offset = 0
    if self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierRunnerMode or self._level_config.LevelMode == cysoldierssortie_LevelMode.SoldierTowerDefenceMode then
        if nthSoldier == 0 then
            return {x=0,y=0,z=0}
        end
        center_offset = 1
    else
        center_offset = 0
    end
    
    -- Calculate the circumference of a circle based on radius
    local function GetCircumference(radius)
        return 2 * math.pi * radius
    end

    -- Determine the radius of the current circle and the position on it
    local circleRadius = baseRadius
    local soldiersInCurrentCircle = 0
    local totalSoldiers = 0
    
    local index = 1
    while true do
        local spacing = soldierSpacing[index] or soldierSpacing[#soldierSpacing]
        -- Calculate the maximum number of soldiers that can fit on the current circle
        local circumference = GetCircumference(circleRadius)
        soldiersInCurrentCircle = math.floor(circumference / spacing)
        totalSoldiers = totalSoldiers + soldiersInCurrentCircle

        if nthSoldier <= totalSoldiers then
            -- If the nth soldier is within this circle
            break
        else
            -- Move to the next circle
            circleRadius = circleRadius + IncreasingRadius
        end
        index = index + 1
    end

    -- Calculate the angle for the nth soldier on the circle
    local positionInCircle = nthSoldier - (totalSoldiers - soldiersInCurrentCircle)
    local angle = (positionInCircle - center_offset ) * (2 * math.pi / soldiersInCurrentCircle)
    angle = angle + startAngle

    -- Calculate the x and y coordinates based on the angle and radius
    local x = circleRadius * math.cos(angle)
    local y = circleRadius * math.sin(angle)

    return {x = x, y=0, z = y}
end


function cysoldierssortie_comp_player:GetSoldierPos2(nthSoldier, soldierSize, spacing)
    -- Default values for soldier size and spacing if not provided
    soldierSize = soldierSize or 1
    spacing = spacing or 0.5

    if nthSoldier == 0 then
        return {x = 0, y = 0, z = 0} -- Soldier 0 at the center
    end

    -- Determine which layer the soldier belongs to (1x1, 2x2, 3x3, etc.)
    local layer = math.ceil((math.sqrt(nthSoldier + 1) - 1) / 2)

    -- Total number of soldiers in all previous layers
    local soldiersInPreviousLayers = (2 * layer - 1) * (2 * layer - 1)

    -- Position within the current layer
    local positionInLayer = nthSoldier - soldiersInPreviousLayers

    -- Side length of the current layer (number of soldiers along one side)
    local sideLength = 2 * layer

    -- Calculate the spacing factor (size + spacing)
    local interval = soldierSize + spacing

    -- Calculate x and z positions based on position within the layer
    local x, z = 0, 0
    if positionInLayer < sideLength then
        -- Top side
        x = layer
        z = layer - positionInLayer - 1
    elseif positionInLayer < 2 * sideLength then
        -- Left side
        x = layer - (positionInLayer - sideLength) - 1
        z = -layer
    elseif positionInLayer < 3 * sideLength then
        -- Bottom side
        x = -layer
        z = -layer + (positionInLayer - 2 * sideLength) + 1
    else
        -- Right side
        x = -layer + (positionInLayer - 3 * sideLength) + 1
        z = layer
    end

    -- Scale positions based on interval
    x = x * interval
    z = z * interval

    return {x = x, y = 0, z = z}
end

--获得士兵奖励 num-为减少
function cysoldierssortie_comp_player:GetRewardSolider(num,UndeadSoldiers)
    -- 无尽模式不加小兵
    if self.cysoldierssortie_firstRecharge_Endless then
        return
    end
    
    local performanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PerformanceMgr)
    local levelUpNum = performanceMgr:GetLevelUpEffectNum()
    cysoldierssortie_PlaySfx(cysoldierssortie_FxName.obtained)
    if num >0 then
        for i = 1, num do
            if i <= levelUpNum then
                self:CreatePlayerCharacter(nil,nil,nil,true)
            else
                self:CreatePlayerCharacter(nil,nil,nil,false)
            end
        end
    elseif num<0 then
        local dead_soldier_num = -num
        self:SoldierDead(dead_soldier_num,UndeadSoldiers)
    end
end
--获得士兵升级奖励
function cysoldierssortie_comp_player:GetRewardSoliderLevelUp(level)
   -- log.Error("GetRewardSoliderLevelUp=11111111level="..level)
    local performanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PerformanceMgr)
    local levelUpNum = performanceMgr:GetLevelUpEffectNum()
    local counter = 1
    if level>0 and self._soldier_lst and #self._soldier_lst>0 then
        local size=#self._soldier_lst
        self._soldierDpsDirty = true
        for i, v in ipairs(self._soldier_lst) do
            if v._unit_type == cysoldierssortie_unit_type.Soldier then
            if size>20 then
                if i%2==0 then
                    if counter > levelUpNum then
                        v:LevelUp(level,false)
                    else
                        v:LevelUp(level,true)
                        counter= counter +1
                    end
                else
                    v:LevelUp(level,false)
                end
            else
                if counter > levelUpNum then
                    v:LevelUp(level,false)
                else
                    v:LevelUp(level,true)
                    counter= counter +1
                end
            end

                v:ResetCooldown()
            end
        end
    end
   --print("GetRewardSoliderLevelUp=2222222=="..index)
end
--获得英雄奖励
function cysoldierssortie_comp_player:GetRewardHero(heroId)
    self:CreatePlayerCharacter(heroId)
end

function cysoldierssortie_comp_player:GetRewardHeroUp(heroId)
    if not heroId or heroId==0 then
        return
    end
    if self._soldier_lst and #self._soldier_lst>0 then
        for i, v in ipairs(self._soldier_lst) do
            if v._heroId  and v._heroId==heroId then
                v:LevelUp(nil,true)
            end
        end
    end
end
--EventType事件类型（1—障碍物  2—加减门  3—道具）
--RewardType奖励类型（类型1—获得士兵  类型2—士兵升级 类型3—获得英雄）
--RewardParameters 奖励参数（类型1—士兵数量  类型2—士兵等级  类型3—获得英雄ID、MiniUnit表）
--UndeadSoldiers是否扣除士兵40%的血量不死
function cysoldierssortie_comp_player:GetReward(pos,EventType, RewardType,RewardParameters, RewardParameters2,UndeadSoldiers)

    if self.curLevel.isGameOver then
        return
    end
    if not RewardType then
        print("奖励配置错误")
        return
    end
    --if EventType==1 then
        if RewardType==1 then
            self:GetRewardSolider(RewardParameters,UndeadSoldiers)
        elseif RewardType==2 then
            self:GetRewardSoliderLevelUp(RewardParameters)
        elseif RewardType==3 then
            self:GetRewardHero(RewardParameters)
        elseif RewardType==4 then
            self:GetRewardHeroUp(RewardParameters)
        elseif RewardType==5 then
            self:GetRewardHeroAttribute(RewardParameters,RewardParameters2)
        end
   -- end
end
function cysoldierssortie_comp_player:GetRewardHeroAttribute(attributeV,attributeType)
    local minigame_buff_mgr= require "minigame_buff_mgr"
    minigame_buff_mgr.AddGlobalAttribute(attributeType,attributeV)
    --if self._soldier_lst and #self._soldier_lst>0 then
    --    for i, v in ipairs(self._soldier_lst) do
    --        if v._heroId  then
    --            --v:LevelUp(nil,true)
    --        end
    --    end
    --end
end
function cysoldierssortie_comp_player:OnHeroDead(hero)
    table.remove_value(self._soldier_lst,hero)
    
    if #self._soldier_lst==0 and self.curLevel.isStartGame then
        self:Dead();
    end
end

function cysoldierssortie_comp_player:CreateAttackRange(UnitID,attackRange)
    self._attack_ranges = self._attack_ranges or {}
    if self._attack_ranges[UnitID] then
       return 
    end
    if not self._rangeParent then
        self._rangeParent = self.transform:Find("AttackRange")
    end

    local function callback(range_go)
        local range_entity = cysoldierssortie_GetLuaComp(range_go)
        range_entity:SetAttackRange(attackRange)
        self._attack_ranges[UnitID] = range_entity
    end
    if isAsync then
        NeeGame.PoolObjectAsync(cysoldierssortie_PoolObjectName.AttackRangeEntity,callback,self._rangeParent)
    else
        local range_go =  NeeGame.PoolObject(cysoldierssortie_PoolObjectName.AttackRangeEntity, self._rangeParent)
        callback(range_go)
    end
end

--当敌人死亡时，移除范围内的敌人
function cysoldierssortie_comp_player:RemoveRangeEnemy(enemy)

    if not self._attack_ranges then
        return
    end
    
    for i, v in pairs(self._attack_ranges) do
        table.remove_value(v.RangeEnemys, enemy)
    end

end

--胜利
function cysoldierssortie_comp_player:PlayerWin()
    self.Boss=nil
    self.isPlayerWin = true

end
function cysoldierssortie_comp_player:Dead()
    if self.isGameEnd then
        return
    end
    self.curLevel:GameOver(false)
    self.isGameEnd = true;

end
function cysoldierssortie_comp_player:OnDestroy()
end

function cysoldierssortie_comp_player:RefreshPos()
    self._PX = nil
    self._PY = nil
    self._PZ = nil
end
function cysoldierssortie_comp_player:GetPositionXYZ()
    local runnerMode = self.curLevel:IsRunnerMode()
    if runnerMode then
        if not self._PX then
            local x,y,z = GetTransformPositionXYZ(self.transform)
            self._PX = x
            self._PY = y
            self._PZ = z
        end
    else
        self._PX = 0
        self._PY = 0
        self._PZ = 0
    end
    return self._PX,self._PY,self._PZ
end

function cysoldierssortie_comp_player:AddDynamicHpActor(character)
    self._dynamicAddHpActors = self._dynamicAddHpActors or {}
    self._dynamicAddHpActors[#self._dynamicAddHpActors+1] = character
end

function cysoldierssortie_comp_player:ReduceDynamicHp()
    if not self._dynamicAddHpActors or #self._dynamicAddHpActors<=0 then
        return
    end
    for i=1,#self._dynamicAddHpActors do
        local character = self._dynamicAddHpActors[i]
        if not character or character.isDelKey then
            self._dynamicAddHpActors[i] = nil
        else
            character:ReduceDynamicHp()
        end
    end
end

--- 无尽模式下，关卡结束后重置当前角色状态
function cysoldierssortie_comp_player:ResetStatus()
    if self._soldier_lst then
        local animStand = cysoldierssortie_hero_anim_set.Stand
        local tmpHeroList = {}
        for i, v in ipairs(self._soldier_lst) do
            v:ResetCooldown()
            v:PlayAnim(animStand)
            if v._unit_type == cysoldierssortie_unit_type.Hero then
                tmpHeroList[#tmpHeroList + 1] = v
                -- 恢复血量
                local offHp = v._hp - v._curHp
                v._curHp = v._hp
                self:PlayRecoverEffect(v.transform.position)
            end
        end
        -- 复活死亡的英雄
        if self.oriHeroList then
            for i, v in ipairs(self.oriHeroList) do
                local aliveFlag = false
                for _, tmpC in ipairs(tmpHeroList) do
                    if v == tmpC then
                        aliveFlag = true
                        break
                    end
                end
                -- 复活
                if not aliveFlag then
                    local slotParent = self._pointRoots[v.slotIndex]
                    self:PlayRecoverEffect(slotParent.transform.position)
                    self:CreatePlayerCharacter(v._unitID, v.heroData, v.slotIndex)
                end
            end
        end
    end
    if self._playerDrone then
        local atkComp = self._playerDrone:GetAttackComp()
        if atkComp then
            if atkComp._weapon_entitys and #atkComp._weapon_entitys > 0 then
                local weapon_entity = atkComp._weapon_entitys[1]
                weapon_entity:ResetStatus()
            end
        end
    end
end

-- function cysoldierssortie_comp_player:RandomDie()
--     if self._soldier_lst then
--         local tmpIndex = nil
--         for i, v in ipairs(self._soldier_lst) do
--             if v._unit_type == cysoldierssortie_unit_type.Hero then
--                 tmpIndex = i
--                 break
--             end
--         end
--         if tmpIndex then
--             self._soldier_lst[tmpIndex]:BeHit(9999999)
--         end
--     end
-- end

function cysoldierssortie_comp_player:PlayRecoverEffect(tarPos)
    -- FIXME 无尽模式下，英雄复活暂时用升级特效
    local effect_path = cysoldierssortie_CommonEffect.UpgradeEffect
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    local EffectParamCache = {}
    EffectParamCache.auto_release = true
    EffectParamCache.delay_release = 1
    EffectParamCache.effect_path = effect_path
    EffectParamCache.callBack = function(go)
        go.transform.position = tarPos
    end
    EffectParamCache.maxWeightLimit = true
    effect_mgr:CreateEffect(EffectParamCache)
end

-- 无尽模式下，起点在远方
function cysoldierssortie_comp_player:InitEndlessPos(offZ)
    SetTransformLocalPositionAndLocalRotation(self.transform, self.startPos.x, self.startPos.y, self.startPos.z + offZ, 0, 0, 0)
end

function cysoldierssortie_comp_player:EndlessRushStart(moveDistance,moveSpeed,callBack)
    if not self.cysoldierssortie_firstRecharge_Endless then
        return
    end
    minigame_mgr.ShowEndlessRunProcess(true)
    minigame_mgr.ShowEndlessSpeedline(true)
    local rushCollider = "cysoldierssortie/prefab/player/endlessrush.prefab"
    local rushFX = "art/effects/effects/effect_troopclash_charge_victory/prefabs/effect_troopclash_charge_victory.prefab"
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    poolMgr:CreateEntityAsync(rushFX, self.transform, function(effectGo)
        self.EL_RushEffectGo = effectGo
        local effect_trans = effectGo.transform
        SetTransformLocalPositionAndLocalRotation(effect_trans, 0, 2, 0.3, 0, 0, 0)
        SetTransformLocalScale(effect_trans, 1.5, 1, 1)
    end)
    poolMgr:CreateEntityAsync(rushCollider, self.transform, function(go)
        ---@type cysoldierssortie_comp_endlessrush_trigger
        self.EL_RushTrigger = cysoldierssortie_GetLuaComp(go)
        SetTransformLocalPositionAndLocalRotation(go.transform, 0, 0, -4, 0, 0, 0)
        SetTransformLocalScale(go.transform, 1, 1, 1)
        self.EL_RushTrigger:Init(self.curLevel)
    end)
    
    local animRun = cysoldierssortie_hero_anim_set.Run
    for i, v in ipairs(self._soldier_lst) do
        v:PlayAnim(animRun)
    end
    local moveDur = moveDistance / moveSpeed
    local cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    self.EL_CamParent = cam_mgr.mainCamParent
    self.EL_RushFollow_Flag = true
    local focusRot = bc_CS_Quaternion.Euler({ x = 50, y = 0, z = 0 })
    self.EL_FocusOff = { x = 0, y = 0, z = 3 }
    self.EL_Rush_FocusDir = focusRot * bc_CS_Vector3.forward
    self.EL_Rush_FocusDis = 20
    self.tween_ELRush = DOTween.Sequence()
    self.EL_OriCamRot = self.EL_CamParent.transform.rotation
    self.tween_ELRush:Append(DOVirtual.Float(0, 1, 0.8, function(value)
        self.EL_CamParent.rotation = bc_CS_Quaternion.Lerp(self.EL_OriCamRot, focusRot, value)
    end):SetEase(Ease.OutSine))
    self.tween_ELRush:Insert(0, self.transform:DOMove(self.startPos, moveDur):SetEase(Ease.Linear))
    self.tween_ELRush:InsertCallback(moveDur - 0.3, function()
        minigame_mgr.ShowEndlessSpeedline(false)
    end)
    self.tween_ELRush:OnComplete(function()
        minigame_mgr.ShowEndlessRunProcess(false)
        self.EL_RushFollow_Flag = false
        self.EL_CamToStart_Flag = true
        self:ELRushFxRecycle()
        self:ELRushTriggerRecycle()
        callBack()
    end)
    self.tween_ELRush:SetAutoKill(true)
end

function cysoldierssortie_comp_player:EndlessRushEnd(callBack)
    self.tween_ELRush = DOTween.Sequence()
    local oriCamRot = self.EL_CamParent.transform.rotation
    local tarPos = self.rfCameraFollow.transform.position
    local duration = 0.5
    local ease = Ease.OutSine
    self.tween_ELRush:Append(DOVirtual.Float(0, 1, duration, function(value)
        self.EL_CamParent.rotation = bc_CS_Quaternion.Lerp(oriCamRot, self.EL_OriCamRot, value)
    end):SetEase(ease))
    self.tween_ELRush:Insert(0, self.EL_CamParent:DOMove(tarPos, duration):SetEase(ease))
    self.tween_ELRush:OnComplete(callBack)
    self.tween_ELRush:SetAutoKill(true)
end

function cysoldierssortie_comp_player:KillTween_ELRush()
    if self.tween_ELRush then
        self.tween_ELRush:Kill()
        self.tween_ELRush = nil
    end
end

function cysoldierssortie_comp_player:ELRushFxRecycle()
    if self.EL_RushEffectGo then
        if isAsync then
            NeeGame.ReturnObjectAsync(self.EL_RushEffectGo)
        else
            NeeGame.ReturnObject(self.EL_RushEffectGo)
        end
        self.EL_RushEffectGo = nil
    end
end

function cysoldierssortie_comp_player:ELRushTriggerRecycle()
    if self.EL_RushTrigger then
        if isAsync then
            NeeGame.ReturnObjectAsync(self.EL_RushTrigger.gameObject)
        else
            NeeGame.ReturnObject(self.EL_RushTrigger.gameObject)
        end
        self.EL_RushTrigger = nil
    end
end

return cysoldierssortie_comp_player