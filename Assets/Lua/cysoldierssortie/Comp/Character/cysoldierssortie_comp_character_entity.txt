local cysoldierssortie_comp_character_entity = bc_Class("cysoldierssortie_comp_character_entity") --类名用小游戏名加后缀保证全局唯一
local gw_hero_mgr    = require "gw_hero_mgr"
local game_scheme 	        = require "game_scheme"
local Transform = CS.UnityEngine.Transform
local Vector3 = CS.UnityEngine.Vector3
local UnityEngine = CS.UnityEngine
local typeof = typeof
local Animator     = CS.UnityEngine.Animator
local Quaternion = CS.UnityEngine.Quaternion
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local math = math
local LayerMask           = CS.UnityEngine.LayerMask
local SkinnedMeshRenderer = CS.UnityEngine.SkinnedMeshRenderer
local MeshRenderer = CS.UnityEngine.MeshRenderer
local table = table
local AnimatorCullingMode = CS.UnityEngine.AnimatorCullingMode
local log = log
local tostring = tostring
local NavMeshAgent = CS.UnityEngine.AI.NavMeshAgent
local ObstacleAvoidanceType = CS.UnityEngine.AI.ObstacleAvoidanceType
local gw_sand_animator_helper = require "gw_sand_animator_helper"

local GCPerf = GCPerf
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetDestinationXYZ = ApiHelper.SetDestinationXYZ
local IsActiveNavAgent = ApiHelper.IsActiveNavAgent
local CSUpdateEvent = cysoldierssortie_EventName.CSUpdate
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_KillTimer = cysoldierssortie_KillTimer
local bc_IsNotNull = bc_IsNotNull
local bc_CS_Quaternion = bc_CS_Quaternion
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_unit_target_layer_str = cysoldierssortie_unit_target_layer_str
local xpcall = xpcall
local debug = debug
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local bc_Time = bc_Time
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local cysoldierssortie_CommonEffect = cysoldierssortie_CommonEffect
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local util = require "util"
local BoxCollider = CS.UnityEngine.BoxCollider
local SetLuaCompCache = SetLuaCompCache

local NewGpuAnimator = util.IsCSharpClass(CS.GPUAnimationBaker.Engine.ShaderIDs)
local cysoldierssortie_character_state = cysoldierssortie_character_state
local minigame_mgr = require "minigame_mgr"

local SetTransformLocalScale = ApiHelper.SetTransformLocalScale
local SetTransformLocalPositionAndLocalRotation = ApiHelper.SetTransformLocalPositionAndLocalRotation
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local IsEntityHybridUtility = util.IsCSharpClass(EntityHybridUtility)
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")
local cysoldierssortie_entity_loader_batch = require("cysoldierssortie_entity_loader_batch")
local Ease = CS.DG.Tweening.Ease
local SetParent = ApiHelper.SetParent

-- LOD系统常量定义
local LOD_DISTANCE_LEVEL_1 = 20  -- 20-40M：1秒30帧
local LOD_DISTANCE_LEVEL_2 = 40  -- 40-60M：1秒15帧
local LOD_DISTANCE_LEVEL_3 = 60 -- 60M以上：1秒1帧

local UpdateSpeedRate = 1
local LOD_UPDATE_INTERVAL_1 = 1/30
local LOD_UPDATE_INTERVAL_2 = 1/15 
local LOD_UPDATE_INTERVAL_3 = 1 
local UPDATE_INIT = false

function cysoldierssortie_comp_character_entity.__init(self,parent,character)
    if not UPDATE_INIT then
        local performanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PerformanceMgr)
        UpdateSpeedRate = performanceMgr:GetUpdateSpeedRate()
        LOD_UPDATE_INTERVAL_1 =  1/30 * UpdateSpeedRate
        LOD_UPDATE_INTERVAL_2 = 1/15 * UpdateSpeedRate
        LOD_UPDATE_INTERVAL_3 = 1 * UpdateSpeedRate
        UPDATE_INIT = true
    end
    if self.modelGo then
        self:ReleaseModel()
    end

    -- 初始化LOD相关变量
    self._lastUpdateTime = 0
    self._currentLODLevel = 1
    self._updateInterval = LOD_UPDATE_INTERVAL_1
    
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not bc_IsNotNull(self.gameObject) then
        if poolMgr then
            poolMgr:CreateEntity(cysoldierssortie_PoolObjectName.Hero,parent,
                    function(character_go)
                        self.transform = poolMgr:GetTransform(character_go)
                        self.gameObject = character_go
                        self.transform.localRotation = Quaternion.identity
                        self._rfNumPos = poolMgr:GetRfNumPos(character_go)
                        self._collider = poolMgr:GetCollider(character_go)
                        self._releaseSkillPoint = poolMgr:GetReleaseSkillPoint(character_go)
                        self._hpPoint = poolMgr:GetHpPoint(character_go)
                        self._modelRoot = poolMgr:GetModelRootGo(character_go)
                        SetLuaCompCache(character_go,self)
                    end)
        end
    else
        self.gameObject:SetActive(true)
        ApiHelper.SetParent(self.transform,parent)
        self.transform.localRotation = Quaternion.identity
    end

    self:SetCharacter(character)
    character._skillReleaseTrans = self._releaseSkillPoint.transform
    character.transform = self.transform
    SetTransformLocalPositionAndLocalRotation(self.transform,character._localPos.x,character._localPos.y,character._localPos.z,0,0,0)

    self.eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    self.eventMgr:RegisterEvt(self,CSUpdateEvent)
    self.cysoldierssortie_TroopClash = cysoldierssortie_TroopClash
    self.TroopClash_Define = TroopClash_Define
    self.cysoldierssortie_firstRecharge_Endless = cysoldierssortie_firstRecharge_Endless
    if self.cysoldierssortie_firstRecharge_Endless and poolMgr then
        self.poolParent = poolMgr.transform
    end
end

function cysoldierssortie_comp_character_entity:OnPopFormPool(parent,character)
    self.__init(self,parent,character)
end

function cysoldierssortie_comp_character_entity:OnPushIntoPool()
    self.__delete(self)
    -- 无尽模式下，节点生成的角色要放外部的池内，防止节点被消耗后影响角色对象池
    if self.cysoldierssortie_firstRecharge_Endless and self.poolParent then
        if SetParent ~= nil then
            SetParent(self.transform, self.poolParent)
        else
            self.transform:SetParent(self.poolParent)
        end
    end
end

function  cysoldierssortie_comp_character_entity.__delete(self)
    -- 清理LOD相关变量
    self._lastUpdateTime = nil
    self._currentLODLevel = nil
    self._updateInterval = nil
    
    if self._recycle_coroutine then
        cysoldierssortie_KillTimer(self._recycle_coroutine)
        self._recycle_coroutine = nil
    end
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(false)
    end
    self.eventMgr:UnRegisterEvt(self,CSUpdateEvent)
    self:KillTween_ELRushDie()
end
-- lua脚本正式开始

function cysoldierssortie_comp_character_entity:SetCharacter(character)
    self.character = character
    self._targetLayerMask =  LayerMask.GetMask(cysoldierssortie_unit_target_layer_str[self.character._unit_type])
    self:InitLayer()
    if bc_IsNotNull(self._releaseSkillPoint) then
        self._releaseSkillPoint.transform.localRotation = bc_CS_Quaternion.Euler(0,0,0)
    end
    self._pause_rotation = true
end

function cysoldierssortie_comp_character_entity:InitLayer()
    self._layer = cysoldierssortie_unit_layer[self.character._unit_type]
    self.gameObject.layer = self._layer
    if not self.character._player then
        self.gameObject.tag =  cysoldierssortie_TagName.Enemy_zombie
        self._cull = true
    else
        self.gameObject.tag = cysoldierssortie_TagName.Player
        self._cull = false
    end
end

local EffectParamCache = {}
function cysoldierssortie_comp_character_entity:PlayNewGetEffect()
    if self.character._unit_type == cysoldierssortie_unit_type.Soldier then
        local effect_path = cysoldierssortie_CommonEffect.UpgradeEffect
        local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
        EffectParamCache.auto_release = true
        EffectParamCache.delay_release = 1
        EffectParamCache.effect_path = effect_path
        EffectParamCache.callBack = function(go)
            go.transform.position = self.character.transform.position
        end
        EffectParamCache.maxWeightLimit = true
        effect_mgr:CreateEffect(EffectParamCache)
    end
end

local CenterCache = {}
function cysoldierssortie_comp_character_entity:SetRenderPropertiesFloat(property,value)
    if self._animator and self._gpu_anim then
        self._animator:SetRenderPropertiesFloat(property,value)
    end
end

function cysoldierssortie_comp_character_entity:SetMaterialFloat(id,value)
    if self._animator  and self._gpu_anim then
        self._animator:SetMaterialFloat(id,value)
    end
end

function cysoldierssortie_comp_character_entity:SetRenderPropertiesColor(property,value)
    if self._animator and self._gpu_anim  then
        self._animator:SetRenderPropertiesColor(property,value)
    end
end

function cysoldierssortie_comp_character_entity:SetBoundRelevance(bound_size)
    self.character:UpdateBoundSize(bound_size.z,bound_size.y)
    local maxHeight = bound_size.y
    local hpTrans =  self._hpPoint.transform
    local hp_pos = hpTrans.localPosition
    if self.character._unit_type == cysoldierssortie_unit_type.Hero then
        SetTransformLocalPositionAndLocalRotation(hpTrans,hp_pos.x,3,hp_pos.z,0,0,0)
    else
        SetTransformLocalPositionAndLocalRotation(hpTrans,hp_pos.x,maxHeight+0.5,hp_pos.z,0,0,0)
    end
    if self.cysoldierssortie_TroopClash then
        SetTransformLocalPositionAndLocalRotation(self._releaseSkillPoint.transform,0,math.min(self.TroopClash_Define.Params.MaxReleaseHeight, maxHeight * 0.3),0,0,0,0)
    else
        SetTransformLocalPositionAndLocalRotation(self._releaseSkillPoint.transform,0,maxHeight/3,0,0,0,0)
    end
    if self._collider then
        self._collider.size = bound_size
        CenterCache.x = 0
        CenterCache.y = self._collider.size.y * 0.5
        CenterCache.z = 0
        self._collider.center = CenterCache
    end
end

function cysoldierssortie_comp_character_entity:CreateModel()
    if self._modelRoot then
        SetTransformLocalPositionAndLocalRotation(self._modelRoot.transform,0,0,0,0,0,0)
    end
    if self.character._isDrone then
        return
    end
    local heroId = self.character._heroId
    local star = self.character._star
    local moudleId = gw_hero_mgr.ChangeHeroModel(heroId,star)
    local moduleCfg = game_scheme:Modul_0(moudleId)
    if not moduleCfg then
        log.Error("[Mini Game SoldierSsortie] UniId = "..tostring(self.character._unitID).." model path not found!!!!!!")
        return
    end
    local modelPath = moduleCfg.modelPath
    modelPath = modelPath:gsub("%.prefab","_simple.prefab")
    local pool_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local loadCallBack = function(gameObject)
        self.modelGo = gameObject
        local modelTrans = self.modelGo.transform
        self._animator = gameObject:GetComponent(typeof(Animator))
        if self._animator and not self._animator:IsNull() then
            self._animator.cullingMode = AnimatorCullingMode.CullCompletely;
            self._gpu_anim = false
        else
            self._animator = gw_sand_animator_helper.new()
            self._animator:AddAnimator(self.modelGo)
            self._gpu_anim = true
        end
        if not self._lastModelPath or self._lastModelPath~=modelPath or not self._lastScale or self._lastScale~=self.character._scale then
            local actorInstanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
            local bound_size =  actorInstanceMgr:CalActorBoundSize(modelPath,self.character._scale,gameObject)
            self:SetBoundRelevance(bound_size)
            if self._gpu_anim and (not self._lastModelPath or self._lastModelPath~=modelPath) then
                if NewGpuAnimator then
                    self._animator:SetLayer(self._layer)
                end
            end
        end
        
        SetTransformLocalPositionAndLocalRotation(modelTrans,0,0,0,0,0,0)
        SetTransformLocalScale(modelTrans,self.character._scale,self.character._scale,self.character._scale)
        

        self._modelRoot.layer = self._layer
        if not self.character._player then
            if self._cull or not self.character._view_actor then
               self.modelGo:SetActive(false)
            end
        end
        self.character:InitAnimState()
        self.character:SpawnHp()
        self._lastModelPath = modelPath
        self._lastScale = self.character._scale
    end
    
    local beforeLoadCallBackECS = function(rootGO)
        if not self._lastModelPath or self._lastModelPath~=modelPath or not self._lastScale or self._lastScale~=self.character._scale then
            local actorInstanceMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
            local bound_size =  actorInstanceMgr:CalActorBoundSize(modelPath,self.character._scale,rootGO)
            self:SetBoundRelevance(bound_size)
        end
    end
    
    local loadCallBackECS = function(resPath,entity,rootGO)
        self._entity = entity
        if self._requestMapID then
            self._requestMapID = nil
        end
        SetTransformLocalScale(self._modelRoot.transform,self.character._scale,self.character._scale,self.character._scale)
        EntityHybridUtility.AttachToTransform(entity,self._modelRoot.transform)
        self._animator = gw_sand_animator_helper.new()
        self._animator:SetEntity(resPath,entity)
        self._gpu_anim = true
        self._modelRoot.layer = self._layer
        if not self.character._player then
            if self._cull or not self.character._view_actor then
                EntityHybridUtility.SetEnable(self._entity,false)
            end
        end
        self.character:InitAnimState()
        self.character:SpawnHp()
        self._lastModelPath = modelPath
        self._lastScale = self.character._scale
    end
    if not entity_manager.URP22 or not cysoldierssortie_urp_ecs  then
        pool_mgr:CreateEntity(modelPath,self._modelRoot.transform,loadCallBack)
    else 
        self._requestMapID = pool_mgr:CreateEntity(modelPath,self._modelRoot.transform,loadCallBackECS,nil,nil,nil,true,beforeLoadCallBackECS,self._layer)
    end
end

function cysoldierssortie_comp_character_entity:CreateNavMeshAgent(isBuffCreate)
    local truckBuffFlag = false
    if not isBuffCreate then
        local minigame_buff_mgr= require "minigame_buff_mgr"
        if  minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.IgnoringTheTerrain,self.character) then
            return
        elseif  minigame_buff_mgr.IsBuff(minigame_buff_mgr.BuffType.Truck,self.character) then  --卡车buff
            -- 小兵大作战的卡车会自动转向
            if self.cysoldierssortie_TroopClash then
                truckBuffFlag = true
            else
                return
            end
        end
    end

    self._navMeshAgent = self.gameObject:GetComponent(typeof(NavMeshAgent))
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then 
        self._navMeshAgent = self.gameObject:AddComponent(typeof(NavMeshAgent))
    else
        self._navMeshAgent.enabled = true
    end
    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
    self._navMeshAgent.acceleration = 1000
    self._navMeshAgent.stoppingDistance = 0--self.character._attackRange/2
    self._navMeshAgent.angularSpeed = truckBuffFlag and self.TroopClash_Define.Params.TruckRotateSpeed or 360
    self._crowed = false
end

function cysoldierssortie_comp_character_entity:SwitchObstacleAvoidState(Open)
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end
    
    if Open then
        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
    else
        self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
    end
end

function cysoldierssortie_comp_character_entity:InitNavAgentData()
    if not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end

    if not self._navMeshAgent.isOnNavMesh  or not self._navMeshAgent.isActiveAndEnabled then
        return
    end

    self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
    if self._collider then
        local minSize = self._collider.size.z
        if minSize > self._collider.size.x then
            minSize = self._collider.size.x
        end
        self._navMeshAgent.radius = minSize*0.5
        self._navMeshAgent.height = self._collider.size.y
    else
        self._navMeshAgent.radius = 0.5
    end
    self._crowed = true
end

function cysoldierssortie_comp_character_entity:GetMinBoundSizeXZ()
    if self._collider then
        local minSize = self._collider.size.z
        if minSize > self._collider.size.x then
            minSize = self._collider.size.x
        end
        return minSize
    end
end

function cysoldierssortie_comp_character_entity:RemoveNavMeshAgent()
    if  not self._navMeshAgent or self._navMeshAgent:IsNull() then
        return
    end
    self._navMeshAgent.enabled = false
end

function cysoldierssortie_comp_character_entity:UpdateNavAgentProp(speed)
    if not self._navMeshAgent then
        return
    end
    
    if self:IsActiveNavAgent() then
        self._navMeshAgent.speed = speed
    end
end

function cysoldierssortie_comp_character_entity:IsActiveNavAgent()
    if not self._navMeshAgent then
        return false
    end
    if not self._isActiveNavAgent then
        self._isActiveNavAgent =  IsActiveNavAgent(self._navMeshAgent)
    end
    return self._isActiveNavAgent
end

function cysoldierssortie_comp_character_entity:SetDestinationXYZ(px, py, pz)
    SetDestinationXYZ(self._navMeshAgent, px, py, pz)
end

function cysoldierssortie_comp_character_entity:StopNav(stop)
    if not self._navMeshAgent  then
        return
    end

    if not self:IsActiveNavAgent() then
        return
    end
    self._navMeshAgent.isStopped = stop
    if self.cysoldierssortie_TroopClash and stop then
        self._navMeshAgent:ResetPath()
    end

    if self._crowed then
        if not stop  then
            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.LowQualityObstacleAvoidance
        else
            self._navMeshAgent.obstacleAvoidanceType = ObstacleAvoidanceType.NoObstacleAvoidance
        end
    end
end

function cysoldierssortie_comp_character_entity:EndlessRushDie(flyDir)
    self:Dead(true)
    if flyDir then
        local oriPos = self.transform.position
        local targetPos = oriPos + flyDir * math.lerp(5, 8, math.random())
        self.tween_ELRushDie = self.transform:DOMove(targetPos, 0.5):SetEase(Ease.OutSine)
        self.tween_ELRushDie:SetAutoKill(true)
    end
end

function cysoldierssortie_comp_character_entity:KillTween_ELRushDie()
    if self.tween_ELRushDie then
        self.tween_ELRushDie:Kill()
        self.tween_ELRushDie = nil
    end
end

function cysoldierssortie_comp_character_entity:Dead(anim,gameOverFlag)
    anim = anim==nil and true or anim
    self._isActiveNavAgent = nil
    
    if not anim then
        self:Release()
        return 
    end

    if self.character._player then
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        local res = xpcall(function()
            ApiHelper.SetParent(self.transform,poolMgr.transform)
        end,debug.traceback)

        if not res then
            self.transform:SetParent(poolMgr.transform)
        end
    end
    
    if self._animator  then
        self._animator:SetTrigger(cysoldierssortie_hero_anim_set.Dead)
    end

    if self._collider then
        self._collider.enabled = false
    end
    local recycleTime = 1.5
    if self.cysoldierssortie_TroopClash and gameOverFlag then
        recycleTime = recycleTime / self.TroopClash_Define.Params.GameOverScale
    end
    self._recycle_coroutine = cysoldierssortie_DelayCallOnce(recycleTime,function()
        if self._collider then
            self._collider.enabled = true
        end
        self:Release()
    end)
end

function cysoldierssortie_comp_character_entity:ReleaseModel()
    if bc_IsNotNull(self.modelGo)  then
        SetTransformLocalScale(self.modelGo.transform, 1, 1, 1) 
    end
    
    if entity_manager.URP22 and cysoldierssortie_urp_ecs then
        if self._entity then
            local instanceID = EntityHybridUtility.DestroyEntity(self._entity)
            if instanceID ~= 0 then
                local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
                poolMgr:DisposeEntity(instanceID)
            end
            self._entity = nil
        else
            if self._requestMapID then
                cysoldierssortie_entity_loader_batch.Dispose(self._requestMapID)
                self._requestMapID = nil
            end
        end
    else
        if self.modelGo then
            if bc_IsNotNull(self.modelGo) then
                NeeGame.ReturnObject(self.modelGo)
            end
            self.modelGo = nil
        end
    end
end

function cysoldierssortie_comp_character_entity:Clean()
    if self._animator and self._gpu_anim then
        self._animator:Dispose()
        self._animator = nil
    end
    
    self:ReleaseModel()
end

function cysoldierssortie_comp_character_entity:Release()
    self:KillTween_ELRushDie()
    SetTransformLocalPositionAndLocalRotation(self.transform,1000,0,1000,0,0,0)
    cysoldierssortie_DelayCallOnce(0,function()
        self:ReleaseModel()
        if self._animator and self._gpu_anim then
            self._animator:Dispose()
            self._animator = nil
        end
        --NeeGame.ReturnObject(self.gameObject)
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:ReleaseObj(self)
        end
    end)
end

-- 根据距离更新LOD等级
function cysoldierssortie_comp_character_entity:UpdateLODLevel()
    local newLODLevel = 1
    local newUpdateInterval = LOD_UPDATE_INTERVAL_1

    if not self.character._player then
        local distance = self.character._disToPlayerZ or 0
        if distance > LOD_DISTANCE_LEVEL_3 then
            -- 超过60M：1秒1帧
            newLODLevel = 3
            newUpdateInterval = LOD_UPDATE_INTERVAL_3
        elseif distance > LOD_DISTANCE_LEVEL_2 then
            -- 40-60M：1秒15帧
            newLODLevel = 2
            newUpdateInterval = LOD_UPDATE_INTERVAL_2
        elseif distance > LOD_DISTANCE_LEVEL_1 then
            -- 20-40M：1秒30帧
            newLODLevel = 1
            newUpdateInterval = LOD_UPDATE_INTERVAL_1
        else
            newLODLevel = 1
            newUpdateInterval = LOD_UPDATE_INTERVAL_1
        end
    end
    
    -- 如果LOD等级发生变化，重置更新时间
    if self._currentLODLevel ~= newLODLevel then
        self._currentLODLevel = newLODLevel
        self._updateInterval = newUpdateInterval
        -- LOD等级变化时立即更新一次，避免延迟
        self._lastUpdateTime = bc_Time.time - self._updateInterval
    else
        self._updateInterval = newUpdateInterval
    end
end

-- LOD系统：根据距离判断是否需要更新
function cysoldierssortie_comp_character_entity:ShouldUpdateByLOD()
    -- 玩家角色始终正常更新
    local currentTime = bc_Time.time

    -- 更新LOD等级和更新间隔
    self:UpdateLODLevel()

    -- 检查是否到了更新时间
    if currentTime - self._lastUpdateTime >= self._updateInterval then
        self._lastUpdateTime = currentTime
        return true
    end

    return false
end

--生命周期函数
function cysoldierssortie_comp_character_entity:CSUpdate()
    if self.cysoldierssortie_TroopClash then
        if self.character.troopClash_IgnoreBattleUpdate then
            return
        end
    end

    if not self._cull and self.character._view_actor then
        self:UpdateHp()
    end
    
    -- LOD性能优化：根据距离控制更新频率
    if not self:ShouldUpdateByLOD() then
        return
    end
    
    if minigame_mgr.GetIsStartGame() then
        self.character:UpdateAI()
    end
    self:DistanceCull()
    self:LookAtTarget()
    self:DisToPlayer()
end

function cysoldierssortie_comp_character_entity:UpdateHp()
    self.character:UpdateHp()
end

function cysoldierssortie_comp_character_entity:GetDistance1D(z1, z2)
    local dz = z1 - z2
    return math.abs(dz)
end

function cysoldierssortie_comp_character_entity:DisToPlayer()
    local currentPositionX,currentPositionY,currentPositionZ = GetTransformPositionXYZ(self.transform)

    if self.cysoldierssortie_TroopClash then
        local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
        local x, _, z = levelMgr.curLevel.playerLua:GetPositionXYZ()
        self.character._disToPlayerZ = self.TroopClash_Define.Func_GetDistance(currentPositionX, currentPositionZ, x, z)
    else
        local playerZ = 0
        self._currentPositionX = currentPositionX
        if not self.character._player and self._isRunnerMode == nil then
            local levelMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
            local curLevel = levelMgr.curLevel
            local isRunnerMode =  curLevel:IsRunnerMode()
            self._isRunnerMode = isRunnerMode
            if self._isRunnerMode then
                if not self._player then
                    self._player = curLevel.playerLua
                end
            end
        end
    
        if not self.character._player and self._isRunnerMode then
            local x,y,z=  self._player:GetPositionXYZ()
            playerZ = z
        end
        self.character._disToPlayerZ = self:GetDistance1D(currentPositionZ,playerZ)
    end

    local minigame_buff_mgr= require "minigame_buff_mgr"
    minigame_buff_mgr.CheckCondition(self.character,minigame_buff_mgr.ConditionType.Behind_Enemy,self.character._disToPlayerZ)
    minigame_buff_mgr.CheckFailureCondition(self.character,minigame_buff_mgr.FailureConditions.Behind_Enemy,self.character._disToPlayerZ)
end

function cysoldierssortie_comp_character_entity:LookAtTarget()
    if not self.character._enemyRange then
        return
    end
    local targetGo
    if not self._cull then
        targetGo = self.character:GetTargetGo()
    end

    if self.character._freezeRotate then
        return
    end
    
    if bc_IsNotNull(targetGo) then
        self._pause_rotation = false
    end
    
    if self._pause_rotation then
        return 
    end

    if self.character._unit_type ~= cysoldierssortie_unit_type.Hero and self.character._unit_type~=cysoldierssortie_unit_type.Soldier then
        return 
    end
    
    if bc_IsNotNull(targetGo)  then
        self:SmoothRotateTo(targetGo.position)
    else
        self:AlignToForward()
    end
end

function cysoldierssortie_comp_character_entity:DistanceCull(noNavFlag)
    local minigame_mgr = require "minigame_mgr"
    if not minigame_mgr.GetIsStartGame() then
        return
    end
    local cull_dis = 80
    local horizontal_cull_dis = 60
    local crowed_dis = 20
    
    if self.character then
        if self.character._player then
            return
        end
    else
        return
    end

    if not self._cam_mgr then
        self._cam_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.cam)
    end
    local cam_to_ground_farthest_dis,cam_horizontal_max_dis  = self._cam_mgr:GetFarthestDistance()
    if cam_to_ground_farthest_dis then
        cull_dis = cam_to_ground_farthest_dis
    end
    if cam_horizontal_max_dis then
        horizontal_cull_dis = cam_horizontal_max_dis
    end
    
    if GCPerf then
        --local character_posX, character_posY, character_posZ = GetTransformPositionXYZ(self.transform)
        local disToPlayerZ =  self.character._disToPlayerZ or 100
        if self._cull then
            if disToPlayerZ <= cull_dis and math.abs(self._currentPositionX or 100)<=horizontal_cull_dis then
                if self.character._view_actor then
                    if entity_manager.URP22 and cysoldierssortie_urp_ecs then
                        if self._entity then
                            EntityHybridUtility.SetEnable(self._entity,true)
                        end
                    else
                        if bc_IsNotNull(self.modelGo) then
                            self.modelGo:SetActive(true)
                        end
                    end

                    local minigame_buff_mgr= require "minigame_buff_mgr"
                    minigame_buff_mgr.ShowBossTips(self.character)
                end
                self._cull = false
                if not noNavFlag then
                    self.character:CreateNavAgent()
                end
            end
        end

        if not noNavFlag and not self._crowed and self._cull == false then
            if disToPlayerZ <= crowed_dis then
                self:InitNavAgentData()
            end
        end
    end
end

-- Aligns the character to face directly forward
function cysoldierssortie_comp_character_entity:AlignToForward()
    local lookDir = self.transform.forward
    local forwardRotation = Quaternion.LookRotation(lookDir)
    
    if bc_CS_Vector3.Angle(self._modelRoot.transform.forward,lookDir) <= 5 then
        self._modelRoot.transform.rotation = forwardRotation
        self._releaseSkillPoint.transform.rotation = forwardRotation
        self._pause_rotation = true
        return
    end
    
    --forwardRotation.y = 0 -- Only rotate on the horizontal plane
    self:ApplyRotation(forwardRotation)
end

-- Smoothly rotates the character to face a target position
function cysoldierssortie_comp_character_entity:SmoothRotateTo(targetPosition)
    local directionToTarget = targetPosition - self.transform.position
    directionToTarget.y = 0 -- Only rotate on the horizontal plane
    -- Create the desired rotation
    if directionToTarget.magnitude < 0.8 then
        return 
    end 
    
    local targetRotation = Quaternion.LookRotation(directionToTarget)

    if bc_CS_Vector3.Angle(self._modelRoot.transform.forward,directionToTarget) <= 5 then
        self._modelRoot.transform.rotation = targetRotation
        self._releaseSkillPoint.transform.rotation = targetRotation
        return
    end
    
    -- Smoothly rotate toward the target
    self:ApplyRotation(targetRotation)
end

-- Applies a rotation to the model root and skill release point
function cysoldierssortie_comp_character_entity:ApplyRotation(rotation)
    self._modelRoot.transform.rotation = Quaternion.Slerp(
            self._modelRoot.transform.rotation,
            rotation,
            bc_Time.deltaTime * 10
    )
    self._releaseSkillPoint.transform.rotation = self._modelRoot.transform.rotation
end

return cysoldierssortie_comp_character_entity