-- chat_content_list.txt -----------------------------------
-- author:  刘志远
-- date:    2019-10-24
-- ver:     1.0
-- desc:    聊天内容列表
----------------------------------------------------------------
local print = print
local require = require
local typeof = typeof
local ipairs = ipairs
local pairs = pairs
local table = table
local math = math
local string = string
local tostring = tostring
local os = os
local unpack = unpack
local tonumber = tonumber
local Edump = Edump
local cfg_util = require "cfg_util"
local time_util = require "time_util"
local const = require "const"
local net_sandbox_module = require "net_sandbox_module"
local sandbox_pb = require "sandbox_pb"
local gw_sand_camera_mgr = require "gw_sand_camera_mgr"
local gw_common_util = require "gw_common_util"
local util = require "util"

local lang_res_key = require "lang_res_key"
local http_inst = require "http_inst"
local mq_common_pb = require "mq_common_pb"
local ui_chat_main_new = require "ui_chat_main_new"
local flow_text = require "flow_text"
local lang = require "lang"
local item_data = require "item_data"
local iui_item_detail = require "iui_item_detail"
local card_sprite_asset = require "card_sprite_asset"
local game_scheme = require "game_scheme"
local player_mgr = require "player_mgr"
local face_item_new = require "face_item_new"
local event = require "event"
local class = require "class"
local ui_window_mgr = require "ui_window_mgr"
local chat_mgr_new = require "chat_mgr_new"
-- local explore_mgr = require "explore_mgr"
local common_new_pb = require "common_new_pb"
local block_cache = require "block_cache"
local ui_setting_data = require "ui_setting_data"
local ui_setting_cfg = require "ui_setting_cfg"
local json = require "dkjson"
local color_palette = require "color_palette"
local game_config = require "game_config"
local log = require("log")
local skep_mgr = require "skep_mgr"
local festival_activity_mgr = require "festival_activity_mgr"
-- local dimension_war_helper = require "dimension_war_helper"
local sociaty_icon = require "sociaty_icon"
local sociaty_title = require "sociaty_title"
local sand_ui_event_define = require "sand_ui_event_define"
local surprise_bag_define = require "surprise_bag_define"
local POP_OPEN_TYPE_ENUM = surprise_bag_define.POP_OPEN_TYPE_ENUM
local timer_mgr = require "timer_mgr"
local UnityEngine = CS.UnityEngine
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local UI = CS.UnityEngine.UI
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local mail_data_gw = require "mail_data_gw"
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey
local Utility = CS.War.Script.Utility
local Vector3 = CS.UnityEngine.Vector3
local Application = CS.UnityEngine.Application
local Time = CS.UnityEngine.Time
local LayoutElement = CS.UnityEngine.UI.LayoutElement
local LayoutElementType = typeof(LayoutElement)
local InlineText = CS.EmojiText.Taurus.InlineText
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local Gradient = CS.War.UI.Gradient
local Canvas = CS.UnityEngine.Canvas
local RectTransformType = typeof(CS.UnityEngine.RectTransform)
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Application = CS.UnityEngine.Application
local LinkImageTextEx = CS.War.UI.LinkImageTextEx
local Common_Util = CS.Common_Util.UIUtil
local chat_mgr_pro = require "chat_mgr_pro"
local shrinkTxt = require "shrinkTxt"
local battle_report_ext_helper = require "battle_report_ext_helper"
local string_util = require "string_util"
module("chat_content_list")

local TEXT_MAX_WIDTH = 400

local M = {}

local itemHeights = {}

local nextAddReset = false
local placeholderSizeDelta = { x = 0, y = 0 }

local openNew = false

local openNewDebug = false
local openNewConsole = true
local openNewInput = false

local function ComputeOpenNew()
    if openNewDebug then
        openNew = false
        return
    end
    openNew = openNewConsole and openNewInput
end

util.RegisterConsole("chat_debug", 0, function(st)
    openNewConsole = st == 0
    ComputeOpenNew()
end)

local tempVector3 = Vector3(0, 0, 0)

local chatHandle = {}


--@viewType 窗口类型：1普通频道聊天窗口 2私聊窗口
function M:Init(listGo, headLongClickCB, parentOrder, isOpenNew)
    self.curOrder = parentOrder and parentOrder + 5 or 1
    self.headLCB = headLongClickCB
    self.scrollRect = listGo:GetComponent(typeof(UI.ScrollRect))
    self.viewport = listGo:Find("Viewport"):GetComponent(typeof(UI.RectTransform))
    self.content = listGo:Find("Viewport/Content"):GetComponent(typeof(UI.RectTransform))
    self.contentLayout = listGo:Find("Viewport/Content"):GetComponent(typeof(UI.VerticalLayoutGroup))
    self.listItems = listGo:Find("Viewport/ListItems")
    self.template = listGo:Find("template")
    self.poorRoot = listGo:Find("Viewport/poorRoot")
    self.zhanwei = listGo:Find("Viewport/Content/zhanwei"):GetComponent(typeof(UI.RectTransform))
    self.spacing = self.contentLayout.spacing
    self.spriteAsset = card_sprite_asset.CreateSpriteAsset()
    self.title_sprite_asset = card_sprite_asset.CreateTitleAsset()
    self.leagueSpriteAsset = card_sprite_asset.CreateLeagueAsset()
    self.monsterIconAsset = card_sprite_asset.CreateGWSandMonsterIcon()
    self.trainAsset = card_sprite_asset.CreateAllianceTrainIconAsset()

    self.timerHandler = util.DelayCallOnce(0, function()
        if not self or not self:IsValid() then
            return
        end
        self:Update(deltaTime)
        return 0
    end)

    self:DisposeItem()
    if self.poorArr then
        for k, v in pairs(self.poorArr) do
            GameObject.Destroy(v.obj)
        end
    end
    if self.pool then
        for k, v in pairs(self.pool) do
            GameObject.Destroy(v.obj)
        end
    end
    if self.renderItemArr then
        for k, v in pairs(self.renderItemArr) do
            GameObject.Destroy(v.obj)
        end
    end
    self.poorArr = {}       --对象池
    self.renderItemArr = {} --当前显示的对象

    self.pool = {}

    self.startInx = -1
    self.endInx = -1

    self.gotoIndex = nil

    self.itemDatas = {}
    self.styleDatas = {}

    isOpenNew = true
    openNewInput = isOpenNew
    ComputeOpenNew()
end

function M:InitOrder(parentOrder)
    self.curOrder = parentOrder and parentOrder + 5 or 1
end

function M:IsValid()
    return self.scrollRect and not self.scrollRect:IsNull()
end
function M:Dispose()
    if self.timerHandler then
        -- self.timerHandler:Dispose()
        util.RemoveDelayCall(self.timeHandler)
        self.timeHandler = nil
    end
    self:ResetRefreshTime()
    if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
    end
    if self.title_sprite_asset then
        self.title_sprite_asset:Dispose()
        self.title_sprite_asset = nil
    end
    if self.leagueSpriteAsset then
        self.leagueSpriteAsset:Dispose()
        self.leagueSpriteAsset = nil
    end
    if self.monsterIconAsset then
        self.monsterIconAsset:Dispose()
        self.monsterIconAsset = nil
    end
    if self.trainAsset then
        self.trainAsset:Dispose()
        self.trainAsset = nil
    end
    self:DisposeItem()
end

local frameCount = 0

function M:Update()
    self:Update_list()
    self:Update_listPos()
    self:CheckRefreshUI()
end

function M:Update_listPos()
    if self.gotoIndex and self.gotoIndex >= 1 and self.gotoIndex <= #self.itemDatas then
        if self.content and not util.IsObjNull(self.content) then
            if self.gotoIndex < self.startInx then
                local oldPos = self.content.transform.localPosition
                self.content.transform.localPosition = { x = oldPos.x, y = oldPos.y - 60, z = 0 }
            elseif self.gotoIndex > self.endInx then
                local oldPos = self.content.transform.localPosition
                self.content.transform.localPosition = { x = oldPos.x, y = oldPos.y + 60, z = 0 }
            else
                local objTable = self.renderItemArr[self.gotoIndex - self.startInx + 1]
                if objTable then
                    local distance = objTable.obj.transform.position.y - self.scrollRect.transform.position.y
                    if math.abs(distance) < 0.2 or (distance > 0 and self.scrollRect.verticalNormalizedPosition >= 0.9999) or (distance < 0 and self.scrollRect.verticalNormalizedPosition <= 0.0001) then
                        self.gotoIndex = nil
                        self.scrollRect.enabled = true
                        return
                    end
                    local oldPos = self.content.transform.position
                    self.content.transform.position = { x = oldPos.x, y = oldPos.y - distance / 10, z = 0 }
                end
            end
        end
    elseif self.scrollRect and (not util.IsObjNull(self.scrollRect)) and self.scrollRect.enabled == false then
        self.scrollRect.enabled = true
    end
end

function M:Update_list()
    --优化重设数据时显示效果
    frameCount = frameCount + 1
    if frameCount < 2 then
        return
    end
    --safe check
    if (not self.scrollRect) or util.IsObjNull(self.scrollRect) then
        return
    end
    local topDis, botDis = self:GetBoundDistance()
    local safeDis = 50
    --如果renderItemArr为空，则为首次绘制
    if #self.renderItemArr == 0 then
        local preContentHeight = -self.spacing
        for i = #self.itemDatas, 1, -1 do
            self.startInx = i                                                               --标记渲染位置
            local objTable = self:PoorSpawn(self.styleDatas[i], self.itemDatas[i])

            table.insert(self.renderItemArr, 1, objTable)                               --放入渲染列表
            objTable.transform:SetSiblingIndex(0)
            self:OnItemRender(objTable, self.itemDatas[i], true)                                   --绘制数据
            local objRectTransf = objTable.transform
            UI.LayoutRebuilder.ForceRebuildLayoutImmediate(objRectTransf)                   --重新计算item高度
            preContentHeight = preContentHeight + objRectTransf.rect.size.y + self.spacing  --计算content高度

            if preContentHeight >= math.abs(topDis) then
                break
            end
        end

        if #self.renderItemArr ~= 0 then
            self.endInx = #self.itemDatas
            -- UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.content)
        end
        return
    end

    --是否要强制刷新布局
    local needRebuildLayout = false
    --检测上界
    if topDis > 0 and self.endInx < #self.itemDatas then
        local copyTopDis = topDis
        local removeIdx = 0
        for i = 1, #self.renderItemArr do
            local objTable = self.renderItemArr[i]
            local itemHeight = objTable.transform.rect.size.y
            if copyTopDis - itemHeight - self.spacing > safeDis then
                copyTopDis = copyTopDis - itemHeight - self.spacing
                --标记渲染位置
                self.startInx = self.startInx + 1
                --移除并回收item
                self:PoorRecyc(objTable)
                removeIdx = i
            else
                break
            end
        end
        local newItemArr = {}
        for i = 1, #self.renderItemArr do
            if i > removeIdx then
                table.insert(newItemArr, self.renderItemArr[i])
            end
        end
        self.renderItemArr = newItemArr

    elseif topDis < 0 then
        local copyTopDis = topDis
        for i = self.startInx - 1, 1, -1 do
            self.startInx = i                                                   --标记渲染位置
            local objTable = self:PoorSpawn(self.styleDatas[i], self.itemDatas[i])
            table.insert(self.renderItemArr, 1, objTable)                   --放入渲染列表
            local transform = objTable.obj.transform
            transform:SetSiblingIndex(0)                           --放入content显示
            self:OnItemRender(objTable, self.itemDatas[i])                       --绘制数据
            UI.LayoutRebuilder.ForceRebuildLayoutImmediate(transform)       --重新计算item高度

            copyTopDis = copyTopDis + transform.rect.size.y + self.spacing
            if copyTopDis > 0 then
                break
            end
        end
    end
    --检测下界
    local copyBotDis = botDis
    if botDis < 0 and self.startInx > 1 then
        local removeIdx = #self.renderItemArr + 1
        for i = #self.renderItemArr, 1, -1 do
            local objTable = self.renderItemArr[i]
            local itemHeight = objTable.transform.rect.size.y
            if copyBotDis + itemHeight + self.spacing < -safeDis then
                copyBotDis = copyBotDis + itemHeight + self.spacing
                ------print("++++++++++++++",itemHeight)
                itemHeights[self.endInx] = itemHeight

                --标记渲染位置
                self.endInx = self.endInx - 1
                --移除并回收item
                self:PoorRecyc(objTable)
                removeIdx = i
            else
                break
            end
        end
        local newItemArr = {}
        for i = 1, #self.renderItemArr do
            if i < removeIdx then
                table.insert(newItemArr, self.renderItemArr[i])
            end
        end
        self.renderItemArr = newItemArr

    elseif botDis > 0 then
        for i = self.endInx + 1, #self.itemDatas do
            needRebuildLayout = true
            self.endInx = i                                                     --标记渲染位置
            local objTable = self:PoorSpawn(self.styleDatas[i], self.itemDatas[i])
            table.insert(self.renderItemArr, objTable)                        --放入渲染列表
            objTable.transform:SetSiblingIndex(self.content.childCount - 2) --放入content显示
            self:OnItemRender(objTable, self.itemDatas[i])                       --绘制数据
            local objRectTransf = objTable.transform
            UI.LayoutRebuilder.ForceRebuildLayoutImmediate(objRectTransf)       --重新计算item高度
            local itemH = itemHeights[self.endInx] or 0

            copyBotDis = copyBotDis - itemH - self.spacing
            if copyBotDis < 0 then
                break
            end
        end
    end

    --调整位置
    local offset = copyBotDis - botDis
    if offset ~= 0 then
        local oldSize = self.zhanwei.sizeDelta
        local newH = oldSize.y + offset
        newH = newH < 0 and 0 or newH
        if math.abs(newH - placeholderSizeDelta.y) > 0.001 or math.abs(oldSize.x - placeholderSizeDelta.x) > 0.001 then
            placeholderSizeDelta = { x = oldSize.x, y = newH }
            self.zhanwei.sizeDelta = placeholderSizeDelta
        end
    end
    if needRebuildLayout == true then
        UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.content)
    end

    if self.isHelp then
        self.isHelp = false
        for i = 1, #self.renderItemArr do
            if self.startInx then
                --数数报错修复，撤回消息RemoveData和联盟互助消息SetUpdateHelp同时更新时，撤回消息并不在显示界面，列表刚好处于最下面，此时renderItemArr并不会少，self.itemDatas已经少了一个，渲染到最后一个会报空
                if self.itemDatas[self.startInx + i - 1] then
                    self:OnItemRender(self.renderItemArr[i], self.itemDatas[self.startInx + i - 1])
                end
            end
        end
        self.helpOther = false
    end

    if self.helpOther then
        for i = 1, #self.renderItemArr do
            if self.startInx then
                if self.itemDatas[self.startInx + i - 1] and self.itemDatas[self.startInx + i - 1].roleid == self.helpOtherID then
                    self:OnItemRender(self.renderItemArr[i], self.itemDatas[self.startInx + i - 1])
                    self.helpOther = false
                end
            end
        end
    end
end

--翻译当前展示中的聊天列表内容
function M:TranslationShowingChatItems()
    for i = 1, #self.renderItemArr do
        local objTable = self.renderItemArr[i]
        local data = self.itemDatas[i]
        --排除玩家自己的聊天内容style ~= 1
        if objTable and data and objTable.style ~= 1 and not objTable.isTranslated
                and objTable.data and objTable.data.translationFun then
            --判断该聊天项正在使用中
            if not util.IsObjNull(objTable.obj) and objTable.obj.transform.position.x ~= 10000 then
                TryAutoTranslation(objTable, data)
            end
        end
    end
end

function M:GetBoundDistance()
    --以viewport为坐标空间计算
    local vpHeight = self.viewport.rect.size.y
    local vpPivotY = self.viewport.pivot.y
    local vpBotBound = -vpHeight * vpPivotY - 100
    local vpTopBound = vpHeight * (1 - vpPivotY) + 100

    local conHeight = self.content.rect.size.y
    local conPivotY = self.content.pivot.y
    local conPositionY = self.content.transform.localPosition.y
    local conBotBound = conPositionY - conHeight * conPivotY + self.zhanwei.sizeDelta.y + self.spacing
    local conTopBound = conPositionY + conHeight * (1 - conPivotY)

    return conTopBound - vpTopBound, conBotBound - vpBotBound
end

--设置列表数据
function M:SetListData(itemDatas, styleDatas)
    if (not itemDatas) or (not styleDatas) or #itemDatas ~= #styleDatas then
        return
    end

    self:Reset()
    for i, v in pairs(itemDatas) do

       if chat_mgr_pro.CheckOldChatTargetType(v.sType) then
            table.insert(self.itemDatas, v)
        else
           local tempMsg={}
            tempMsg.sType=chat_mgr_pro.GetOldChatTargetTypeMsg()--临时提示消息
          table.insert(self.itemDatas, tempMsg)
        end


    end
    for i, v in pairs(styleDatas) do
        table.insert(self.styleDatas, v)
    end



    -- body
end

--增加一条数据
function M:AddData(data, style)
    table.insert(self.itemDatas, data)
    table.insert(self.styleDatas, style)
end

function M:RemoveData(msg)
    local removePos = 0
    for pos, data in ipairs(self.itemDatas) do
        if data.szChatID == msg.szChatID then
            removePos = pos
            break
        end
    end
    local msgData = self.itemDatas[removePos]
    if removePos > 0 and msgData then
        self:ResetIteam(msgData.szChatID)
        table.remove(self.itemDatas, removePos)
        table.remove(self.styleDatas, removePos)
    else
        print("kasug---removeData failed:", removePos, msg.szChatID)
    end
end

--跳转到相应位置
function M:MoveToIndex(idx)
    if idx and idx >= 1 and idx <= #self.itemDatas then
        self.scrollRect.enabled = false
        self.gotoIndex = idx
    end
end

--滚动到某条数据
function M:MoveToData(data)
    if data then
        for i, v in ipairs(self.itemDatas) do
            if v and v == data then
                self:MoveToIndex(i)
                return
            end
        end
    end
    flow_text.Add("Data is invalid!")
end

--滚动到某类型数据最后一条
function M:MoveToTypeLastData(enSpeakType)
    if enSpeakType then
        for i = #self.itemDatas, 1, -1 do
            if self.itemDatas[i].sType == enSpeakType then
                self:MoveToIndex(i)
                return
            end
        end
    end
    flow_text.Add("Data is invalid!")
end

--移到最底部
function M:MoveToBot()
    if #self.itemDatas - self.endInx > 3 then
        self:Reset()
    else
        if self.content and (not util.IsObjNull(self.content)) then
            local oldPos = self.content.transform.localPosition
            self.content.transform.localPosition = { x = oldPos, y = 0 }
        end
    end
end

function M:InitHead(objTable)
    objTable.data.nHead = objTable.obj:Find("head"):GetComponent(RectTransformType)
    objTable.data.GMHead = objTable.obj:Find("GMHead")
    if objTable.data.GMHead then
        objTable.data.GMHeadBtn = objTable.data.GMHead:GetComponent(typeof(UI.Button))
    end
    objTable.data.vipSpriteSwitch = objTable.obj:Find("head/VIP"):GetComponent(typeof(SpriteSwitcher))
    objTable.data.vipSprite = objTable.obj:Find("head/VIP"):GetComponent(typeof(UI.Image))

    local canvasCom = objTable.data.vipSprite.gameObject:GetComponent(typeof(Canvas))
    if canvasCom and not util.IsObjNull(canvasCom) then
        objTable.data.vipCanvas = canvasCom
    end

    objTable.data.vipText = objTable.obj:Find("head/VIP/Text"):GetComponent(typeof(UI.Text))
    local faceRoot = objTable.obj:Find("head/iconRoot"):GetComponent(RectTransformType)
    objTable.data.roleFace = face_item_new.CFaceItem():Init(faceRoot)
    objTable.data.roleFace:SetActorLvText(false)
end

function DisposeHead(chatContentList, objTable)
    if objTable.data.roleFace then
        objTable.data.roleFace:Dispose()
        objTable.data.roleFace = nil
    end
end

function RecoveryHead(chatContentList, objTable)
    if objTable.data.GMHeadBtn then
        objTable.data.GMHeadBtn.onClick:RemoveListener(OnGMHeadClick)
    end
end

function M:InitTitle(objTable)
    local childName = objTable.obj.name
    --print(childName)
    local scrollItem = objTable.obj:Find("title"):GetComponent(typeof(ScrollRectItem))
    --for i = 0, objTable.obj.transform:GetChild(1):GetChild(0).childCount-1 do
    --    local childName= objTable.obj.transform:GetChild(1):GetChild(0):GetChild(i).name
    --    print(childName)
    --end
    objTable.data.name = scrollItem:Get("name")
    objTable.data.roleLv = scrollItem:Get("roleLv") --等级
    objTable.data.icon_sex = scrollItem:Get("icon_sex") --性别
    objTable.data.icon_leaguePostion = scrollItem:Get("icon_leaguePostion") --联盟职位，图标展示
    objTable.data.txt_leaguePostion = scrollItem:Get("txt_leaguePostion") --联盟职位，字体显示
    objTable.data.icon_postionId = scrollItem:Get("icon_postionId")--地区职位


    --region 老代码（巅峰赛，时间）
    objTable.data.timeText = objTable.obj:Find("title/timeRoot/Time"):GetComponent(typeof(UI.Text))
    objTable.data.timeRoot = objTable.obj:Find("title/timeRoot"):GetComponent(typeof(RectTransformType))
    objTable.data.titleIcon = objTable.obj:Find("title/hLayout/Root/titleIcon"):GetComponent(typeof(UI.Image))
    objTable.data.titleInfo = objTable.obj:Find("title/hLayout/Root/titleIcon/info"):GetComponent(typeof(UI.Text))
    objTable.data.titleLabOutline = objTable.obj:Find("title/hLayout/Root/titleIcon/titleLab"):GetComponent(typeof(UI.Outline))
    objTable.data.titleLab = objTable.obj:Find("title/hLayout/Root/titleIcon/titleLab"):GetComponent(typeof(UI.Text))
    objTable.data.titleLabGradient = objTable.obj:Find("title/hLayout/Root/titleIcon/titleLab"):GetComponent(typeof(Gradient))
    --endregion
end

function M:InstantiateTranslateItem(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "fanyiLogo")
    objTable.data.fanyiLogo = instance
    objTable.data.inTranslation = instance:Find("bg/inTranslation").gameObject
    objTable.data.translated = instance:Find("bg/translated")
    objTable.data.translationFun = function()
        if objTable.data.onClickTranslation then
            objTable.data.onClickTranslation()
        end
    end
    objTable.data.pendant_fanyi.onClick:AddListener(objTable.data.translationFun)

end

function RegisterTranslateEvent(chatContentList, objTable)
    if objTable.data.OnTranslateEvent then
        return
    end
    objTable.data.OnTranslateEvent = function(event, sStr, tStr, langIso)
        if objTable.data.inTranslation.activeInHierarchy then
            if objTable.data.onTranslate then
                objTable.data.onTranslate(sStr, tStr, langIso)
            end
        end
    end
    event.Register(event.TRANSLATE_RSP, objTable.data.OnTranslateEvent)
end

function DisposeTranslateItem(chatContentList, objTable)
    if objTable.data.OnTranslateEvent then
        event.Unregister(event.TRANSLATE_RSP, objTable.data.OnTranslateEvent)
        objTable.data.OnTranslateEvent = nil
    end
end

function M:InstantiateShareGood(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "ShareGoodObj")

    objTable.data.shareGoodObj = instance.gameObject
    objTable.data.shareGoodRoot = instance:Find("Root")
    objTable.data.shareGoodBtn = instance:Find("Btn"):GetComponent(typeof(UI.Button))
    objTable.data.shareGoodName = instance:Find("Name"):GetComponent(typeof(UI.Text))

    local clickfun = function()
        if objTable.data.clickShareGood then
            objTable.data.clickShareGood()
        end
    end
    objTable.data.shareGoodBtn.onClick:AddListener(clickfun)
end

function M:InstantiateItem(itemStyle, parentTransform, itemName)
    local parentPath = itemStyle == 0 and "ortherSpeakItem" or "selfSpeakItem"
    return GameObject.Instantiate(self.listItems:Find(parentPath .. "/" .. itemName), parentTransform)
end

function M:InstantiateContentText(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "ContentBg")

    local data = objTable.data

    data.contentImage = instance:GetComponent(typeof(UI.Image))

    data.content = instance:Find("Content"):GetComponent(typeof(UI.Text))
    local ContentGM = instance:Find("ContentGM")
    if ContentGM then
        data.contentGM = instance:Find("ContentGM"):GetComponent(typeof(UI.Text))
        data.contentGMRect = instance:Find("ContentGM"):GetComponent(typeof(UI.RectTransform))
        data.linkImageTextEx = instance:Find("ContentGM"):GetComponent(typeof(LinkImageTextEx))
    end

    --data.inlineText = instance:Find("Content"):GetComponent(typeof(InlineText))
    data.contentRect = instance:Find("Content"):GetComponent(typeof(UI.RectTransform))
    data.content2 = instance:Find("Content2"):GetComponent(typeof(UI.Text))
    data.contentRect2 = instance:Find("Content2"):GetComponent(typeof(UI.RectTransform))
    data.contentLayout = instance:GetComponent(typeof(UI.VerticalLayoutGroup))
    data.line = instance:Find("line")
    data.lineRect = instance:Find("line").transform
    data.pendant_fanyi = instance:Find("pendants/liaotianfanyi"):GetComponent(typeof(UI.Button))
    data.contentBtn = instance:GetComponent(typeof(UI.Button))

    data.xianhuaEff = instance:Find("pendants/xianhua/xianhuaEffect")
    data.pendant_xianhua = instance:Find("pendants/xianhua"):GetComponent(typeof(UI.Button))
    data.pendant_luxiang = instance:Find("pendants/luxiang"):GetComponent(typeof(UI.Button))
    data.pendant_fightbtn = instance:Find("pendants/fightbtn"):GetComponent(typeof(UI.Button))
    data.pendant_fanyi = instance:Find("pendants/liaotianfanyi"):GetComponent(typeof(UI.Button))

    local clickFun = function()
        if data.conClickFun then
            data.conClickFun()
        end
    end
    local clickFightFun = function()
        if data.fightClickFun then
            data.fightClickFun()
        end
    end

    data.contentBtn.onClick:AddListener(clickFun)
    data.pendant_xianhua.onClick:AddListener(clickFun)
    data.pendant_luxiang.onClick:AddListener(clickFun)
    data.pendant_fightbtn.onClick:AddListener(clickFightFun)
end

--------货车详情分享-----------
function M:InstantiateCarriageDetail(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "CarriageDetailObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.carriageDetailObj = instance
    objTable.data.carriageBtn = instance:GetComponent(typeof(UI.Button))
    objTable.data.carriageNameTxt = item:Get("txt_name")
    objTable.data.carriageUnionTxt = item:Get("txt_union")
    objTable.data.carriageQuality = item:Get("ssw_quality")
    objTable.data.carriageContentText = item:Get("Text")
    objTable.data.carriageTitleText = item:Get("txt_des")
    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.carriageBtn.onClick:AddListener(clickJumpFun)
end

--------新增集结大作战分享----------
function M:InstantiateWarRally(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "WarRallyShareObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.warRallyShareObj = instance
    objTable.data.warRallyPlayerName = item:Get("playerName")
    objTable.data.warRallyAttackDes = item:Get("attackDes")
    objTable.data.warRallyRewardTran = item:Get("RewardTran")
    objTable.data.warRallyGoButton = item:Get("GoButton")
    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.warRallyGoButton.onClick:AddListener(clickJumpFun)
end
-------------------------------

-------新增沙盘定位分享类型------------
function M:InstantiateSandBoxPosition(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "SandboxPosObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.sandboxPosObj = instance
    objTable.data.sandboxPosBtn = instance:GetComponent(typeof(UI.Button))
    objTable.data.sandboxPosZoneTxt = item:Get("txt_zone")
    objTable.data.sandboxPosNameTxt = item:Get("txt_name")
    objTable.data.sandboxPosUnionTxt = item:Get("txt_union")
    objTable.data.sandboxPosQuality = item:Get("ssw_quality")
    objTable.data.sandboxPosDesTxt = item:Get("txt_des")
    objTable.data.sandboxPosContentText = item:Get("Text")
    objTable.data.TitleText = item:Get("TitleText")
    objTable.data.tavernTaskObj = item:Get("tavernTaskObj")
    objTable.data.starGroup = item:Get("starGroup")

    --[[    objTable.data.txtPos = item.transform:Find("TitleBG/TitleText"):GetComponent(typeof((UI.Text)))
        objTable.data.txtPos.text = lang.Get(6255)]]
    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.sandboxPosBtn.onClick:AddListener(clickJumpFun)
end
-------------------------------

-------新增灭火定位分享类型------------
function M:InstantiateFireCityPosition(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "CityFirePosObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.cityFirePosObj = instance
    objTable.data.cityFirePosBtn = instance:GetComponent(typeof(UI.Button))
    objTable.data.cityFirePosDesTxt = item:Get("txt_des")

    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.cityFirePosBtn.onClick:AddListener(clickJumpFun)
end
-------------------------------

------新增游荡怪集结分享----------
function M:InstantiateSandBoxMass(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "GatherTeamObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.gatherTeamObj = instance
    objTable.data.gatherTeamWaitTimeText = item:Get("WaitTimeText")
    objTable.data.gatherTeamMonsterIcon = item:Get("MonsterIcon")
    objTable.data.gatherTeamMonsterNameText = item:Get("MonsterNameText")
    objTable.data.gatherTeamPlayerList = item:Get("PlayerList")
    objTable.data.gatherTeamDistanceText = item:Get("DistanceText")
    objTable.data.gatherTeamDistanceBtn = item:Get("DistanceTextBtn")
    objTable.data.gatherTeamLeaderNameText = item:Get("LeaderNameText")
    objTable.data.gatherTeamGoButton = item:Get("GoButton")
    objTable.data.gatherTeamGoButtonText = item:Get("GoButtonText")
    objTable.data.gatherTeamGoButtonGray = item:Get("GoButtonGray")

    local clickJumpFun = function()
        --跳转游荡怪的方法
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end

    local clickAttackFun = function()
        --前往出征方法
        if objTable.data.clickAttackFun then
            objTable.data.clickAttackFun()
        end
    end
    objTable.data.gatherTeamGoButton.onClick:AddListener(clickAttackFun)
    objTable.data.gatherTeamDistanceBtn.onClick:AddListener(clickJumpFun)
    for i = 1, 4 do
        local itemName = string.format("FaceItem_%d", i)
        local parentTran = objTable.data.gatherTeamPlayerList:Find(itemName)
        local addBtn = parentTran:Find("AddIcon"):GetComponent(typeof(UI.Button))
        addBtn.onClick:AddListener(clickAttackFun)
    end

end
----------------------------------


------新增雷达挖掘宝藏分享----------
function M:InstantiateTreasurePosition(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "TreasureObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.treasureObj = instance
    objTable.data.treasureText = item:Get("strText")
    objTable.data.strBtn = item:Get("strBtn")

    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.strBtn.onClick:AddListener(clickJumpFun)
end
----------------------------------

------新增酒馆任务分享----------
function M:InstantiateTavernPosition(objTable, itemStyle)
    --TavernObj
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "TavernObj")
    objTable.data.tavernObj = instance

    objTable.data.strText = instance:Find("strText"):GetComponent(typeof(UI.Text))
    objTable.data.strBtn = instance:Find("strBtn"):GetComponent(typeof(UI.Button))

    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.strBtn.onClick:AddListener(clickJumpFun)
end
----------------------------------

---------挖掘完成----------
function M:InstantiateDigTreasure(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "DigTreasureObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    --objTable.data.digTreasureObj = instance

    objTable.data.digTreasureObj = instance
    objTable.data.msgText = item:Get("msgText")
    objTable.data.digBtn = instance:Find("digBtn"):GetComponent(typeof(UI.Button))

    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.digBtn.onClick:AddListener(clickJumpFun)
end
----------------------------------

-----------宝箱双倍奖励分享----------
function M:InstantiateDoubleReward(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "DoubleRewardObj")

    objTable.data.doubleRewardObj = instance
    objTable.data.Text_name = instance:Find("Text_name"):GetComponent(typeof(UI.Text))
    objTable.data.tips = instance:Find("tips"):GetComponent(typeof(UI.Text))
    objTable.data.doubleBtn = instance:Find("doubleBtn"):GetComponent(typeof(UI.Button))
    objTable.data.headParent = instance:Find("headParent"):GetComponent(typeof(UI.RectTransform))

    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.doubleBtn.onClick:AddListener(clickJumpFun)
end
----------------------------------
---
------新增联盟成就分享----------
function M:InstantiateAchievementShare(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AchievementObj")

    objTable.data.achievementObj = instance

    objTable.data.titleText = instance:Find("UnlockPanel/titleText"):GetComponent(typeof(UI.Text))
    objTable.data.slider = instance:Find("UnlockPanel/slider"):GetComponent(typeof(UI.Slider))
    objTable.data.Text_collect = instance:Find("UnlockPanel/slider/Text_collect"):GetComponent(typeof(UI.Text))
    objTable.data.UnlockPanel = instance:Find("UnlockPanel"):GetComponent(typeof(UI.RectTransform))
    objTable.data.desText = instance:Find("UnlockPanel/tipText"):GetComponent(typeof(UI.Text))

    objTable.data.NotUnlockPanel = instance:Find("NotUnlockPanel"):GetComponent(typeof(UI.RectTransform))
    objTable.data.titleText2 = instance:Find("NotUnlockPanel/titleText"):GetComponent(typeof(UI.Text))
    objTable.data.tipText = instance:Find("NotUnlockPanel/tipText"):GetComponent(typeof(UI.Text))
    objTable.data.AchievementBtn = instance:GetComponent(typeof(UI.Button))
    --没有点击事件

    --objTable.data.strBtn = instance:Find("strBtn"):GetComponent(typeof(UI.Button))
    --local clickJumpFun = function()
    --    if objTable.data.clickJumpFun then
    --        objTable.data.clickJumpFun()
    --    end
    --end
    --objTable.data.strBtn.onClick:AddListener(clickJumpFun)
end
----------------------------------

function M:InstantiateEquipmentObject(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "EquipObj")

    objTable.data.equipObj = instance
    objTable.data.equipName = instance:Find("equipName"):GetComponent(typeof(UI.Text))
    objTable.data.equipRoot = instance:Find("equipRoot").gameObject
    objTable.data.equipScore = instance:Find("score").gameObject
    objTable.data.equipScoreIcon = instance:Find("score/scoreIcon"):GetComponent(typeof(SpriteSwitcher))
    objTable.data.equipBtn = instance:Find("Btn"):GetComponent(typeof(UI.Button))
    objTable.data.starGroup = instance:Find("starGroup").gameObject

    local clickfun = function()
        if objTable.data.clickEquipment then
            objTable.data.clickEquipment()
        end
    end
    objTable.data.equipBtn.onClick:AddListener(clickfun)
end

function M:InstantiateEquipmentRecast(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "EquipRecastObj")

    objTable.data.equipRecastObj = instance
    objTable.data.equipRecastRoot = instance:Find("bg/equipRoot").gameObject
    objTable.data.equipRecastContent = instance:Find("bg/equipContent"):GetComponent(typeof(UI.Text))
    objTable.data.equipRecastBtn = instance:Find("Btn"):GetComponent(typeof(UI.Button))
    objTable.data.equipRecastScore = instance:Find("bg/score").gameObject
    objTable.data.equipRecastScoreIcon = instance:Find("bg/score/scoreIcon"):GetComponent(typeof(SpriteSwitcher))

    local clickfun = function()
        if objTable.data.clickEquipmentRecast then
            objTable.data.clickEquipmentRecast()
        end
    end
    objTable.data.equipRecastBtn.onClick:AddListener(clickfun)
end

function M:InstantiateLike(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "likeLogo")

    objTable.data.likeLogo = instance
end

function M:InstantiateLeagueRecruit(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "UnionObj")

    objTable.data.unionObj = instance
    objTable.data.unionIcon = instance:Find("icon"):GetComponent(typeof(UI.Image))
    local unionTitle = instance:Find("title")
    if unionTitle and not util.IsObjNull(unionTitle) then
        objTable.data.unionTitle = unionTitle:GetComponent(typeof(UI.RectTransform))
    end
    objTable.data.unionText = instance:Find("UnionText"):GetComponent(typeof(UI.Text))
    objTable.data.joinText = instance:Find("bg/joinText"):GetComponent(typeof(UI.Text))
    objTable.data.unionJoinBtn = instance:Find("JoinBtn"):GetComponent(typeof(UI.Button))
    objTable.data.unionJoin = instance:Find("Join"):GetComponent(typeof(UI.Button))

    local clickfun = function()
        if objTable.data.clickLeagueRecruit then
            objTable.data.clickLeagueRecruit()
        end
    end
    objTable.data.unionJoinBtn.onClick:AddListener(clickfun)
    objTable.data.unionJoin.onClick:AddListener(clickfun)
end

function M:InstantiateLeagueMutualHelp(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "HelpObj")

    objTable.data.helpObj = instance
    objTable.data.helpProgressImg = instance:Find("ProgressBg/ProgressFore"):GetComponent(typeof(UI.Image))
    objTable.data.helpBtn = instance:Find("HelpBtn"):GetComponent(typeof(UI.Button))
    objTable.data.helpProgressImg = instance:Find("ProgressBg/ProgressFore"):GetComponent(typeof(UI.RectTransform))
    objTable.data.helpProgressText = instance:Find("ProgressBg/Text"):GetComponent(typeof(UI.Text))
    objTable.data.helpTips = instance:Find("tips"):GetComponent(typeof(UI.Text))
    objTable.data.iconRoot = instance:Find("IconRoot"):GetComponent(typeof(UI.RectTransform))
    objTable.data.helpBtnGray = instance:Find("HelpBtn"):GetComponent(typeof(SpriteSwitcher))
    objTable.data.flowTrans = instance:Find("flowtext"):GetComponent(typeof(UI.RectTransform))

    local clickfun = function()
        if objTable.data.clickLeagueMutualHelp then
            objTable.data.clickLeagueMutualHelp()
        end
    end
    objTable.data.helpBtn.onClick:AddListener(clickfun)
end

function M:InstantiateChineseRedPacket(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "RedEnvelopeObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.redEnvelopeObj = instance
    objTable.data.btnRedEnvelope = item:Get("redEnvelopeObj")
    objTable.data.goRedEnvelopeBgGray = item:Get("bgGray")
    objTable.data.goRedEnvelopeArrowGray = item:Get("arrowGray")
    objTable.data.goRedEnvelopeBgSw = item:Get("bgSw")
    objTable.data.goRedEnvelopeArrowSw = item:Get("arrowSw")

    local clickfun = function()
        if objTable.data.clickChineseRedPacket then
            objTable.data.clickChineseRedPacket()
        end
    end
    objTable.data.btnRedEnvelope.onClick:AddListener(clickfun)
end

function M:InstantiateLabourDay(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "LaborDayObj")

    objTable.data.laborDayObj = instance
    objTable.data.laborDayText = instance:Find("bg/LaborText"):GetComponent(typeof(UI.Text))
    objTable.data.laborDayJoin = instance:Find("JoinBtn"):GetComponent(typeof(UI.Button))
    objTable.data.laborDayJoinTxt = instance:Find("JoinBtn/Text"):GetComponent(typeof(UI.Text))
    local clickfun = function()
        if objTable.data.clickLabourDay then
            objTable.data.clickLabourDay()
        end
    end
    objTable.data.laborDayJoin.onClick:AddListener(clickfun)
end

function M:InstantiatePeakGameSupport(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "PeakGameSupport")

    objTable.data.PeakGameSupport = instance
    objTable.data.PeakGameSupportRect = instance:Find("GameObject"):GetComponent(typeof(UI.RectTransform))
    objTable.data.PeakGameSupportTxt = instance:Find("GameObject/txt"):GetComponent(typeof(UI.Text))
end

function M:InstantiateDimensionWarAssemble(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "GatherObj")

    objTable.data.gatherObj = instance
    objTable.data.gatherBtn = instance:Find("root/content/Auto_BtnGoto"):GetComponent(typeof(UI.Button))
    objTable.data.gatherBuildingIcon = instance:Find("root/content/buildingIcon"):GetComponent(typeof(UI.Image))
    objTable.data.gatherBuildingName = instance:Find("root/content/buildingName"):GetComponent(typeof(UI.Text))
    local clickfun = function()
        if objTable.data.clickGather then
            objTable.data.clickGather()
        end
    end
    objTable.data.gatherBtn.onClick:AddListener(clickfun)
end

function M:InstantiateDimensionWarOccupy(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "OccupyObj")

    objTable.data.occupyObj = instance
    objTable.data.occupyName = instance:Find("content/name"):GetComponent(typeof(UI.Text))
    objTable.data.occupyFortName = instance:Find("content/fortname"):GetComponent(typeof(UI.Text))
end

function M:InstantiatePickTheRouteShareArchived(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "PickTheRouteObj")

    objTable.data.pickTheRouteObj = instance
    objTable.data.pickTheRouteLevel = instance:Find("level"):GetComponent(typeof(UI.Text))
    objTable.data.pickTheRouteStepCount = instance:Find("stepCount"):GetComponent(typeof(UI.Text))
    objTable.data.pickTheRouteBox1 = instance:Find("reward/box1").gameObject
    objTable.data.pickTheRouteBox2 = instance:Find("reward/box2").gameObject
    objTable.data.pickTheRouteBox3 = instance:Find("reward/box3").gameObject
    objTable.data.pickTheRouteReceived1 = instance:Find("reward/box1/received1").gameObject
    objTable.data.pickTheRouteReceived2 = instance:Find("reward/box2/received2").gameObject
    objTable.data.pickTheRouteReceived3 = instance:Find("reward/box3/received3").gameObject
    objTable.data.pickTheRouteViewBtn = instance:Find("viewBtn"):GetComponent(typeof(UI.Button))
    local clickfun = function()
        if objTable.data.clickPickTheRouteView then
            objTable.data.clickPickTheRouteView()
        end
    end
    objTable.data.pickTheRouteViewBtn.onClick:AddListener(clickfun)
end

function M:InstantiatePumnkinShareArchived(objTable, itemStyle)
    local parentTransform = objTable.transform
    objTable.data.pumnkinShareObj = self:InstantiateItem(itemStyle, parentTransform, "pumnkinShareObj")
    if not util.IsObjNull(objTable.data.pumnkinShareObj) then
        local instance = objTable.data.pumnkinShareObj.gameObject:GetComponent(typeof(ScrollRectItem))
        objTable.data.desc = instance:Get("desc")
        objTable.data.clickBtn = instance:Get("clickBtn")
        local clickFunc = function()
            if objTable.data.clickFunc then
                objTable.data.clickFunc()
            end
        end
        objTable.data.clickBtn.onClick:AddListener(clickFunc)
    end
end

function M:InstantiateMakeFoodInfoShareArchived(objTable, itemStyle)
    local parentTransform = objTable.transform
    local go = self:InstantiateItem(itemStyle, parentTransform, "MakeFoodsGameOrderObj")
    if not util.IsObjNull(go) then
        objTable.data.makeFoodsGameOrderObj = go.gameObject:GetComponent(typeof(ScrollRectItem))
    end
end

function M:InstantiateLikeCombo(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = GameObject.Instantiate(self.listItems:Find("comboItem"), parentTransform)

    objTable.data.comboNumText = instance:Find("comboInfo/Text"):GetComponent(typeof(UI.Text))
    objTable.data.headTemplate = instance:Find("head")
    objTable.data.headTableArr = {}  --{{headRoot = xx,faceItem = xx,dianzanGo = xx,omitGo = xx},{},...}
end

function M:InstantiateMakeFoodInfo(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "MakeFoodsGameOrderObj")

    objTable.data.makeFoodsGameOrderObj = instance:GetComponent(typeof(ScrollRectItem))
end

---生成联盟公告
function M:InstantiateAllianceNotice(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceNoticeObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.instance = instance

    objTable.data.pendant_fanyi = item:Get("btn_translate")
    objTable.data.btn_turnUrgent = item:Get("btn_turnUrgent")
    objTable.data.btn_close = item:Get("btn_close")
    objTable.data.line = item:Get("line")
    objTable.data.lineRect = item:Get("line"):GetComponent(RectTransformType)
    objTable.data.content2 = item:Get("txt_content_translate")
    objTable.data.content = item:Get("txt_content")
    objTable.data.contentRect = item:Get("txt_content"):GetComponent(RectTransformType)
    objTable.data.contentRect2 = item:Get("txt_content_translate"):GetComponent(RectTransformType)
    objTable.data.btn_delete = item:Get("btn_delete")
    objTable.data.btn_self = item:Get("btn_self")

    local btn_close = function()
        if objTable.data.clickClose then
            objTable.data.clickClose()
        end
    end
    objTable.data.btn_close.onClick:AddListener(btn_close)
    local btn_turnUrgent = function()
        if objTable.data.clickTurnUrgent then
            objTable.data.clickTurnUrgent()
        end
    end
    objTable.data.btn_turnUrgent.onClick:AddListener(btn_turnUrgent)

    local btn_delete = function()
        if objTable.data.clickDelete then
            objTable.data.clickDelete()
        end
    end
    objTable.data.btn_delete.onClick:AddListener(btn_delete)

    local btn_selfClick = function()
        if objTable.data.clickSelf then
            objTable.data.clickSelf()
        end
    end
    objTable.data.btn_self.onClick:AddListener(btn_selfClick)
end

--将翻译logo设置父物体
function SetTranslateLogo2Root_AllianceNotice(chatContentList, objTable)
    local item = objTable.data.instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.obj_translateLogoRoot = item:Get("obj_translateLogoRoot")
    Common_Util.SetParent(objTable.data.fanyiLogo, objTable.data.obj_translateLogoRoot.transform)
    Common_Util.SetLocalPos(objTable.data.fanyiLogo, 0, 0, 0)
end

--生成联盟频道公告样式
function M:InstantiateChannelAllianceNotice(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceChannelNoticeObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.instance = instance

    objTable.data.pendant_fanyi = item:Get("liaotianfanyi")
    objTable.data.line = item:Get("line")
    objTable.data.lineRect = item:Get("line"):GetComponent(RectTransformType)
    objTable.data.content2 = item:Get("Content2")
    objTable.data.content = item:Get("Content")
    objTable.data.contentRect = item:Get("Content"):GetComponent(RectTransformType)
    objTable.data.contentRect2 = item:Get("Content2"):GetComponent(RectTransformType)
end

function M:InstantiateOccupyNeutralCity(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "NCOccupiedObj")
    if not util.IsObjNull(instance) then
        objTable.data.ncOccupiedItem = instance:GetComponent(typeof(ScrollRectItem))
        objTable.data.cityText = objTable.data.ncOccupiedItem:Get("cityText")
        objTable.data.cityRImg = objTable.data.ncOccupiedItem:Get("cityRImg")
        objTable.data.cityIcon = objTable.data.ncOccupiedItem:Get("cityIcon")
        objTable.data.cityBtn = objTable.data.ncOccupiedItem:Get("cityBtn")
    end
end

function M:InstantiateAbandonNeutralCity(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(0, parentTransform, "SystemInfo")
    objTable.data.systemText = instance:Find("Text"):GetComponent(typeof(UI.Text))
end

--------火车分享-----------
function M:InstantiateAllianceTrainShare(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceTrainShareObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.trainShareObj = instance
    objTable.data.jumpBtn = instance:GetComponent(typeof(UI.Button))
    objTable.data.progress = item:Get("progress")
    objTable.data.Icon = item:Get("Icon")
    objTable.data.avatar = item:Get("avatar")
    objTable.data.trainName = item:Get("name")
    objTable.data.level = item:Get("level")
    objTable.data.power = item:Get("power")
    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.jumpBtn.onClick:AddListener(clickJumpFun)
end
--------火车详情推送-----------
function M:InstantiateAllianceTrainDetail(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceTrainDetailObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.trainDetailObj = instance
    objTable.data.jumpBtn = instance:GetComponent(typeof(UI.Button))
    objTable.data.Icon = item:Get("Icon")
    objTable.data.title = item:Get("title")
    objTable.data.avatar = item:Get("avatar")
    objTable.data.trainName = item:Get("name")
    objTable.data.level = item:Get("level")
    objTable.data.power = item:Get("power")
    local clickJumpFun = function()
        if objTable.data.clickJumpFun then
            objTable.data.clickJumpFun()
        end
    end
    objTable.data.jumpBtn.onClick:AddListener(clickJumpFun)
end
--------火车掠夺推送-----------
function M:InstantiateAllianceTrainBattle(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceTrainBattleObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.trainBattleObj = instance
    objTable.data.zone = item:Get("zone")
    objTable.data.union = item:Get("union")
    objTable.data.trainName = item:Get("name")
    objTable.data.lv = item:Get("lv")
    objTable.data.power = item:Get("power")
    objTable.data.face = item:Get("face")
    objTable.data.reward = item:Get("reward")
    objTable.data.trainImage = item:Get("trainImage")
end

--------藏宝图碎片分享-----------
function M:InstantiateTavernTreasure(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "TavernTreasureObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.trainShareObj = instance
    objTable.data.getNode = item:Get("getNode")
    objTable.data.loseNode = item:Get("loseNode")
    objTable.data.getCount = item:Get("getCount")
    objTable.data.loseCount = item:Get("loseCount")
    objTable.data.changeBtn = item:Get("changeBtn")
    objTable.data.jumpBtn = objTable.data.changeBtn.gameObject:GetComponent(typeof(UI.Button))
    local clickExchangeFun = function()
        if objTable.data.clickExchangeFun then
            objTable.data.clickExchangeFun()
        end
    end
    objTable.data.jumpBtn.onClick:AddListener(clickExchangeFun)
end

--惊喜盒分享
function M:InstantiateSurpriseBag(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "surpriseBagObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.surpriseBagObj = instance
    objTable.data.titleText = item:Get("titleText")
    objTable.data.Text_collect = item:Get("Text_collect")
    objTable.data.txtLike = item:Get("txtLike")
    objTable.data.hasLikeText = item:Get("hasLikeText")
    objTable.data.mask = item:Get("mask")
    objTable.data.boxIcon = item:Get("boxIcon")

    objTable.data.btnLike = item:Get("btnLike")
    local clickLikeBtnFun = function()
        if objTable.data.clickLikeBtnFun then
            objTable.data.clickLikeBtnFun()
        end
    end
    objTable.data.btnLike.onClick:AddListener(clickLikeBtnFun)

    objTable.data.surpriseBagBtn = item:Get("surpriseBagBtn")
    local clickSurpriseBagBtn = function()
        if objTable.data.clickSurpriseBagBtn then
            objTable.data.clickSurpriseBagBtn()
        end
    end
    objTable.data.surpriseBagBtn.onClick:AddListener(clickSurpriseBagBtn)
end

function DisposeSurpriseBagUI(chatContentList, objTable)
    --if objTable.data.btnLike then
    --    objTable.data.btnLike.onClick:RemoveAllListeners()
    --end
    --if objTable.data.surpriseBagBtn then
    --    objTable.data.surpriseBagBtn.onClick:RemoveAllListeners()
    --end
    if objTable.data.updateEvent then
        event.Unregister(surprise_bag_define.CHAT_VIEW_UPDATE, objTable.data.updateEvent)
    end
end

--惊喜盒手气最佳
function M:InstantiateSurpriseBagLuckly(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "surpriseBagLucklyObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.surpriseBagLucklyObj = instance
    objTable.data.Text_name = item:Get("Text_name")
    objTable.data.tips = item:Get("tips")
    objTable.data.headParent = item:Get("headParent")
    objTable.data.clickBtn = item:Get("clickBtn")
    local clickBtnFun = function()
        if objTable.data.clickBtnFun then
            objTable.data.clickBtnFun()
        end
    end
    objTable.data.clickBtn.onClick:AddListener(clickBtnFun)
end
--新聊天消息提示
function M:InstantiateNotStyleObj(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "NotStyleObj")
    objTable.data.laborDayText = instance:Find("bg/LaborText"):GetComponent(typeof(UI.Text))
end
--联盟邀请
function M:InstantiateAllianceShare(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "AllianceShareObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.allianceShareObj = instance
    objTable.data.flagIcon = item:Get("flagIcon")
    objTable.data.allianceName = item:Get("allianceName")
    objTable.data.num = item:Get("num")
    objTable.data.lang = item:Get("lang")
    objTable.data.power = item:Get("power")
    objTable.data.lvLimit = item:Get("lvLimit")
    objTable.data.ceLimit = item:Get("ceLimit")
    objTable.data.btnJoin = item:Get("btnJoin")
    objTable.data.btnApply = item:Get("btnApply")
    objTable.data.applyed = item:Get("applyed")
    local clickJoinFun = function()
        if objTable.data.clickJoinFun then
            objTable.data.clickJoinFun()
        end
    end
    objTable.data.btnJoin.onClick:AddListener(clickJoinFun)
    objTable.data.btnApply.onClick:AddListener(clickJoinFun)
end


--灭火活动
function M:InstantiateGetLikeObj(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "GetLikeObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.getLikeObj = instance
    objTable.data.tipText = item:Get("tipText")
    objTable.data.Heart = item:Get("Heart")

end



function DisposeAllianceTrainUI(chatContentList, objTable)
    if objTable.data.LeaderAvatar then
        objTable.data.LeaderAvatar:Dispose()
        objTable.data.LeaderAvatar = nil
    end
    if objTable.data.goodsItemList then
        for index, value in ipairs(objTable.data.goodsItemList) do
            value:Dispose()
        end
        objTable.data.goodsItemList = nil
    end
end

function DisposeLikeCombo(chatContentList, objTable)
    if objTable.data.headTableArr then
        for i, v in pairs(objTable.data.headTableArr) do
            if v.faceItem then
                v.faceItem:Dispose()
                v.faceItem = nil
            end
        end
        objTable.data.headTableArr = {}
    end
end

--战斗战报生成
function M:InstantiateBattleReportSareObj(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "BattleReportShareObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.instance = instance

    objTable.data.imgResult = item:Get("imgResult")
    objTable.data.txtDetail = item:Get("txtDetail")
    objTable.data.btn_self = item:Get("btn_self")

    local btn_selfClick = function()
        if objTable.data.clickSelf then
            objTable.data.clickSelf()
        end
    end
    objTable.data.btn_self.onClick:AddListener(btn_selfClick)
end

--侦查战报生成
function M:InstantiateDetectReportSareObj(objTable, itemStyle)
    local parentTransform = objTable.transform
    local instance = self:InstantiateItem(itemStyle, parentTransform, "DetectReportShareObj")
    local item = instance:GetComponent(typeof(ScrollRectItem))
    objTable.data.instance = instance

    objTable.data.txtDetail = item:Get("txtDetail")
    objTable.data.txtTeamCount = item:Get("txtTeamCount")
    objTable.data.txtTeamPower = item:Get("txtTeamPower")
    objTable.data.btn_self = item:Get("btn_self")

    local btn_selfClick = function()
        if objTable.data.clickSelf then
            objTable.data.clickSelf()
        end
    end
    objTable.data.btn_self.onClick:AddListener(btn_selfClick)
end

function RegisterChatHandle(handleTarget, instantiateFunction, process, beforeProcess, afterProcess, disposeProcess, recoveryProcess, getObjKey)
    table.insert(chatHandle, {
        handleTarget = handleTarget,
        instantiate = instantiateFunction,
        process = process,
        beforeProcess = beforeProcess,
        afterProcess = afterProcess,
        disposeProcess = disposeProcess,
        recoveryProcess = recoveryProcess,
        getObjKey = getObjKey
    })
end

--///对象管理 @objTable {obj = GameObject, style = styleType }
function M:PoorSpawn(itemStyle, itemData)
    local objTable = nil
    if openNew and itemStyle and itemData then
        for k, v in ipairs(chatHandle) do
            if v.handleTarget(itemStyle, itemData) then
                local key = table.concat({ itemStyle, "_", k })
                if v.getObjKey then
                    key = v.getObjKey(itemStyle) or key
                end
                if not self.pool[key] then
                    self.pool[key] = {
                        list = {},
                        count = 0
                    }
                end
                local pool = self.pool[key]
                if pool.count > 0 then
                    objTable = pool.list[pool.count]
                    pool.count = pool.count - 1
                    objTable._poolKey = key
                else
                    objTable = v.instantiate(self, itemStyle, self.content)
                    objTable._poolKey = key
                end
                objTable.chatHandle = v
            end
        end
    end

    if objTable == nil then
        for k, v in pairs(self.poorArr) do
            if v.style == itemStyle then
                objTable = v
                table.remove(self.poorArr, k)
                break
            end
        end
    end
    if objTable == nil then
        local template = self.listItems:GetChild(itemStyle)
        --local itemGo = GameObject.Instantiate(template)
        local itemGo = template
        --itemGo.transform:SetParent(self.content)
        itemGo.transform.localScale = Vector3.one
        local layoutElement = itemGo:GetComponent(typeof(LayoutElement))
        objTable = { obj = itemGo, style = itemStyle, layoutElement = layoutElement }
    end
    if not objTable.transform then
        objTable.transform = objTable.obj.transform
    end
    if objTable.layoutElement then
        objTable.layoutElement.ignoreLayout = false
    end
    objTable.transform.anchoredPosition3D = Vector3.zero
    objTable.szChatID = itemData and itemData.szChatID or ""

    local chatObj = objTable.obj
    if not chatObj.activeSelf then
        chatObj:SetActive(true)
    end
    return objTable
end
function M:PoorRecyc(objTable)
    if openNew and objTable._poolKey then
        local key = objTable._poolKey
        if not self.pool[key] then
            self.pool[key] = {
                list = {},
                count = 0
            }
        end
        local pool = self.pool[key]
        pool.count = pool.count + 1
        pool.list[pool.count] = objTable

        self:OnItemRecyc(objTable)
        objTable.isTranslated = false
        if objTable.layoutElement then
            objTable.layoutElement.ignoreLayout = true
        end
        tempVector3.x = 10000
        tempVector3.y = 20000
        tempVector3.z = 0
        objTable.transform.localPosition = tempVector3
        return
    end
    self:OnItemRecyc(objTable)
    local itemGO = objTable.obj
    objTable.isTranslated = false
    if objTable.layoutElement then
        objTable.layoutElement.ignoreLayout = true
    end
    itemGO.transform.localPosition = { x = 10000, y = itemGO.transform.position.y, z = 0 }
    table.insert(self.poorArr, objTable)
end

--释放所有item，Dispose 时调用
function M:DisposeItem()
    if self.pool then
        for k, pool in pairs(self.pool) do
            for i = 1, pool.count do
                self:OnItemDispose(pool.list[i])
            end
        end
        self.pool = {}
    end
    if self.poorArr then
        for k, v in pairs(self.poorArr) do
            self:OnItemDispose(v)
        end
    end
    if self.renderItemArr then
        for k, v in pairs(self.renderItemArr) do
            self:OnItemDispose(v)
        end
    end
end

function M:CheckRefreshUI()
    local refreshTime = self:GetRefreshTime()
    local serverTime = os.server_time()
    --达到刷新时间刷新聊天界面
    if refreshTime and serverTime and refreshTime <= serverTime then
        refreshTime = nil
        util.DelayCallOnce(0, function()
            event.Trigger(event.UPDATE_CHAT_VIEW)
        end)
    end
end
function M:SetRefreshTime(time)
    if time then
        local serverTime = os.server_time()
        if serverTime then
            if (not self.refreshTiem or self.refreshTime > time) and time > serverTime then
                self.refreshTime = time
                print("[chat_content_list]>>SetRefreshTime", self.refreshTime)
            end
        end
    end
end
function M:GetRefreshTime()
    --print("[chat_content_list]>>GetRefreshTime",self.refreshTime)
    return self.refreshTime
end
function M:ResetRefreshTime()
    self.refreshTime = nil
    print("[chat_content_list]>>ResetRefreshTime")
end

--重置状态：回收所有目前显示的item，在重新SetListData或moveToBot时调用
function M:Reset()
    if self.renderItemArr then
        for k, v in pairs(self.renderItemArr) do
            self:PoorRecyc(v)
        end
    end
    self:ResetRefreshTime()
    self.renderItemArr = {}
    self.startInx = -1
    self.endInx = -1
    self.zhanwei.sizeDelta = { x = 40, y = 0 }
    local oldPos = self.content.transform.localPosition
    self.content.transform.localPosition = { x = oldPos, y = 0 }

    frameCount = 0
    nextAddReset = false
    self.gotoIndex = nil
    self.scrollRect.enabled = true
    self.itemDatas = {}
    self.styleDatas = {}
end

function M:ResetIteam(szChatID)
    local renderItem, removePos
    for pos, item in ipairs(self.renderItemArr) do
        if item.szChatID == szChatID then
            renderItem = item
            removePos = pos
            break
        end
    end
    --如果撤回的消息在显示    
    if renderItem then
        self:PoorRecyc(renderItem)
        renderItem.obj:SetActive(false)
        table.remove(self.renderItemArr, removePos)

        --撤回时默认回到最下面
        local endIndex = self.endInx - 1
        self.endInx = math.max(0, endIndex)
        self.zhanwei.sizeDelta = { x = 40, y = 0 }
        local oldPos = self.content.transform.localPosition
        self.content.transform.localPosition = { x = oldPos.x, y = 0 }

        self.gotoIndex = #self.itemDatas
        self.scrollRect.enabled = true

        print("kasug chat--remove success:::", szChatID, renderItem.obj.name)
    else
        --如果撤回的消息不在显示    
        self.startInx = self.startInx - 1
        print("kasug chat--remove failed:::", szChatID)
    end
end

function M:RefreshContentSize()
    UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.content)
end

function M:OnItemRecyc(objTable)
    local chatHandle = objTable.chatHandle
    if openNew and chatHandle and chatHandle.recoveryProcess then
        for _, func in ipairs(chatHandle.recoveryProcess) do
            func(self, objTable)
        end
    end

    if (objTable.style == 0 or objTable.style == 1) and objTable.data and objTable.data.content then
        objTable.data.content.text = ""
    end
end
function M:OnItemRender(objTable, data, first)
    if openNew then
        if objTable.chatHandle then
            if objTable.safe then
                local objTableChatHandle = objTable.chatHandle
                local lastFuncReturnValue = {}
                if objTableChatHandle.beforeProcess then
                    for _, func in ipairs(objTableChatHandle.beforeProcess) do
                        func(self, objTable, data)
                    end
                end
                if objTableChatHandle.process then
                    for _, func in ipairs(objTableChatHandle.process) do
                        lastFuncReturnValue = { func(self, objTable, data, unpack(lastFuncReturnValue)) }

                    end
                end
                if objTableChatHandle.afterProcess then
                    for _, func in ipairs(objTableChatHandle.afterProcess) do
                        func(self, objTable, data)
                    end
                end
            else
                objTable.obj:SetActive(false)

            end
        end
        return
    end
end

function OnGMHeadClick()
    flow_text.Add(lang.Get(15299))
end

function RenderHead(chatContentList, objTable, data)
    local curHead = objTable.data.nHead
    if data.roleid == chat_mgr_new.GMConstRoldId then
        --gm的发言
        objTable.data.nHead:SetActive(false)
        if objTable.style == 0 and objTable.data.GMHead then
            objTable.data.GMHead:SetActive(true)
            objTable.data.GMHeadBtn.onClick:AddListener(OnGMHeadClick)
            curHead = objTable.data.GMHead
        end
    else
        objTable.data.nHead:SetActive(true)
        if objTable.style == 0 and objTable.data.GMHead then
            objTable.data.GMHead:SetActive(false)
        end
        local faceStr = data.faceId
        if data.faceStr and not string.IsNullOrEmpty(data.faceStr) then
            faceStr = data.faceStr
        end
        objTable.data.roleFace:SetFaceInfo(faceStr,
                function()
                    --短按
                    chatContentList:ClickHeadCB(data)
                end,
                nil,
                function()
                    --长按
                    if chatContentList.headLCB then
                        chatContentList.headLCB(data)
                    end
                end
        )
        objTable.data.roleFace:FrameEffectEnable(true, chatContentList.curOrder + 1, 1)
        objTable.data.roleFace:SetActorLvText(false, data.roleLv)
        objTable.data.roleFace:SetFrameID(data.avatarFrame, true)
    end

    -- 根据是否有时间显示的标记位设置 nHead 的 y 值
    if chat_mgr_new.GetIsShowChatTime(data.szChatID) then
        curHead.anchoredPosition = { x = curHead.anchoredPosition.x, y = -103 }
    else
        curHead.anchoredPosition = { x = curHead.anchoredPosition.x, y = -43 }
    end
end

function RenderVip(chatContentList, objTable, data)
    if objTable.data.vipCanvas then
        objTable.data.vipCanvas.sortingOrder = chatContentList.curOrder + 3
    end
    local showVip = (not data.bHideVip) and data.nVipLv >= 3
    objTable.data.vipSpriteSwitch:SetActive(showVip)
    if showVip then
        objTable.data.vipSpriteSwitch:Switch(data.nVipLv - 1)
        objTable.data.vipText.text = data.nVipLv
    end
end

function RenderTitle(chatContentList, objTable, data)
    --时间展示
    local isShowChatTime = chat_mgr_new.GetIsShowChatTime(data.szChatID)
    objTable.data.timeRoot:SetActive(isShowChatTime)
    if isShowChatTime then
        objTable.data.timeText.text = FormatTime(data.chatTime)
    end

    --objTable.data.timeText.text = FormatTime(data.chatTime)
    if data.roleid == chat_mgr_new.GMConstRoldId then
        objTable.data.name.text = util.SetColor(util.EnumColor.Red_Dark2, lang.Get(938))
        objTable.data.roleLv.text = ""
        objTable.data.icon_sex:SetActive(false)
        objTable.data.icon_postionId:SetActive(false)
        objTable.data.icon_leaguePostion:Switch(5)
        objTable.data.txt_leaguePostion.text = util.SetColor(util.EnumColor.White, "GM")
        objTable.data.icon_leaguePostion:SetActive(true)

    else
        --翻译
        if objTable.data.fanyiLogo then
            objTable.data.fanyiLogo:SetActive(false)
        end

        --等级
        objTable.data.roleLv.text = "LV." .. (data.roleLv or 1)

        --男女图标
        local isSexIconShow = data.sex ~= nil and (data.sex == 1 or data.sex == 2)
        objTable.data.icon_sex:SetActive(isSexIconShow)
        if isSexIconShow then
            objTable.data.icon_sex:Switch(data.sex)
        end

        --联盟职称(仅在联盟频道显示)
        local _pageState, _2, isClose = chat_mgr_new.GetPageState()
        local alliance_data = require "alliance_data"
        local allianceData = alliance_data.AllianceOfficeDataMap[data.leaguePostion]
        local isleaguePostionShow = data.leaguePostion ~= nil and data.leaguePostion ~= 0 and _pageState == chat_mgr_new.enum_pState.guide
        objTable.data.icon_leaguePostion:SetActive(isleaguePostionShow)
        if isleaguePostionShow and allianceData then
            objTable.data.icon_leaguePostion:Switch(data.leaguePostion)
            objTable.data.txt_leaguePostion.text = lang.Get(allianceData.langID)
        end

        --区服职称
        local positionID = data.postionId or 0
        objTable.data.icon_postionId:SetActive(positionID ~= 0)
        objTable.data.icon_postionId:Switch(positionID)

        local str_name = ""
        local isSameServerId = true
        --玩家区服id（同服不展示，非同服红色）
        if data.worldid then
            local data_personalInfo = require "data_personalInfo"
            local selfWorldid = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.WorldId)
            if selfWorldid then
                isSameServerId = selfWorldid == data.worldid
                if not isSameServerId then
                    local ui_util = require "ui_util"
                    str_name = "#" .. ui_util.GetWorldIDToShowWorldID(data.worldid, nil, ui_util.WorldIDRangeType.Normal)
                end
            end
        end

        --联盟简称（非同服红色）
        if not string.IsNullOrEmpty(data.leagueShortName) then
            str_name = str_name .. "[" .. data.leagueShortName .. "]"
        end

        --玩家名称（非同服红色）
        str_name = str_name .. data.name
        if isSameServerId then
            str_name = util.SetColor("#4D6676", str_name)
        else
            str_name = util.SetColor(util.EnumColor.Red_Light, str_name)
        end
        objTable.data.name.text = str_name
    end

    --region 巅峰赛相关
    if data.titleID and data.titleID ~= 0 then
        objTable.data.titleIcon.gameObject:SetActive(true)
        local titleInfo = game_scheme:RoleTitle_0(data.titleID)
        if titleInfo then
            objTable.data.titleLab.text = lang.Get(titleInfo.NameID)

            if titleInfo.LimitType == 5 then
                local customTitleInfo = data.customtitle;
                if customTitleInfo ~= nil then
                    local customTitleInfoArray = string.split(customTitleInfo, "#")
                    local titleName = customTitleInfoArray[1]
                    if (titleName ~= nil) then
                        objTable.data.titleLab.text = titleName
                    else
                        objTable.data.titleIcon.gameObject:SetActive(false)
                    end
                end
            end

            objTable.data.titleInfo.text = ""
            if data.customtitle and titleInfo.isUseNum == 1 then
                local infoData = string.split(data.customtitle, '#', tonumber)
                local championTime = infoData[2] or 0
                objTable.data.titleInfo.text = championTime
            end

            if chatContentList.title_sprite_asset then
                chatContentList.title_sprite_asset:GetSprite(titleInfo.rivalType, function(sp)
                    if chatContentList and chatContentList:IsValid() and sp and objTable and objTable.data and not util.IsObjNull(objTable.data.titleIcon) then
                        objTable.data.titleIcon.sprite = sp
                    end
                end)
            end
            if not util.IsObjNull(objTable.data.titleLabGradient) then
                local topColor = color_palette.HexToColor(titleInfo.NameColor)
                local buttonColor = color_palette.HexToColor(titleInfo.NameColorB)
                if objTable.data.titleLabGradient.BottomColor then
                    objTable.data.titleLabGradient.BottomColor = util.MulTable(buttonColor, 255)
                end
                if objTable.data.titleLabGradient.TopColor then
                    objTable.data.titleLabGradient.TopColor = util.MulTable(topColor, 255)
                else
                    if objTable.data.titleLabGradient.color then
                        objTable.data.titleLabGradient.color = topColor   --真机不支持渐变色的使用单色
                    end
                end
            end
            if not util.IsObjNull(objTable.data.titleLabOutline) and objTable.data.titleLabOutline.effectColor then
                local outlineColor = color_palette.HexToColor(titleInfo.outline)
                objTable.data.titleLabOutline.effectColor = outlineColor
            end
        end
    else
        objTable.data.titleIcon.gameObject:SetActive(false)
    end
    --endregion
end

local _renderTranslationMap = {
    [mq_common_pb.enSpeak_UrgentAnnouncement] = {
        canTrs = true,
        canAuto = true,
    },
    [mq_common_pb.enSpeak_Announcement] = {
        canTrs = true,
        canAuto = true,
    },
    [mq_common_pb.enSpeak_AnnounceForChannel] = {
        canTrs = true,
        canAuto = true,
    },
    [mq_common_pb.enSpeak_UrgentAnnounceForChannel] = {
        canTrs = true,
        canAuto = true,
    },
    [mq_common_pb.enSpeak_Text] = {
        canTrs = true,
        canAuto = true,
    },
    [mq_common_pb.enSpeak_At] = {
        canTrs = true,
        canAuto = true,
    }
}

function IsCanRenderTranslation(data)
    return data.roleid ~= player_mgr.GetPlayerRoleID()
            or _renderTranslationMap[data.sType]
end

function IsCanAutoTranslation(data)
    return _renderTranslationMap[data.sType] and _renderTranslationMap[data.sType].canAuto and data.roleid ~= player_mgr.GetPlayerRoleID()
            and not ui_setting_data.IsSamePlatformLangId(data.deviceLangType)
end

local isCanAutoTranslate_setting = false
local isCanAutoTranslate = false
function TryAutoTranslation(objTable, data)
    isCanAutoTranslate_setting = chat_mgr_new.IsCanAutoTranslation()
    isCanAutoTranslate = IsCanAutoTranslation(data)
    if isCanAutoTranslate_setting and isCanAutoTranslate then
        objTable.data.translationFun()
    end
end

function RenderTranslation(chatContentList, objTable, data)
    if IsCanRenderTranslation(data) then
        --if true then
        --系统语言改为游戏内语言 
        local langkey = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
        local LangIso = ui_setting_cfg.LangMap[tonumber(langkey)].iso
        objTable.data.fanyiLogo:SetActive(false)

        local contextStr = data.context
        if data.sType == mq_common_pb.enSpeak_ReinforcePrivateChat then
            local langId = tonumber(data.context)
            contextStr = lang.Get(langId)
        end

        local tStr = chat_mgr_new.GetTranslation(contextStr, LangIso)
        objTable.data.pendant_fanyi:SetActive(not tStr and ((not game_config.Q1SDK_DOMESTIC) or Utility.IsInEditor()))
        if not tStr then
            objTable.data.translated:SetActive(false)
            objTable.data.line:SetActive(false)
            objTable.data.content:SetActive(true)
            objTable.data.content2:SetActive(false)
            local context = contextStr
            objTable.data.onClickTranslation = function()
                objTable.data.fanyiLogo:SetActive(true)
                objTable.data.inTranslation:SetActive(true)
                objTable.data.translated:SetActive(false)
                objTable.isTranslated = true
                local net_chat_module_new = require "net_chat_module_new"
                net_chat_module_new.Req_TRANSLATE(context)
                chatContentList:RefreshContentSize()

                objTable.data.onTranslate = function(sStr, tStr, langIso)
                    if context == sStr then
                        RenderTranslation(chatContentList, objTable, data)
                    end
                end
            end
            TryAutoTranslation(objTable, data)
        else

            objTable.isTranslated = true --已执行翻译操作
            objTable.data.inTranslation:SetActive(false)
            objTable.data.translated:SetActive(true)
            objTable.data.content2:SetActive(true)
            local isShowContent = objTable.data.showTranslateAndHideOriginal == nil or (objTable.data.showTranslateAndHideOriginal ~= nil and objTable.data.showTranslateAndHideOriginal)
            objTable.data.content:SetActive(isShowContent)
            objTable.data.line:SetActive(isShowContent)

            util.DelayCallOnce(0.1, function()
                if chatContentList and chatContentList:IsValid() then
                    objTable.data.content2.text = tStr
                    objTable.data.fanyiLogo.gameObject:SetActive(true)
                    --调整布局
                    --local objRectTransf = objTable.obj:GetComponent(typeof(UI.RectTransform))
                    --UI.LayoutRebuilder.ForceRebuildLayoutImmediate(objRectTransf)
                    if objTable.data.content2.preferredWidth > TEXT_MAX_WIDTH then
                        objTable.data.content2.horizontalOverflow = UnityEngine.HorizontalWrapMode.Wrap
                        --objectData.contentLayout.childControlWidth = false
                        local sizeDelta2 = objTable.data.contentRect.sizeDelta
                        objTable.data.contentRect2.sizeDelta = { x = TEXT_MAX_WIDTH, y = sizeDelta2.y }
                        objTable.data.lineRect.sizeDelta = { x = TEXT_MAX_WIDTH, y = 3 }
                    end
                    UI.LayoutRebuilder.ForceRebuildLayoutImmediate(chatContentList.content)
                end
            end)
        end
    end
end

function RenderCarriageDetailShare(chatContentList, objTable, data)

    local objectData = objTable.data
    objectData.carriageDetailObj.gameObject:SetActive(true)
    local carriageData = data.sandboxCarriageTruckShare
    objectData.carriageQuality:Switch(carriageData.quality - 1)

    objectData.carriageTitleText.text = lang.Get(carriageData.titleDes)
    if carriageData.formats then
        objectData.carriageContentText.text = string.formatL(carriageData.contentLang, unpack(carriageData.formats))
    else
        objectData.carriageContentText.text = lang.Get(carriageData.contentLang)
    end
    objectData.clickJumpFun = function()
        if carriageData then
            if carriageData.tradeid and carriageData.tradeid ~= 0 then
                local net_carriage_module = require "net_carriage_module"
                net_carriage_module.MSG_CARRIAGE_TRADE_DETAIL_REQ(carriageData.tradeid, true, nil, carriageData.sandboxid, carriageData.roleId)
            end
        end
    end
end

function RenderWarRallyShare(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.warRallyShareObj.gameObject:SetActive(true)
    local extendinfopb = data.extendinfopb
    if util.IsObjNull(objectData.warRallyShareObj) then
        return
    end
    if not extendinfopb then
        return
    end
    local warRallyData = chat_mgr_new.GetGatheringChatMsg(extendinfopb)
    objectData.warRallyPlayerName.text = warRallyData.playerName
    objectData.warRallyAttackDes .text = string.format2(lang.Get(663027), warRallyData.killMonsterNum)
    local reward_mgr = require "reward_mgr"
    local good = {
        id = warRallyData.rewardID,
        num = warRallyData.rewardNum,
        nType = warRallyData.rewardType
    }
    objectData.rewardItem = reward_mgr.GetRewardItemData(good, objectData.warRallyRewardTran.transform, true, 0.7)
    objectData.clickJumpFun = function()
        local festival_activity_cfg = require "festival_activity_cfg"
        if not festival_activity_mgr.GetIsOpenByHeadingCode(festival_activity_cfg.ActivityCodeType.WarRally, true) then
            flow_text.Add(lang.Get(663029))
            return
        end
        festival_activity_mgr.OpenActivityUIByActivityCodeType(festival_activity_cfg.ActivityCodeType.WarRally)
        ui_window_mgr:UnloadModule("ui_chat_main_new")
    end
end

function DisposeWarRallyShare(chatContentList, objTable)
    if objTable.data.rewardItem then
        objTable.data.rewardItem:Dispose()
        objTable.data.rewardItem = nil
    end
end

-------------新增沙盘定位分享类型---------------
function RenderSandBoxSharePosition(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.sandboxPosObj.gameObject:SetActive(true)
    local sandboxMarkData = data.sandboxmarkData
    ----objectData.txtPos.text = lang.Get(6255)
    if sandboxMarkData.quality and sandboxMarkData.quality ~= 0 then
        objectData.sandboxPosQuality.gameObject:SetActive(true)
        objectData.sandboxPosQuality:Switch(sandboxMarkData.quality - 1)
    else
        objectData.sandboxPosQuality.gameObject:SetActive(false)
    end
    if sandboxMarkData.tavernTaskID and sandboxMarkData.tavernTaskID ~= 0 then
        local cfg = game_scheme:SecretTask_0(sandboxMarkData.tavernTaskID)
        if cfg then 
            objectData.tavernTaskObj.gameObject:SetActive(true)
            objectData.tavernTaskObj:Switch(cfg.TaskRarity - 1)
            local taskLevel = cfg.TaskLevel
            for i = 1, 5 do
                local star = objTable.data.starGroup.transform:GetChild(i - 1)
                if star then
                    local isShow = taskLevel >= i
                    star.gameObject:SetActive(isShow)
                end
            end
        end
    else
        objectData.tavernTaskObj.gameObject:SetActive(false)
    end

    local ui_chat_data_gw = require "ui_chat_data_gw"
    local contentText = ui_chat_data_gw._getMainChatStr[mq_common_pb.enSpeak_SandboxMarkPos](data)

    objectData.sandboxPosContentText.text = contentText
    --不要直接使用这个，会缓存字符串，导致国际化有问题
    --[[    if not string.IsNullOrEmpty(sandboxMarkData.titleDes) then
            objectData.TitleText.text = lang.Get(sandboxMarkData.titleDes)
        end]]

    if sandboxMarkData.titleID ~= 0 then
        objectData.TitleText.text = lang.Get(sandboxMarkData.titleID)
    else
        objectData.TitleText.text = lang.Get(602010)
    end

    objectData.clickJumpFun = function()
        if data.sandboxmarkData then
            if data.sandboxmarkData.sid and data.sandboxmarkData.sid ~= 0 then
                if sandboxMarkData.quality and sandboxMarkData.quality ~= 0 then
                    local reportMsg = {
                        Truck_quality = sandboxMarkData.quality, --货车品质
                    }
                    event.EventReport("TruckTrade_ClickShare", reportMsg)
                end
            end
            local grid = { x = data.sandboxmarkData.x, y = data.sandboxmarkData.y }
            local sandUIMgr = require "sandbox_ui_mgr"
            if sandUIMgr.GetNewUIDataSwitch() then
                gw_common_util.JumpToSandAndOpenAttack_Common(data.sandboxmarkData.sid, grid, data.sandboxmarkData.sandboxSid)
            else
                if sandboxMarkData.quality and sandboxMarkData.quality ~= 0 then
                    event.RegisterOnce(sand_ui_event_define.GW_SAND_SEARCH_CARRIAGE_DATA_Get, function(_, msg)
                        if msg.pos then
                            gw_common_util.JumpToGrid(msg.pos, function()
                                util.DelayCallOnce(0.2, function()
                                    net_sandbox_module.MSG_SANDBOX_GET_DETAIL_REQ(msg.sid)
                                end)
                            end, data.sandboxmarkData.sandboxSid)
                        else
                            flow_text.Add(lang.Get(100000 + msg.err))
                        end
                    end)
                end
                if sandboxMarkData.quality and sandboxMarkData.quality ~= 0 then
                    net_sandbox_module.MSG_SANDBOX_CARRIAGEPOS_REQ(data.sandboxmarkData.sid, data.sandboxmarkData.sandboxSid)
                else
                    gw_common_util.JumpToSandAndOpenAttack_Common(data.sandboxmarkData.sid, grid, data.sandboxmarkData.sandboxSid)
                end
            end
            ui_window_mgr:UnloadModule("ui_chat_main_new")
        end
    end
end
------------------------------

-------------新增灭火定位分享类型-------------
function RenderCityFirePosition(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.cityFirePosObj.gameObject:SetActive(true)
    local ui_util = require "ui_util"
    local sandboxMarkData = data.sandboxmarkData
    local showWorldId = ui_util.GetWorldIDToShowWorldID(sandboxMarkData.sandboxSid, nil, ui_util.WorldIDRangeType.Normal)
    local posText = string.format("[<a href=w>%s #%d X:%d Y:%d</a>]", lang.Get(602159), showWorldId, sandboxMarkData.x, sandboxMarkData.y)
    local contentText = ""
    if sandboxMarkData and sandboxMarkData.playerName and not string.empty(sandboxMarkData.playerName) then
        contentText = sandboxMarkData.playerName
    end
    contentText = string.format("%s %s", contentText, posText)
    objectData.cityFirePosDesTxt.text = contentText

    objectData.clickJumpFun = function()
        if sandboxMarkData then
            local grid = { x = sandboxMarkData.x, y = sandboxMarkData.y }
            gw_common_util.JumpToSandAndOpenAttack_Common(sandboxMarkData.sid, grid, sandboxMarkData.sandboxSid)
            ui_window_mgr:UnloadModule("ui_chat_main_new")
        end
    end

end

-------------联盟火车定位分享类型---------------
function RenderAllianceTrainSharePosition(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.trainShareObj.gameObject:SetActive(true)
    --log.Warning("[chat] data", Edump(data))
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local trainInfo = chat_mgr_new.GetAllianceTrainMsg(extendinfopb)
        local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(trainInfo.trainType)
        local nowRewardCount = (trainInfo.nCompleteness or 0)
        local allRewardCount = intercity_alliance_train_mgr.GetAllianceTrainCount(trainInfo.trainType)
        local progressValue = (nowRewardCount / allRewardCount) * 100

        objectData.progress.text = progressValue .. "%"
        objectData.level.text = string.format("Lv.%s", trainInfo.roleLv)
        objectData.power.text = util.NumberWithUnit2(trainInfo.rolePower)
        if string.empty(trainInfo.allianceName) then
            objectData.trainName.text = trainInfo.roleName
        else
            objectData.trainName.text = string.format("[%s]%s", trainInfo.allianceName, trainInfo.roleName)
        end
        objTable.data.LeaderAvatar = objTable.data.LeaderAvatar or face_item_new.CFaceItem():Init(objectData.avatar, nil, 1.2)--alliance_ui_util.CreatHeadIcon(avatar, 1.25, dataItem.faceId, dataItem.frameID)
        objTable.data.LeaderAvatar:SetFaceInfo(trainInfo.nAvatarId)
        objTable.data.LeaderAvatar:SetFrameID(trainInfo.nAvatarFrameId, true)
        chatContentList.trainAsset:GetSprite(cfg.icon .. "_lt_lb", function(sp)
            if not util.IsObjNull(objectData.Icon) then
                objectData.Icon.sprite = sp
            end
        end)
        objectData.clickJumpFun = function()
            if trainInfo then
                intercity_alliance_train_mgr.SwitchToSand(trainInfo.trainSid, trainInfo.sandBoxSid, function()
                    ui_window_mgr:UnloadModule("ui_chat_main_new")
                end)
            end
        end
    end

end
------------------------------
-------------联盟火车详情推送类型---------------
function RendeAllianceTrainDetail(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.trainDetailObj.gameObject:SetActive(true)
    --log.Warning("[chat] data", Edump(data))
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local allianceTrain_pb = require "allianceTrain_pb"
        local trainInfo = chat_mgr_new.GetAllianceTrainMsg(extendinfopb)
        if trainInfo then
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(trainInfo.trainType)
            local trainId = trainInfo.trainId

            local langID = 675102
            if trainInfo.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Prepare then
                langID = 675101
            elseif trainInfo.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Running then
                langID = 675102
            elseif trainInfo.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Arrived then
                langID = 675103
            end

            objectData.title.text = lang.Get(langID)
            objectData.level.text = string.format("Lv.%s", trainInfo.roleLv)
            objectData.power.text = util.NumberWithUnit2(trainInfo.rolePower)
            if string.empty(trainInfo.allianceName) then
                objectData.trainName.text = trainInfo.roleName
            else
                objectData.trainName.text = string.format("[%s]%s", trainInfo.allianceName, trainInfo.roleName)
            end
            objTable.data.LeaderAvatar = objTable.data.LeaderAvatar or face_item_new.CFaceItem():Init(objectData.avatar, nil, 1.2)--alliance_ui_util.CreatHeadIcon(avatar, 1.25, dataItem.faceId, dataItem.frameID)
            objTable.data.LeaderAvatar:SetFaceInfo(trainInfo.nAvatarId)
            objTable.data.LeaderAvatar:SetFrameID(trainInfo.nAvatarFrameId, true)
            chatContentList.trainAsset:GetSprite(cfg.icon .. "_lt_jx", function(sp)
                if not util.IsObjNull(objectData.Icon) then
                    objectData.Icon.sprite = sp
                end
            end)
            objectData.clickJumpFun = function()
                if trainInfo and trainInfo.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Running then
                    if trainInfo.trainSid and trainInfo.trainSid ~= 0 then
                        intercity_alliance_train_mgr.SwitchToSand(trainInfo.trainSid, trainInfo.sandBoxSid, function()
                            ui_window_mgr:UnloadModule("ui_chat_main_new")
                        end)
                    else
                        local grid = { x = trainInfo.pos.posX, y = trainInfo.pos.posY }
                        gw_common_util.JumpToGrid(grid, trainInfo.sandBoxSid)
                        --event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
                    end
                elseif trainInfo.pushType == allianceTrain_pb.ALLIANCETRAIN_CHAT_PUSH_TYPE_Prepare then
                    -- 判断打开跳转到对应界面                    
                    local isOpen = intercity_alliance_train_mgr.OpenTrainMain(nil, trainId)
                    if isOpen then
                        ui_window_mgr:UnloadModule("ui_chat_main_new")
                    end
                else
                    flow_text.Add(lang.Get(675088))
                end
            end
        end
    end
end
------------------------------
-------------联盟火车被掠夺推送类型---------------
function RendeAllianceTrainBattle(chatContentList, objTable, data)
    local objectData = objTable.data
    if objectData.trainBattleObj then
        objectData.trainBattleObj.gameObject:SetActive(true)
    end
    --log.Warning("[chat] data", Edump(data))

    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local intercity_alliance_train_mgr = require "intercity_alliance_train_mgr"
        local trainInfo = chat_mgr_new.GetAllianceTrainMsg(extendinfopb)
        if trainInfo then
            local cfg = intercity_alliance_train_mgr.GetAllianceTrainCfgByID(trainInfo.trainType)
            objectData.trainName.text = trainInfo.roleName
            local ui_util = require "ui_util"
            objectData.zone.text = string.format("#%s", ui_util.GetWorldIDToShowWorldID(trainInfo.worldId, nil, ui_util.WorldIDRangeType.Normal))
            if not string.empty(trainInfo.allianceName) then
                objectData.union.text = string.format("[%s]", trainInfo.allianceName)
            else
                objectData.union.text = ""
            end
            objectData.lv.text = string.format("Lv.%s", trainInfo.roleLv)
            objectData.power.text = util.NumberWithUnit2(trainInfo.rolePower)

            objTable.data.LeaderAvatar = objTable.data.LeaderAvatar or face_item_new.CFaceItem():Init(objectData.face, nil, 1.2)
            objTable.data.LeaderAvatar:SetFaceInfo(trainInfo.nAvatarId)
            objTable.data.LeaderAvatar:SetFrameID(trainInfo.nAvatarFrameId, true)
            chatContentList.trainAsset:GetSprite(cfg.icon, function(sp)
                if not util.IsObjNull(objectData.Icon) then
                    objectData.Icon.sprite = sp
                end
            end)

            local reward = trainInfo.nRewardId
            if reward then
                local reward_mgr = require "reward_mgr"
                objectData.goodsItemList = reward_mgr.GetRewardItemList(reward, objectData.reward.transform, nil, 0.65)
            end
        end
    end
end
------------------------------

---------新增游荡怪集结分享------------
function RenderSandBoxShareMass(chatContentList, objTable, data)
    local objectData = objTable.data
    local AllianceMgr = require "alliance_mgr"
    --默认设置
    if data.sandboxmassData.massFinishTime <= os.server_time() then
        objectData.gatherTeamGoButtonText.text = lang.Get(560272)       --已过期
    else
        objectData.gatherTeamGoButtonText.text = lang.Get(560325)       --集结（默认文本）
    end

    local teamPos = { x = data.sandboxmassData.x, y = data.sandboxmassData.y }
    local distance = math.floor(gw_common_util.GetKilometerByGrid(teamPos, gw_common_util.GetSandBasePosition()))
    objectData.gatherTeamDistanceText.text = string.format("<a href=w>%s</a>", string.format2(lang.Get(560270), distance))       --距离{%s1}公里
    objectData.gatherTeamDistanceText.raycastTarget = true
    objectData.gatherTeamLeaderNameText.text = string.format2(lang.Get(560271), data.sandboxmassData.strName)--集结发起人：{%s1}
    local info = game_scheme:SandMapMonster_0(data.sandboxmassData.targetId)
    if info then
        objectData.gatherTeamMonsterNameText.text = string.format("Lv.%d %s", info.level, lang.Get(info.name))    --游荡怪名字和等级
        --游荡怪图标
        chatContentList.monsterIconAsset:GetSprite(info.icon, function(sprite)
            if sprite then
                objectData.gatherTeamMonsterIcon.sprite = sprite
            end
        end)
    end

    local teamData = AllianceMgr.GetAllianceWarGenData(data.sandboxmassData.massTeamId) or nil
    local isOver = data.sandboxmassData.massFinishTime <= os.server_time()        --是否过期
    if not teamData then
        local waitingTime = data.sandboxmassData.massFinishTime - os.server_time()
        waitingTime = (waitingTime <= 0 and data.sandboxmassData.waitingTime) and data.sandboxmassData.waitingTime - os.server_time() or waitingTime
        if waitingTime <= 0 then
            --已过期
            isOver = true
            teamData = {}
            teamData.massLeaderId = data.sandboxmassData.massLeaderId or 0
            teamData.memberList = {}
            teamData.massFinishTime = data.sandboxmassData.massFinishTime or 0
            objectData.gatherTeamGoButtonText.text = lang.Get(560272)       --已过期
        end
    end
    --设置按钮置灰显示
    objectData.gatherTeamGoButtonGray:SetEnable(isOver)
    --设置等待时间的显示
    Common_Util.SetActive(objectData.gatherTeamWaitTimeText.gameObject, not isOver)
    Common_Util.SetActive(objectData.gatherTeamObj.gameObject, true)

    local function updateTeamShow(curTeamData)
        if not curTeamData then
            return
        end
        local isJoin = false
        isOver = data.sandboxmassData.massFinishTime > os.server_time()
        if isOver then
            local roleId = player_mgr.GetPlayerRoleID()
            if curTeamData.massLeaderId == roleId then
                --自己创建的队伍
                if not util.IsObjNull(objectData.gatherTeamGoButton) then
                    Common_Util.SetActive(objectData.gatherTeamGoButton.gameObject, false)
                end
                isJoin = true
                --objectData.gatherTeamGoButtonText.text = lang.Get(560047)       --已加入
            else
                --别人创建的队伍
                for k, v in ipairs(curTeamData.memberList) do
                    if v.roleId == roleId then
                        Common_Util.SetActive(objectData.gatherTeamGoButton.gameObject, false)
                        isJoin = true
                        --objectData.gatherTeamGoButtonText.text = lang.Get(560047)
                        break
                    end
                end
            end
        else
            if objectData.gatherTeamPlayerListItem then
                for k, v in pairs(objectData.gatherTeamPlayerListItem) do
                    if v then
                        Common_Util.SetActive(v.gameObject, false)
                    end
                end
            end
            return
        end
        --设置队伍成员数据
        objectData.gatherTeamPlayerListItem = objectData.gatherTeamPlayerListItem or {}
        local index = 1
        for i = 1, 5 do
            local member = curTeamData.memberList[i]
            local itemName = string.format("FaceItem_%d", index)
            if util.IsObjNull(objectData.gatherTeamPlayerList) then
                return
            end
            local parentTran = objectData.gatherTeamPlayerList:Find(itemName)
            if member then
                if member.roleId ~= curTeamData.massLeaderId then
                    local FaceItem = objectData.gatherTeamPlayerListItem[index] or face_item_new.CFaceItem()
                    FaceItem:Init(parentTran, function()
                        Common_Util.SetActive(FaceItem.gameObject, true)
                    end, 0.9)
                    FaceItem:SetFaceInfo(member.faceId, function()
                        local pageState, curSessionData, isClose = chat_mgr_new.GetPageState()
                        local mgr_personalInfo = require "mgr_personalInfo"
                        mgr_personalInfo.ShowRoleInfoView(member.roleId, pageState)
                    end)
                    FaceItem:SetFrameID(member.frameID, true)
                    if objectData.gatherTeamPlayerListItem[index] then
                        Common_Util.SetActive(objectData.gatherTeamPlayerListItem[index].gameObject, true)
                    end
                    objectData.gatherTeamPlayerListItem[index] = FaceItem
                    index = index + 1
                end
            else
                if objectData.gatherTeamPlayerListItem[index] then
                    Common_Util.SetActive(objectData.gatherTeamPlayerListItem[index].gameObject, false)
                end
                index = index + 1
            end
            local AddIcon = parentTran:Find("AddIcon")
            Common_Util.SetActive(AddIcon, not isJoin)
        end
    end
    updateTeamShow(teamData)

    objectData.clickDataChange = function(eventName, teamId)
        if teamId == data.sandboxmassData.massTeamId then
            teamData = AllianceMgr.GetAllianceWarGenData(data.sandboxmassData.massTeamId)
            if teamData then
                updateTeamShow(teamData)
            end
        end

    end
    event.Register(sand_ui_event_define.GW_SAND_SHARE_DATA_CHANGE, objectData.clickDataChange)
    --倒计时显示
    if objectData.Timer then
        util.RemoveDelayCall(objectData.Timer)
        objectData.Timer = nil
    end
    objectData.Timer = util.IntervalCall(1, function()
        if teamData.massTeamId ~= data.sandboxmassData.massTeamId then
            objectData.Timer = nil
            return true
        end
        if util.IsObjNull(objectData.gatherTeamWaitTimeText) then
            objectData.Timer = nil
            return true
        end
        if teamData and data.sandboxmassData.massFinishTime ~= teamData.massFinishTime then
            objectData.Timer = nil
            return true
        end
        local waitTime = math.floor(data.sandboxmassData.massFinishTime - os.server_time())
        if waitTime > 0 and not util.IsObjNull(objectData.gatherTeamWaitTimeText) then
            --还在等待盟友加入中
            objectData.gatherTeamWaitTimeText.text = string.format2(lang.Get(560268), util.GetCountDown(waitTime))   --集结等待中：{%s1}
        else
            if teamData and teamData.waitingTime and teamData.waitingTime ~= 0 then
                local waitPlayerTime = teamData.waitingTime - os.server_time()
                if waitPlayerTime > 0 then
                    objectData.gatherTeamWaitTimeText.text = string.format2(lang.Get(560269), util.GetCountDown(waitPlayerTime))   --等待盟友中：{%s1}
                end
            else
                objectData.gatherTeamGoButtonGray:SetEnable(true)
                Common_Util.SetActive(objectData.gatherTeamWaitTimeText.gameObject, false)
                Common_Util.SetActive(objectData.gatherTeamGoButton.gameObject, true)
                objectData.gatherTeamGoButtonText.text = string.format2(lang.Get(560272))   --已过期
                isOver = true
                for i = 1, 4 do
                    local itemName = string.format("FaceItem_%d", i)
                    local parentTran = objTable.data.gatherTeamPlayerList:Find(itemName)
                    local addIcon = parentTran:Find("AddIcon")
                    if objectData.gatherTeamPlayerListItem and objectData.gatherTeamPlayerListItem[i] then
                        Common_Util.SetActive(objectData.gatherTeamPlayerListItem[i].gameObject, false)
                    end
                    Common_Util.SetActive(addIcon.gameObject, false)
                end
                objectData.Timer = nil
                return true
            end
        end
    end)

    objectData.clickJumpFun = function()
        --跳转游荡怪的方法
        local curTeamData = AllianceMgr.GetAllianceWarGenData(data.sandboxmassData.massTeamId) or nil
        if not curTeamData then
            return
        end
        -- 跨服不能参与游荡怪集结
        if gw_common_util.GetSandCrossServiceState() then
            flow_text.Add(lang.Get(666005))
            return
        end
        local grid = { x = data.sandboxmassData.x, y = data.sandboxmassData.y }
        gw_common_util.JumpToGrid(grid)
        ui_window_mgr:UnloadModule("ui_chat_main_new")
    end

    objectData.clickAttackFun = function()
        --前往集结方法
        local curTeamData = AllianceMgr.GetAllianceWarGenData(data.sandboxmassData.massTeamId) or nil
        if not curTeamData then
            return
        end

        local lineType = curTeamData.gotoTargetMarchType
        local tempSid = curTeamData.targetSId
        local entityData = {
            pos = { x = data.sandboxmassData.x, y = data.sandboxmassData.y },
            sid = tempSid,
            cfg = info,
        }
        gw_common_util.JumpToAndShowAttack_GatherJoin(curTeamData.leaderPos, tempSid, lineType,
                function(selectIndex, soldierNum)
                    if not selectIndex then
                        flow_text.Add(string.format2(lang.Get(560363), lang.Get(info.name)))
                        return
                    end
                    --调用加入消息
                    local sandbox_gather_mgr = require "sandbox_gather_mgr"
                    sandbox_gather_mgr.JoinGatherTeam(data.sandboxmassData.massTeamId, selectIndex, soldierNum)
                end, nil, curTeamData.sandboxSid, entityData)
        ui_window_mgr:UnloadModule("ui_chat_main_new")
    end

end

function DisposeSandBoxShareMass(chatContentList, objTable)
    local objectData = objTable.data
    if objectData and objectData.gatherTeamObj then
        if objectData.Timer then
            timer_mgr:RemoveTimer(objectData.gatherTeamObj, objectData.Timer)
            objTable.data.Timer = nil
        end
        event.Unregister(sand_ui_event_define.GW_SAND_SHARE_DATA_CHANGE, objTable.data.clickDataChange)
        if objectData.gatherTeamPlayerListItem then
            for k, v in pairs(objectData.gatherTeamPlayerListItem) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end
            objectData.gatherTeamPlayerListItem = {}
        end
    end
end
------------------------------

-------------新增雷达宝藏分享类型---------------
function RenderRadarTreasureSharePos(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.treasureObj.gameObject:SetActive(true)

    if data.sandboxTreasureData then

        local sandboxTreasureData = data.sandboxTreasureData
        local warZoneID = string.format("%s%s",lang.Get(560111),sandboxTreasureData.bServerID)
        objectData.treasureText.text = string.format("%s <a href=w>(%s X:%s Y:%s )</a>", lang.Get(652018),warZoneID,sandboxTreasureData.x, sandboxTreasureData.y) --tempText
        objectData.clickJumpFun = function()
            local grid = { x = sandboxTreasureData.x, y = sandboxTreasureData.y }

            local reqData = {
                callback = 1,
                sid = 0,
                pos = grid,
            }

            net_sandbox_module.MSG_SANDBOX_CALLBACK_DETAIL_REQ(reqData, function(rspMsg)
                if not rspMsg.exist then
                    --飘字 宝藏已消失
                    flow_text.Add(lang.Get(652109))
                else
                    if rspMsg.enType then
                        if rspMsg.enType ~= sandbox_pb.enSandboxEntity_RadarTreasure and rspMsg.enType ~= sandbox_pb.enSandboxEntity_KastenBox then
                            flow_text.Add(lang.Get(652109))
                        end
                    end
                end
            end)

            gw_common_util.JumpToGrid(grid, function()
                ui_window_mgr:UnloadModule("ui_chat_main_new")
                event.EventReport("Radar_ClickShare", {})
            end, gw_common_util.GetSandZoneSandBoxSid())
        end
    end
end
------------------------------

-------------新增酒馆任务分享类型---------------
function RenderTavernTaskSharePos(chatContentList, objTable, data)

    --log.Error("渲染酒馆分享方法！！！")

    local objectData = objTable.data
    objectData.tavernObj.gameObject:SetActive(true)

    if data.sandboxTavernData then

        local sandboxTavernData = data.sandboxTavernData

        local tempText = sandboxTavernData.context
        objectData.strText.text = tempText

        objectData.clickJumpFun = function()
            local grid = { x = sandboxTavernData.x, y = sandboxTavernData.y }
            gw_common_util.JumpToGrid(grid, function()
                ui_window_mgr:UnloadModule("ui_chat_main_new")
            end)
        end
    end

end
------------------------------

-----------------新增联盟成就分享类型------------
function RenderAllianceAchievement(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.achievementObj.gameObject:SetActive(true)

    if data.leagueAchievementData then

        --local leagueAchievementData = data.leagueAchievementData
        local alliance_achievement_data = require "alliance_achievement_data"

        local leagueAchievementData = nil
        if alliance_achievement_data.GetAchievementDataByAchID(data.leagueAchievementData.achID) then
            leagueAchievementData = alliance_achievement_data.GetAchievementDataByAchID(data.leagueAchievementData.achID).baseInfo or data.leagueAchievementData
        end

        local cfg = leagueAchievementData and game_scheme:LeagueAchievements_0(leagueAchievementData.achID) or nil
        if cfg and leagueAchievementData then
            --leagueAchievementData.achID
            Common_Util.SetActive(objectData.UnlockPanel, leagueAchievementData.state ~= 0)
            Common_Util.SetActive(objectData.NotUnlockPanel, leagueAchievementData.state == 0)

            local titleStr = string.format("%s-%s", lang.Get(600494), lang.Get(cfg.AchieveName))
            objectData.titleText2.text = titleStr
            objectData.titleText.text = titleStr

            objectData.AchievementBtn.onClick:RemoveAllListeners()
            objectData.AchievementBtn.onClick:AddListener(function()
                --log.Error("点击了联盟成就！！！！")
                local alliance_mgr_extend = require "alliance_mgr_extend"
                alliance_mgr_extend.JumpAchievementView(leagueAchievementData.achID)
            end)

            --region 成就描述
            local alliance_mgr_extend = require "alliance_mgr_extend"
            local lockState = alliance_mgr_extend.GetConditionParam(cfg.AchieveID)
            if lockState == 0 then
                objectData.desText.text = lang.Get(cfg.AchieveConditionText)
            elseif lockState == 1 then
                objectData.desText.text = string.format2(lang.Get(cfg.AchieveConditionText), cfg.AchieveConditionParameter1.data[0])
            elseif lockState == 2 then
                objectData.desText.text = string.format2(lang.Get(cfg.AchieveConditionText), cfg.AchieveConditionParameter2)
            end
            --endregion

            if leagueAchievementData.state == 0 then

                local setStateFun = function()
                    Common_Util.SetActive(objectData.UnlockPanel, false)
                    Common_Util.SetActive(objectData.NotUnlockPanel, true)

                    objectData.slider.value = leagueAchievementData.finishCnt / leagueAchievementData.reqCnt
                    objectData.Text_collect.text = string.format("%s/%s", leagueAchievementData.finishCnt, leagueAchievementData.reqCnt)
                end

                local curTime = leagueAchievementData.unlockTime - os.server_time()

                if curTime <= 0 then
                    setStateFun()
                else
                    objectData.timer = objectData.timer or util.IntervalCall(1, function()
                        local tempTime = leagueAchievementData.unlockTime - os.server_time()
                        objectData.tipText.text = string.format2(lang.Get(600538), time_util.FormatTimeXMan(tempTime))

                        if tempTime <= 0 then
                            setStateFun()
                            return true
                        end
                    end)
                end

            elseif leagueAchievementData.state == 1 or leagueAchievementData.state == 2 then
                objectData.slider.value = leagueAchievementData.finishCnt / leagueAchievementData.reqCnt
                objectData.Text_collect.text = string.format("%s/%s", leagueAchievementData.finishCnt, leagueAchievementData.reqCnt)
            end
        end

    end

end
-----------------------------------------------

--region 中立城池新增
function RenderOccupyNeutralCity(chatContentList, objTable, data) 
    if data.sandboxNCOccupied then
        local ui_chat_data_gw = require "ui_chat_data_gw"
        local message, cityCfg, regionCfg = ui_chat_data_gw._getMainChatStr[mq_common_pb.enSpeak_NC_Occupied](data)
        objTable.data.cityText.text = message

        objTable.data.cityBtn.onClick:RemoveAllListeners()
        objTable.data.cityBtn.onClick:AddListener(function()
            if regionCfg then
                local cityPos = cfg_util.StringToNumberArray(regionCfg.CityPos)
                local grid = { x = cityPos[1], y = cityPos[2] }
                gw_common_util.JumpToGrid(grid, function()
                    ui_window_mgr:UnloadModule("ui_chat_main_new")
                end)
            end
        end)

        -- 显示模型
        local modelCfg = game_scheme:SandMapModelResource_0(cityCfg.ModelResource)
        if modelCfg then
            if string.IsNullOrEmpty(modelCfg.UIpicture) then
                if not objTable.data.ncOccupiedObj then
                    local ui_module_item = require "ui_module_item"
                    objTable.data.ncOccupiedObj = ui_module_item:new()
                    objTable.data.ncOccupiedObj:Init(objTable.data.cityRImg.gameObject, nil)
                end
                objTable.data.ncOccupiedObj:SetChangeModel(modelCfg.ModelRes)
                objTable.data.ncOccupiedObj:SetCameraDistance(7)
            else
                local gw_asset_mgr = require "gw_asset_mgr"
                gw_asset_mgr:SetNeutralCityIcon(modelCfg.UIpicture, function(sprite)
                    if objTable.data.cityIcon and not util.IsNullOrEmpty(objTable.data.cityIcon) and sprite then
                        objTable.data.cityIcon.sprite = sprite
                    end
                end)
            end
        end
    end
end

function DisposeOccupyNeutralCity(chatContentList, objTable)
    if objTable.data and objTable.data.ncOccupiedObj then
        objTable.data.ncOccupiedObj:Dispose()
        objTable.data.ncOccupiedObj = nil
    end
end

function RenderAbandonNeutralCity(chatContentList, objTable, data)
    if not string.IsNullOrEmpty(data.extendinfo) then
        local ui_chat_data_gw = require "ui_chat_data_gw"
        local message = ui_chat_data_gw._getMainChatStr[mq_common_pb.enSpeak_NC_Abandon](data)
        objTable.data.systemText.text = message
    end
end

function RenderShareNeutralCity(chatContentList, objTable, data)
    if data.neutralCityData then
        local tempData = data.neutralCityData
    end
end
--endregion

--region 雷达新加的
function RenderDigTreasure(chatContentList, objTable, data)
    if data.sandboxTreasureFinishData then
        local tempData = data.sandboxTreasureFinishData

        local posStr = string.format("<u>（%s，%s）</u>", tempData.x, tempData.y)
        objTable.data.msgText.text = string.format2(lang.Get(652083), tempData.playerName, posStr)

        objTable.data.clickJumpFun = function()

            local radar_mgr = require "radar_mgr"
            radar_mgr.ReqChatDigTreasureInfo(tempData.kastenboxSid)
            ----这里要判断宝箱挖完没？--不对是自己有没有领取过
            --local grid = {x = tempData.x, y = tempData.y}
            --gw_common_util.JumpToGrid(grid,function()
            --    ui_window_mgr:UnloadModule("ui_chat_main_new")
            --end)
        end

    end
end

function RenderDoubleReward(chatContentList, objTable, data)

    if data.sandboxKastenboxMultipleRewardData then
        local tempData = data.sandboxKastenboxMultipleRewardData
        local uiData = objTable.data

        uiData.Text_name.text = tempData.playerName
        uiData.tips.text = lang.Get(652082)

        objTable.data.roleFace = face_item_new.CFaceItem():Init(uiData.headParent, nil, 1.1)
        local faceStr = tempData.faceID
        if tempData.faceStr and not string.IsNullOrEmpty(tempData.faceStr) then
            faceStr = tempData.faceStr
        end
        objTable.data.roleFace:SetFaceInfo(faceStr)--iconUI:SetFaceInfo(faceID, clickEvent)
        objTable.data.roleFace:SetFrameID(tempData.frameID, true)

        objTable.data.clickJumpFun = function()
            --点击双倍 应该处理什么
            --log.Error("处理双倍的点击！！！")

            local radar_mgr = require "radar_mgr"
            radar_mgr.ReqChatDigTreasureInfo(tempData.treasureData.treasureSid)

            ----这里要判断宝箱挖完没？--不对是自己有没有领取过
            --local grid = {x = tempData.x, y = tempData.y}
            --gw_common_util.JumpToGrid(grid,function()
            --    ui_window_mgr:UnloadModule("ui_chat_main_new")
            --end)
        end
    end

end
--endregion

function RenderContentText(chatContentList, objTable, data, text)
    local objectData = objTable.data
    objectData.contentLayout:SetActive(true)
    --objectData.inlineText.text = nil
    objectData.content.horizontalOverflow = UnityEngine.HorizontalWrapMode.Overflow
    objectData.content2.horizontalOverflow = UnityEngine.HorizontalWrapMode.Overflow
    objectData.contentLayout.childControlWidth = true
    --客服消息，可能包含链接，使用LinkImageTextEx组件进行显示
    --data.context = "<a href=\"https://www.baidu.com/\" target=\"_blank\">zzz</a>"
    if data.roleid == chat_mgr_new.GMConstRoldId then
        objectData.content.gameObject:SetActive(false)
        if objectData.contentGM then
            objectData.contentGM.gameObject:SetActive(true)
            local tempText = text or data.context or " "
            objectData.contentGM.text = DealHTMLContent(tempText)
        end
        objectData.contentBtn.enabled = false
        if objectData.linkImageTextEx then
            objectData.linkImageTextEx.onHrefClick:RemoveAllListeners()
            objectData.linkImageTextEx.onHrefClick:AddListener(function(name)
                if string.find(name, "#copy#") then
                    local ui_private_chat_panel = require "ui_private_chat_panel"
                    ui_private_chat_panel.ShowCopyTips(string.gsub(name, "#copy#", ""))
                else
                    local url = mail_data_gw.ReplaceContent(name)
                    local q1sdk = require "q1sdk"
                    q1sdk.ApplicationOpenURL(url)
                end
            end)
        end

        if objectData.contentGM and objectData.contentGM.preferredWidth > TEXT_MAX_WIDTH then
            objectData.contentGM.horizontalOverflow = UnityEngine.HorizontalWrapMode.Wrap
            objectData.contentLayout.childControlWidth = false
            local sizeDelta = objectData.contentGMRect.sizeDelta
            objectData.contentGMRect.sizeDelta = { x = TEXT_MAX_WIDTH, y = sizeDelta.y }
        end
    else
        --玩家消息
        objectData.content.gameObject:SetActive(true)
        if objectData.contentGM then
            objectData.contentGM.gameObject:SetActive(false)
        end
        local contextStr = data.context
        if data.sType == mq_common_pb.enSpeak_ReinforcePrivateChat then
            local langId = tonumber(data.context)
            contextStr = lang.Get(langId)
        end
        objectData.content.text = text or contextStr
    end

    if objectData.content.preferredWidth > TEXT_MAX_WIDTH or objectData.content2.preferredWidth > TEXT_MAX_WIDTH then
        objectData.content.horizontalOverflow = UnityEngine.HorizontalWrapMode.Wrap
        objectData.content2.horizontalOverflow = UnityEngine.HorizontalWrapMode.Wrap
        objectData.contentLayout.childControlWidth = false
        local sizeDelta = objectData.contentRect.sizeDelta
        objectData.contentRect.sizeDelta = { x = TEXT_MAX_WIDTH, y = sizeDelta.y }
        local sizeDelta2 = objectData.contentRect.sizeDelta
        objectData.contentRect2.sizeDelta = { x = TEXT_MAX_WIDTH, y = sizeDelta2.y }
        objectData.lineRect.sizeDelta = { x = TEXT_MAX_WIDTH, y = 3 }
    end
    --local objRectTransf = objTable.obj:GetComponent(typeof(UI.RectTransform))
    -- UI.LayoutRebuilder.MarkLayoutForRebuild(objRectTransf)
end

-------------酒馆藏宝图碎片分享类型---------------
function RenderTavernTreasure(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.trainShareObj.gameObject:SetActive(true)
    --log.Warning("[chat] data", Edump(data))
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local orderInfo = chat_mgr_new.GetTavernTreasureMsg(extendinfopb)
        if orderInfo then
            local tavern_treasure_mgr = require "tavern_treasure_mgr"
            local pb_data = tavern_treasure_mgr.GetOrderDataByPb(orderInfo)
            tavern_treasure_mgr.SetChipItem(objTable.data.getNode, pb_data.getChipData, pb_data.getNum)
            tavern_treasure_mgr.SetChipItem(objTable.data.loseNode, pb_data.loseChipData, pb_data.loseNum)
            objTable.data.getCount.text = pb_data.getChipData and string.formatL(668101, util.PriceConvert(pb_data.getChipData.curCount)) or ""
            objTable.data.loseCount.text = pb_data.loseChipData and string.formatL(668101, util.PriceConvert(pb_data.loseChipData.curCount)) or ""
            objTable.data.changeBtn:SetEnable(pb_data.isMine or (pb_data.loseChipData and pb_data.loseChipData.curCount <= 0))

            objectData.clickExchangeFun = function()
                if orderInfo then
                    if not pb_data.isMine then
                        tavern_treasure_mgr.OnExchangeAlliance(pb_data.loseChipData, pb_data.getChipData, pb_data.orderId)
                    end
                end
            end
        end
    end

end
-------------惊喜盒分享类型---------------
function RenderSurPriseBag(chatContentList, objTable, data)
    local objectData = objTable.data
    local surprise_bag_data = require "surprise_bag_data"
    local chatData = surprise_bag_data.GetGoldenEggsChatDataByChatID(data.szChatID)
    -- objTable.data.titleText.text = string.format2("chatID:{%s1}、redPacketId:{%s2}",data.szChatID,chatData and chatData.redPacketId)
    -- log.Error(string.format2("chatID:{%s1}、redPacketId:{%s2}",data.szChatID,chatData and chatData.redPacketId))
    local function updateSurpriseShow(chatData)
        if chatData then
            local cfg = surprise_bag_data.GetBoxCfg(chatData.goldenEggsID)
            if cfg then
                local itemCfg = game_scheme:Item_0(chatData.goldenEggsID)
                if itemCfg then
                    objTable.data.titleText.text = lang.Get(itemCfg.nameKey)
                end
                objTable.data.Text_collect.text = string.format2("{%s1}/{%s2}", chatData.receiveNum, cfg.sum)
                Common_Util.SetActive(objTable.data.hasLikeText, chatData.like)
                local isMine = chatData.launchID == player_mgr.GetPlayerRoleID()
                local isMask = os.server_time() > chatData.expiraTime--chatData.receive or os.server_time() > chatData.expiraTime or chatData.receiveNum >= cfg.sum
                --if isMine then --自己发的过期再置灰
                --    isMask = os.server_time() > chatData.expiraTime
                --end
                Common_Util.SetActive(objTable.data.mask, isMask)
                objTable.data.boxIcon:Switch(chatData.receive and 1 or 0)
                objTable.data.txtLike.text = chatData.likePoint
            end
        else
            log.Error("获取不到金蛋数据", data.szChatID)
        end
    end
    updateSurpriseShow(chatData)
    objectData.clickSurpriseBagBtn = function()
        if chatData then
            local cfg = surprise_bag_data.GetBoxCfg(chatData.goldenEggsID)
            if cfg then
                local faceStr = data.faceId
                if data.faceStr and not string.IsNullOrEmpty(data.faceStr) then
                    faceStr = data.faceStr
                end
                local openData = {
                    goldenEggsID = chatData.goldenEggsID,
                    launchID = chatData.launchID,
                    name = data.name,
                    faceStr = faceStr,
                    frameid = data.avatarFrame,
                    expiraTime = chatData.expiraTime,
                    redPacketId = chatData.redPacketId,

                }
                if chatData.receive then
                    --如果已领取，弹出已领取弹窗
                    openData.openType = POP_OPEN_TYPE_ENUM.GETTED
                    ui_window_mgr:ShowModule("ui_surprise_bag_pop", nil, nil, openData)
                else
                    if chatData.expiraTime > os.server_time() then
                        --未领取弹出开启弹窗
                        if chatData.receiveNum >= cfg.sum then
                            openData.openType = POP_OPEN_TYPE_ENUM.None,
                            ui_window_mgr:ShowModule("ui_surprise_bag_pop", nil, nil, openData)
                            return
                        end
                        openData.openType = POP_OPEN_TYPE_ENUM.OPEN,
                        ui_window_mgr:ShowModule("ui_surprise_bag_pop", nil, nil, openData)
                    else
                        --弹领取明细
                        ui_window_mgr:ShowModule("ui_surprise_claim_details", nil, nil, { redPacketId = chatData.redPacketId })
                    end
                end
            end

        end
    end

    objectData.clickLikeBtnFun = function()
        local net_surprise_bag_module = require "net_surprise_bag_module"
        if chatData then
            net_surprise_bag_module.MSG_GOLDENEGGS_LIKE_REQ(chatData.redPacketId)
        end
    end

    objectData.updateEvent = function(eventName, redPacketId)
        if chatData and redPacketId == chatData.redPacketId then
            local _data = surprise_bag_data.GetGoldenEggsChatDataByRedPacketId(chatData.redPacketId)
            if _data then
                updateSurpriseShow(_data)
            end
        end
    end
    event.Register(surprise_bag_define.CHAT_VIEW_UPDATE, objectData.updateEvent)
end
------------------------------

-------------惊喜盒手气最佳类型---------------
function RenderSurPriseBagLuckly(chatContentList, objTable, data)
    local objectData = objTable.data
    if data.extendinfopb then
        local surprise_bag_data = require "surprise_bag_data"
        local extendinfopb = surprise_bag_data.GetGoldenEggsMaxRewardMsg(data.extendinfopb)
        local uiData = objTable.data
        uiData.Text_name.text = extendinfopb.playerName
        if extendinfopb.launchName and not string.empty(extendinfopb.launchName) then
            uiData.tips.text = string.format2(lang.Get(1005260), extendinfopb.launchName)
        end
        objTable.data.roleFace = face_item_new.CFaceItem():Init(uiData.headParent, nil, 1.1)
        if extendinfopb.playerFaceStr and not string.empty(extendinfopb.playerFaceStr) then
            objTable.data.roleFace:SetFaceInfo(extendinfopb.playerFaceStr)--iconUI:SetFaceInfo(faceID, clickEvent)
        end
        if extendinfopb.playerFrameid and not string.empty(extendinfopb.playerFrameid) then
            objTable.data.roleFace:SetFrameID(extendinfopb.playerFrameid, true)
        end

        objTable.data.clickBtnFun = function()
            ui_window_mgr:ShowModule("ui_surprise_claim_details", nil, nil, { redPacketId = extendinfopb.redPacketId })
        end
    end
end

-------------联盟分享类型---------------
function RenderAllianceShare(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.allianceShareObj.gameObject:SetActive(true)
    log.Warning("[lmyqh] RenderAllianceShare data", Edump(data))
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local ui_tools = require "ui_tools"
        local alliance_data = require "alliance_data"
        local allianceInfo = chat_mgr_new.GetAllianceShareMsg(extendinfopb)
        if allianceInfo then
            local alliance_pb = require "alliance_pb"
            objectData.allianceName.text = string.format("[%s]%s", allianceInfo.shortName, allianceInfo.allianceName)
            objectData.num.text = lang.Get(600264) .. string.format("%s/%s", allianceInfo.count, alliance_data.GetPeopleMaxLimit() or 100)
            objectData.lang.text = lang.Get(600265) .. lang.Get(allianceInfo.language)
            --老战力兼容
            if allianceInfo.newPower and allianceInfo.newPower > 0 then
                objectData.power.text = lang.Get(600266) .. ui_tools.format_with_thousands_separator(allianceInfo.newPower)
            else
                objectData.power.text = lang.Get(600266) .. ui_tools.format_with_thousands_separator(allianceInfo.power)
            end
            
            local allianceIconId
            local flagData = alliance_data.GetFlagIdData(allianceInfo.flag)
            if flagData then
                allianceIconId = flagData.iconID;
            end
            if chatContentList.leagueSpriteAsset then
                chatContentList.leagueSpriteAsset:GetSprite("qizhi" .. allianceIconId, function(sprite)
                    if sprite and objectData and not util.IsObjNull(objectData.flagIcon) then
                        objectData.flagIcon.sprite = sprite
                    end
                end)
            end
            local isAutoJoin = false
            local isApply = false
            local userData = alliance_data.GetUserAllianceData()
            if userData.allianceId and userData.allianceId == allianceInfo.allianceId then
                Common_Util.SetActive(objectData.lvLimit, false)
                Common_Util.SetActive(objectData.ceLimit, false)
            else
                local checkdata = alliance_data.CheckAlliancePlayer(allianceInfo)
                if allianceInfo.lvLimit and allianceInfo.lvLimit > 0 then
                    if not checkdata.isLevelLimit then
                        objectData.lvLimit.text = string.format("%s  <color=#FF4D2A>≥%s%s</color>", lang.Get(600261), allianceInfo.lvLimit, lang.Get(600347))
                    else
                        objectData.lvLimit.text = string.format("%s  ≥%s%s", lang.Get(600261), allianceInfo.lvLimit, lang.Get(600347))
                    end
                    Common_Util.SetActive(objectData.lvLimit, true)
                else
                    Common_Util.SetActive(objectData.lvLimit, false)
                end
                if allianceInfo.ceLimit and allianceInfo.ceLimit > 0 then
                    if not checkdata.IsPowerLimit then
                        objectData.ceLimit.text = string.format("%s  <color=#FF4D2A>≥%s</color>", lang.Get(600260), util.NumberWithUnit(allianceInfo.ceLimit))
                    else
                        objectData.ceLimit.text = string.format("%s  ≥%s", lang.Get(600260), util.NumberWithUnit(allianceInfo.ceLimit))
                    end
                    Common_Util.SetActive(objectData.ceLimit, true)
                else
                    Common_Util.SetActive(objectData.ceLimit, false)
                end
                isAutoJoin = (checkdata.isLevelLimit and checkdata.IsPowerLimit) and allianceInfo.applySet == alliance_pb.emAllianceApplyType_Auto
                isApply = (checkdata.isLevelLimit and checkdata.IsPowerLimit) and allianceInfo.applySet == alliance_pb.emAllianceApplyType_Apply
            end
            Common_Util.SetActive(objectData.btnJoin, isAutoJoin)
            Common_Util.SetActive(objectData.btnApply, isApply)
            Common_Util.SetActive(objectData.applyed, false)
            objectData.clickJoinFun = function()
                if userData.allianceId and userData.allianceId ~= 0 then
                    local flow_text = require "flow_text"
                    flow_text.Add(lang.Get(600600))
                    return
                end
                local alliance_mgr = require "alliance_mgr"
                alliance_mgr.ApplyAllianceReq(allianceInfo)
            end
        end
    end
end

-------------灭火活动---------------
function RenderGetLikeObj(chatContentList, objTable, data)
    local objectData = objTable.data
   --log.Error(data.context)
    if data.context and data.context~="" then
        objectData.tipText.text=lang.Get(tonumber(data.context))
    else
        objectData.tipText.text=""
    end
    shrinkTxt.auto_wrap_truncate(objectData.tipText,26,1)

end

-------------新聊天消息提示---------------
function RenderNotStyleObj(chatContentList, objTable, data)
    local objectData = objTable.data
    objectData.laborDayText.text=lang.Get(670037)
    shrinkTxt.auto_wrap_truncate(objectData.laborDayText)
end

---战报分享
function RenderBattleReportShareObj(chatContentList, objTable, data)
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local objData = objTable.data
        local reportInfo = chat_mgr_new.GetChatShareBattleReport(extendinfopb)
        local nameInfo=(reportInfo.nameInfoLangId and reportInfo.nameInfoLangId~=0)  and string.format2(reportInfo.nameInfo,lang.Get(reportInfo.nameInfoLangId)) or reportInfo.nameInfo
        objData.imgResult:Switch(reportInfo.bIsVic and 0 or 1)
        objData.txtDetail.text =string.format2(lang.Get(reportInfo.contentLangId),nameInfo) 
        --点击事件添加，请求战报数据流程
        objTable.data.clickSelf = function()
            battle_report_ext_helper.TryShowBattleReportPanel_Share(reportInfo.sId,reportInfo.subMailType)
        end
    end
end

function RenderDetectReportShareObj(chatContentList, objTable, data)
    local extendinfopb = data.extendinfopb
    if extendinfopb then
        local objData = objTable.data
        local reportInfo = chat_mgr_new.GetChatShareBattleReport(extendinfopb)
        local nameInfo=(reportInfo.nameInfoLangId and reportInfo.nameInfoLangId~=0)  and string.format2(reportInfo.nameInfo,lang.Get(reportInfo.nameInfoLangId)) or reportInfo.nameInfo
        objData.txtTeamCount.text=reportInfo.teamCount
        objData.txtTeamPower.text=string_util.NumberWithUnit1(reportInfo.teamPowerSum) 
        objData.txtDetail.text =string.format2(lang.Get(reportInfo.contentLangId),nameInfo)
        --点击事件添加，请求战报数据流程
        objTable.data.clickSelf = function()
            battle_report_ext_helper.TryShowBattleReportPanel_Share(reportInfo.sId,reportInfo.subMailType)
        end
    end
end

---@see 处理客服消息（客服消息以html格式发送）
function DealHTMLContent(content)
    local Html = require "Html"
    content = "<p>" .. content .. "</p>"
    content = string.sub(Html.ToString(Html.ParseHtmlText(content)), 2)
    content = string.gsub(content, " ", " ")
    content = string.gsub(content, "&nbsp;", " ")
    return content
end

function HandleAtName(chatContentList, objTable, data)
    local contentText = data.context
    local nameArr = chat_mgr_new.GetAtNamesByStr(data.context)
    local replacedFlag = {} --防止重名
    for _, name in pairs(nameArr) do
        if replacedFlag[name] == nil then
            replacedFlag[name] = true
            name = util.RefineGsubStr(name)
            local colorName = string.format("<color=#004CFFFF>%s</color>", name) -- colorName需要直接用name格式化，以保证gsub时成功替换
            if string.find(name, '%%') then
                contentText = util.Replace(contentText, name, colorName)
            else
                name = string.gsub(name, "[%]%-%^%.[()$*+?]", function(c)
                    if string.len(c) == 0 then
                        return c
                    end ;
                    return "%" .. c
                end)
                contentText = string.gsub(contentText, name, colorName)
            end
        end
    end
    return contentText
end

function HandleShareSkin(chatContentList, objTable, data)
    local contentText
    local skinID = tonumber(data.context)
    local skin_cfg = skinID and skinID > 0 and game_scheme:Skin_0(skinID)
    if skin_cfg then
        local cfg = game_scheme:Hero_0(skin_cfg.HeroID)
        contentText = "<color=#%s>[%s · " .. (lang.Get(cfg and cfg.HeroNameID) or "") .. "]</color>"

        local gw_hero_mgr = require "gw_hero_mgr"
        local color = gw_hero_mgr.Hero_Skin_Color[skin_cfg.SkinType]
        contentText = string.format(contentText, color, lang.Get(skin_cfg.SkinNameID))

        objTable.data.conClickFun = function()
            ui_window_mgr:ShowModule("ui_hero_skin_share")
            local ui_hero_skin_share = require "ui_hero_skin_share"
            ui_hero_skin_share.OpenShareViewMode(skinID)
        end
    else
        contentText = ""
    end
    return contentText
end

function HandleShareHero(chatContentList, objTable, data)
    local contentText
    local cfg = game_scheme:Hero_0(data.hero.heroId)

    contentText = "<color=#%s>[%s · " .. (lang.Get(cfg and cfg.HeroNameID) or "") .. "]</color>"

    objTable.data.bgColor = color_palette.HexToColor("123A5E")

    local gw_hero_mgr = require "gw_hero_mgr"
    local color = gw_hero_mgr.Hero_Star_Color[data.hero.starLv]

    if data.hero.skinId and data.hero.skinId ~= 0 then
        local skin_cfg = game_scheme:Skin_0(data.hero.skinId)
        if skin_cfg then
            contentText = string.format(contentText, color, lang.Get(skin_cfg.SkinNameID))
        else
            contentText = string.format(contentText, color, cfg and lang.Get(cfg.nameID) or "")
        end
    else
        contentText = string.format(contentText, color, cfg and lang.Get(cfg.nameID) or "")
    end

    objTable.data.conClickFun = function()
        local windowMgr = require "ui_window_mgr"
        local window = windowMgr:ShowModule("ui_hero_share_new")
        if window then
            window:SetInputParam({
                {
                    palID = data.hero.heroId,
                    level = data.hero.lv,
                    starLevel = data.hero.starLv,
                    llCurHp = data.hero.hp,
                    power = data.hero.ce,
                    attack = data.hero.attack,
                    defence = data.hero.defence,
                    speed = data.hero.speed,
                    equip = data.hero.equip,
                    skinID = data.hero.skinId,
                    awakeSkillID = data.hero.awakeSkillID,
                    awakeSkillLV = data.hero.awakeSkillLv1,
                    --TODO新增数据
                    decorate = data.hero.decorate, --祝福和饰品数据
                    nDecorateAddCE = data.hero.nDecorateAddCE, --穿戴饰品增加的战斗力

                    weaponDiamond = data.hero.weaponData, --原力武器数据
                    weaponDiamondCE = data.hero.nWeaponAddCE, --原力武器战力数据
                    bTrialHero = data.hero.bTrialHero --是否是试用英雄
                }
            }, nil, {
                name = data.name,
                ce = data.ce,
                icon = data.faceId,
                frame = data.roleid,
                lv = data.roleLv,
            })
        end
    end
    return contentText
end

function HandleShareGoods(chatContentList, objTable, data)
    local cfg = game_scheme:Item_0(data.goods.goodsId)
    local contentText = ""
    if cfg then
        contentText = "<color=#D0060FFF>[" .. (lang.Get(cfg.nameKey) or "") .. "]</color>"
    else
        contentText = "<color=#D0060FFF>[]</color>"
        log.Error("Item.csv not find", data.goods.goodsId)
    end
    objTable.data.conClickFun = function()
        iui_item_detail.Show(data.goods.goodsId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end
    return contentText
end

function RenderFarmLottery(chatContentList, objTable, data)
    --家园分享抽奖
    local reward_mgr = require "reward_mgr"
    local reward_data = reward_mgr.GetRewardGoods(data.farmLottery.heroData.goodsId)
    local cfg = game_scheme:Item_0(reward_data.id)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
    objTable.data.shareGoodObj:SetActive(true)
    objTable.data.shareGoodName.text = string.format(lang.Get(16978), cfg and lang.Get(cfg.nameKey) or "")

    if openNew then
        objTable.data.clickShareGood = function()
            iui_item_detail.Show(reward_data.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, reward_data.num, nil, nil, data.farmLottery.heroData.goodsId)
        end
    else
        objTable.data.shareGoodBtn.onClick:RemoveAllListeners()
        local clickfun = function()
            iui_item_detail.Show(reward_data.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, reward_data.num, nil, nil, data.farmLottery.heroData.goodsId)
        end
        objTable.data.shareGoodBtn.onClick:AddListener(clickfun)
    end

    local goods_item = require "goods_item_new"

    objTable.data.shareGoodIconUI = objTable.data.shareGoodIconUI or goods_item.CGoodsItem():Init(objTable.data.shareGoodRoot.transform, nil, 0.55)
    objTable.data.shareGoodIconUI:SetGoods(nil, reward_data.id, reward_data.num, function()
        iui_item_detail.Show(reward_data.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, reward_data.num, nil, nil, data.farmLottery.heroData.goodsId)
    end)
end

function RenderShareDecorate(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn:SetActive(false)
    end
    objTable.data.starGroup:SetActive(false)
    objTable.data.equipScore:SetActive(false)
    objTable.data.equipObj:SetActive(true)

    local cfg = game_scheme:Item_0(data.goods.goodsId)
    local prefix = ""
    objTable.data.equipName.text = "<color=#%s>[" .. (cfg and prefix .. lang.Get(cfg.nameKey) or "") .. "]</color>"
    local decoration_item = require "decoration_item"
    objTable.data.equipIconUI = objTable.data.equipIconUI or decoration_item.CDecorationItem():Init(objTable.data.equipRoot.transform, nil, 0.55) -- 不能重用，切换频道几次后会报错
    objTable.data.equipIconUI:SetGoods(nil, data.goods.goodsId, 1)

    if not openNew then
        objTable.data.equipBtn.onClick:RemoveAllListeners()
        local clickfun = function()
            local win = ui_window_mgr:ShowModule("ui_decoration_detail")
            if win then
                local const = require "const"
                win:SetInputParam(nil, data.goods.goodsId, nil, nil, data.goods.lv, const.EnterType.chat)--需要饰品ID和对应槽位等级
            end
        end
        objTable.data.equipBtn.onClick:AddListener(clickfun)
    else
        objTable.data.clickEquipment = function()
            local win = ui_window_mgr:ShowModule("ui_decoration_detail")
            if win then
                local const = require "const"
                win:SetInputParam(nil, data.goods.goodsId, nil, nil, data.goods.lv, const.EnterType.chat)--需要饰品ID和对应槽位等级
            end
        end
    end
end

function RenderShareTreasureRare(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
    objTable.data.starGroup:SetActive(false)
    objTable.data.equipScore.gameObject:SetActive(false)
    objTable.data.equipObj.gameObject:SetActive(true)

    local cfg = game_scheme:Item_0(data.goods.goodsId)
    local prefix = ""
    objTable.data.equipName.text = "<color=#%s>[" .. (cfg and prefix .. lang.Get(cfg.nameKey) or "") .. "]</color>"
    --objTable.data.equipIconUI = decoration_item.CDecorationItem():Init(objTable.data.equipRoot.transform, nil, 0.55) -- 不能重用，切换频道几次后会报错
    local goods_item = require "goods_item_new"
    objTable.data.equipIconUI = goods_item.CGoodsItem():Init(objTable.data.equipRoot.transform, function(p)
        if not p then
            return
        end
        p:DisplayInfo()
    end, 0.55)
    if objTable.data.equipIconUI then
        objTable.data.equipIconUI:SetFrameBg(3)
        objTable.data.equipIconUI:SetCountEnable(false)
        objTable.data.equipIconUI:SetQualityImg(true)
        objTable.data.equipIconUI:SetGoods(nil, data.goods.goodsId)
        -- objTable.data.equipIconUI:SetSigilInfo({ enhanceLv = data.goods.lv })
    end

    if not openNew then
        local clickfun = function()
            local win = ui_window_mgr:ShowModule("ui_treasure_rare_detail")
            if win then
                win:SetInputParam(data.goods.goodsId, true)
            end
        end
        objTable.data.equipBtn.onClick:AddListener(clickfun)
    else
        objTable.data.clickEquipment = function()
            local win = ui_window_mgr:ShowModule("ui_treasure_rare_detail")
            if win then
                win:SetInputParam(data.goods.goodsId, true)
            end
        end
    end
end

function HandleAnniversaryJackpock(chatContentList, objTable, data)
    local goodsid = data.goods.goodsId
    local cfg = game_scheme:Item_0(goodsid)
    local contentText = lang.Get(382176) .. "<color=#D0060FFF>[" .. ((cfg and lang.Get(cfg.nameKey)) or "") .. "]</color>" --
    objTable.data.conClickFun = function()
        iui_item_detail.Show(data.goods.goodsId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end
    return contentText
end

function RenderShareEquipment(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
    objTable.data.starGroup:SetActive(false)
    objTable.data.equipObj.gameObject:SetActive(true)
    if not openNew then
        objTable.data.equipBtn.onClick:RemoveAllListeners()
    end

    objTable.data.bgColor = color_palette.HexToColor("123A5E")

    local itemEntity = nil
    local cfg = game_scheme:Item_0(data.goods.goodsId)
    if cfg and cfg.type == item_data.Item_Type_Enum.Sigil then
        local prefix = ""
        prefix = cfg and lang.Get(cfg.nameKey) or ""
        objTable.data.equipName.text = "<color=#%s>[" .. (cfg and lang.Get(cfg.nameKey) or "") .. "]</color>"
        local sigilCfg = game_scheme:Sigil_0(data.goods.goodsId)
        if sigilCfg then
            local grade = sigilCfg.grade - 50
            local color = item_data.Sigil_Name_Color[grade]
            objTable.data.equipName.text = string.format(objTable.data.equipName.text, color)
            itemEntity = {
                goodsID = data.goods.goodsId,
                enhanceLv = data.goods.lv, -- 强化等级
                baseProp1 = sigilCfg.proPool.data[0], -- 基础词条1
                baseProp2 = nil, -- 基础词条2
                baseProp3 = nil, -- 基础词条3
                baseProp4 = nil, -- 基础词条4
                extendProp1 = nil, -- 额外词条1
                extendProp2 = nil, -- 额外词条2
                extendProp3 = nil, -- 额外词条3
                extendProp4 = nil,
            }
            if data.goods.proId then
                local index = 1
                for k, v in ipairs(data.goods.proId) do
                    if v and v ~= 0 then
                        itemEntity['extendProp' .. index] = v
                        index = index + 1
                    end
                end
            end

            for i = 1, 6 do
                local star = objTable.data.starGroup.transform:GetChild(i - 1)
                if star then
                    local isShow = grade >= i
                    star.gameObject:SetActive(isShow)
                end
            end
        end

        objTable.data.starGroup:SetActive(true)

        local goods_item = require "goods_item_new"
        objTable.data.equipIconUI = (objTable.data.equipIconUI and not util.IsObjNull(objTable.data.equipIconUI)) or goods_item.CGoodsItem():Init(objTable.data.equipRoot.transform, function(p)
            if not p then
                return
            end
            p:DisplayInfo()
        end, 0.55)
        if objTable.data.equipIconUI then
            objTable.data.equipIconUI:SetFrameBg(3)
            objTable.data.equipIconUI:SetCountEnable(false)
            objTable.data.equipIconUI:SetQualityImg(true)
            objTable.data.equipIconUI:SetGoods(nil, data.goods.goodsId)
            -- objTable.data.equipIconUI:SetSigilInfo({ enhanceLv = data.goods.lv })
        end
        objTable.data.equipScore.gameObject:SetActive(false)
    else
        local prefix = ""
        local resonance_cfg = game_scheme:EquipmentResonance_0(data.goods.resonanceproId)
        if resonance_cfg then
            prefix = lang.Get(resonance_cfg.prefix)
        end

        objTable.data.equipName.text = "<color=#%s>[" .. (cfg and prefix .. lang.Get(cfg.nameKey) or "") .. "]</color>"
        local cfg_Info = game_scheme:Equipment_0(data.goods.goodsId)
        if cfg_Info then
            local color = item_data.Equipment_Name_Color[cfg_Info.grade]
            objTable.data.equipName.text = string.format(objTable.data.equipName.text, color)
        else
            objTable.data.equipName.text = string.format(objTable.data.equipName.text, "19448DFF")
        end

        local equipment_mgr = require "equipment_mgr"
        local PropCount = 0
        local totleScore = 0
        local enhanceLv = 0
        if data.goods.proId then
            for k, prop in ipairs(data.goods.proId) do
                if k == 5 then
                    enhanceLv = prop
                end
                if k > 5 then
                    local cfg1 = game_scheme:EquipmentPro_0(prop)
                    if cfg1.Score then
                        PropCount = PropCount + 1
                        totleScore = totleScore + cfg1.Score
                    end
                end
            end
        end
        local activeCount = equipment_mgr.GetEnhancePropUnlockCountByItemId(data.goods.goodsId, enhanceLv) or 0
        if totleScore > 0 and PropCount > 0 then
            objTable.data.equipScore.gameObject:SetActive(activeCount >= PropCount)
            if activeCount >= PropCount then
                local avgScore = totleScore / PropCount
                local section = equipment_mgr.getEquipScoreSection(avgScore, true)
                objTable.data.equipScoreIcon:Switch(section)
            end
        else
            objTable.data.equipScore.gameObject:SetActive(false)
        end

        local goods_item = require "goods_item_new"
        objTable.data.equipIconUI = (objTable.data.equipIconUI and not util.IsObjNull(objTable.data.equipIconUI)) or goods_item.CGoodsItem():Init(objTable.data.equipRoot.transform, function(p)
            if not p then
                return
            end
            p:DisplayInfo()
        end, 0.55)
        if objTable.data.equipIconUI then
            objTable.data.equipIconUI:SetCountEnable(false)
            objTable.data.equipIconUI:SetEquipLvTxt(data.goods.lv)
            objTable.data.equipIconUI:SetGoods(nil, data.goods.goodsId)
            objTable.data.equipIconUI:SetFrameBg(3)
        end
    end

    if not openNew then
        objTable.data.equipBtn.onClick:RemoveAllListeners()
        local clickfun = function()
            if cfg and cfg.type == item_data.Item_Type_Enum.Sigil then
                local ui_sigil_detail = require "ui_sigil_detail"
                ui_sigil_detail.SetShareItemData(itemEntity)
                ui_window_mgr:ShowModule("ui_sigil_detail")
            else
                local cfg_Info = game_scheme:Equipment_0(data.goods.goodsId)
                local mixGrade = game_scheme:InitBattleProp_0(917).szParam.data[0] + 1
                if cfg_Info.grade >= mixGrade then
                    local ui_equip_share_pro = require "ui_equip_share_pro"
                    ui_equip_share_pro.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId)
                    ui_window_mgr:ShowModule("ui_equip_share_pro")
                else
                    local ui_equip_share = require "ui_equip_share"
                    ui_equip_share.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId)
                    ui_window_mgr:ShowModule("ui_equip_share")
                end
            end
        end
        objTable.data.equipBtn.onClick:AddListener(clickfun)
    else
        objTable.data.clickEquipment = function()
            if cfg and cfg.type == item_data.Item_Type_Enum.Sigil then
                local ui_sigil_detail = require "ui_sigil_detail"
                ui_sigil_detail.SetShareItemData(itemEntity)
                ui_window_mgr:ShowModule("ui_sigil_detail")
            else
                local cfg_Info = game_scheme:Equipment_0(data.goods.goodsId)
                local mixGrade = game_scheme:InitBattleProp_0(917).szParam.data[0] + 1
                if cfg_Info.grade >= mixGrade then
                    local ui_equip_share_pro = require "ui_equip_share_pro"
                    ui_equip_share_pro.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId, data.goods.profession)
                    ui_window_mgr:ShowModule("ui_equip_share_pro")
                else
                    local ui_equip_share = require "ui_equip_share"
                    ui_equip_share.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId)
                    ui_window_mgr:ShowModule("ui_equip_share")
                end
            end
        end
    end
end

function DisposeShareEquipment(chatContentList, objTable)
    if objTable.data.equipIconUI then
        objTable.data.equipIconUI:Dispose()
        objTable.data.equipIconUI = nil
    end
end

function RenderShareEquipmentRecast(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
        objTable.data.equipRecastBtn.onClick:RemoveAllListeners()
    end
    objTable.data.equipRecastObj.gameObject:SetActive(true)

    local cfg = game_scheme:Item_0(data.goods.goodsId)
    local cfg_Info = game_scheme:Equipment_0(data.goods.goodsId)

    local langId = 0
    if data.sType == mq_common_pb.enSpeak_ShareEquipRecast then
        objTable.data.bgColor = color_palette.HexToColor("123A5E")
        langId = 182150
    elseif data.sType == mq_common_pb.enSpeak_ShareEquipResonace then
        langId = 186028
    end
    local prefix = ""
    local resonance_cfg = game_scheme:EquipmentResonance_0(data.goods.resonanceproId)
    if resonance_cfg then
        prefix = lang.Get(resonance_cfg.prefix)
    end
    objTable.data.equipRecastContent.text = lang.Get(langId) .. "<color=#%s>[" .. (cfg and prefix .. lang.Get(cfg.nameKey) or "") .. "]</color>"
    if cfg_Info then
        local color = item_data.Equipment_Name_Color[cfg_Info.grade]
        objTable.data.equipRecastContent.text = string.format(objTable.data.equipRecastContent.text, color)
    else
        objTable.data.equipRecastContent.text = string.format(objTable.data.equipRecastContent.text, "19448DFF")
    end
    local equipment_mgr = require "equipment_mgr"
    local PropCount = 0
    local totleScore = 0
    local enhanceLv = 0
    if data.goods.proId then
        for k, prop in ipairs(data.goods.proId) do
            if k == 5 then
                enhanceLv = prop
            end
            if k > 5 then
                local cfg = game_scheme:EquipmentPro_0(prop)
                if cfg.Score then
                    PropCount = PropCount + 1
                    totleScore = totleScore + cfg.Score
                end
            end
        end
    end
    local activeCount = equipment_mgr.GetEnhancePropUnlockCountByItemId(data.goods.goodsId, enhanceLv) or 0
    if totleScore > 0 and PropCount > 0 then
        objTable.data.equipRecastScore.gameObject:SetActive(activeCount >= PropCount)
        if activeCount >= PropCount then
            local avgScore = totleScore / PropCount
            local section = equipment_mgr.getEquipScoreSection(avgScore, true)
            objTable.data.equipRecastScoreIcon:Switch(section)
        end
    else
        objTable.data.equipRecastScore.gameObject:SetActive(false)
    end

    local goods_item = require "goods_item_new"
    objTable.data.equipRecastIconUI = objTable.data.equipRecastIconUI or goods_item.CGoodsItem():Init(objTable.data.equipRecastRoot.transform, nil, 0.55)
    objTable.data.equipRecastIconUI:SetCountEnable(false)
    objTable.data.equipRecastIconUI:SetEquipLvTxt(data.goods.lv)
    objTable.data.equipRecastIconUI:SetGoods(nil, data.goods.goodsId)
    objTable.data.equipRecastIconUI:SetFrameBg(3)

    if not openNew then
        local clickfun = function()
            local mixGrade = game_scheme:InitBattleProp_0(917).szParam.data[0] + 1
            if cfg_Info.grade >= mixGrade then
                local ui_equip_share_pro = require "ui_equip_share_pro"
                ui_equip_share_pro.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId, data.goods.profession)
                ui_window_mgr:ShowModule("ui_equip_share_pro")
            else
                local ui_equip_share = require "ui_equip_share"
                ui_equip_share.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId)
                ui_window_mgr:ShowModule("ui_equip_share")
            end
        end
        objTable.data.equipRecastBtn.onClick:AddListener(clickfun)
    else
        objTable.data.clickEquipmentRecast = function()
            local mixGrade = game_scheme:InitBattleProp_0(917).szParam.data[0] + 1
            if cfg_Info.grade >= mixGrade then
                local ui_equip_share_pro = require "ui_equip_share_pro"
                ui_equip_share_pro.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId, data.goods.profession)
                ui_window_mgr:ShowModule("ui_equip_share_pro")
            else
                local ui_equip_share = require "ui_equip_share"
                ui_equip_share.SetInputData(data.goods.goodsId, data.goods.lv, data.goods.proId, data.goods.resonanceproId)
                ui_window_mgr:ShowModule("ui_equip_share")
            end
        end
    end
end

function DisposeShareEquipmentRecast(chatContentList, objTable)
    if objTable.data.equipRecastIconUI then
        objTable.data.equipRecastIconUI:Dispose()
        objTable.data.equipRecastIconUI = nil
    end
end

function HandleShareLottery(chatContentList, objTable, data)
    --分享抽卡
    objTable.data.conClickFun = function()
        if data.shareLottery then
            local ui_share_lottery = require "ui_share_lottery"
            ui_window_mgr:ShowModule("ui_share_lottery")
            ui_share_lottery.SetShareLotteryData(data.shareLottery)
        end
    end
    if data.shareLottery then
        local contentText
        contentText = "<color=#FF0000FF>%s</color>"
        if data.shareLottery.nLotteryType == 1 then
            --单抽
            contentText = string.format(contentText, lang.Get(16082))
        elseif data.shareLottery.nLotteryType == 2 then
            --十连抽
            contentText = string.format(contentText, lang.Get(16083))
        end
        contentText = string.format(contentText, data.shareLottery.nScore)
        return contentText
    end
end

function HandleShareBattle(chatContentList, objTable, data)
    local context = util.SplitString(data.context, "#")
    local contentText = ""
    if #context == 1 or (#context > 1 and (context[2] == "1" or context[2] == "")) then
        contentText = lang.Get(18513)
    else
        local opponentName = context[2]
        local winFlag = tonumber(context[3])
        contentText = string.format(lang.Get(winFlag == 1 and 18501 or 18502), opponentName)
    end
    objTable.data.conClickFun = function()

        local net_arena_module = require "net_arena_module"
        local url = net_arena_module.GetReplayWebUrl(context[1])
        http_inst.req_raw(string.format(url, context[1]), function(bytes, hasError)
            if hasError then
                flow_text.Add(lang.Get(18514))
                return
            end

            ui_window_mgr:CloseAll()
            ui_window_mgr:ShowModule("ui_loading")

            local battle_data = require "battle_data"
            local roleName = player_mgr.GetRoleName()
            battle_data.SetRoleID((data.name == roleName or context[2] == roleName) and player_mgr.GetPlayerRoleID() or data.roleid)
            battle_data.skipBattle = false

            local battle_manager = require "battle_manager"
            if context[4] ~= "" then
                if tonumber(context[4]) == common_new_pb.Arena then
                    --类型是竞技场的非多队类型就处理为单人类型
                    battle_manager.SetBattleType(common_new_pb.CrystalCrown)
                    battle_manager.SetSkipResult(false)
                    battle_manager.SetReturnArean(false)
                    net_arena_module.Send_ARENA_GET_RECORD_ID(common_new_pb.CrystalCrown, data.roleid, context[1])
                end
            end
            battle_manager.SetIsReplay(true)
            if context[5] ~= 0 then
                battle_manager.SetBattleRecordData(context[1], context[2], tonumber(context[5]))
            else
                battle_manager.SetBattleRecordData(context[1], context[2])
            end
            -- if context[6] and context[6] ~= "" then
            --     local fleet_expedition_data = require "fleet_expedition_data"
            --     fleet_expedition_data.SetBattleStar(context[6])
            -- end

            --初始化多队结算战斗数据
            local ui_battle_result = require "ui_battle_result"
            ui_battle_result.SetBattleIDs(nil)
            battle_manager.OnBattleReportReceived(context[4] ~= "" and tonumber(context[4]) or common_new_pb.Compete, bytes, false, function()
                util.DelayCall(0.2, function()
                    ui_window_mgr:ShowModule("ui_chat_main_new")

                end)

                --ui_window_mgr:ShowModule("ui_lobby", function()
                --end)

                battle_data.SetRoleID(nil)
                battle_manager.SetIsReplay(false)
            end)
            local winFlag = tonumber(context[3])
            battle_data.victory = (winFlag == 1)
        end)
    end
    objTable.data.pendant_luxiang.gameObject:SetActive(true)
    return contentText
end

function HandleVoidAreanSuc(chatContentList, objTable, data)
    local voidInfo = game_scheme:VoidArena_0(data.voidArenaID)
    if voidInfo == nil then
        return
    end
    local contentText
    local context = util.SplitString(data.context, "#")
    objTable.data.fightClickFun = function()
        local message_box = require "message_box"
        local laymain_data = require "laymain_data"
        local passLevel = laymain_data.GetPassLevel()
        local level = game_scheme:InitBattleProp_0(584).szParam.data[1]
        if tonumber(passLevel) < tonumber(level) then
            message_box.Open(lang.Get(103361), message_box.STYLE_YES, function()
            end, 0, lang.KEY_OK)
            return
        end

        message_box.Open(lang.Get(19338), message_box.STYLE_YESNO,
                function(callbackData, nRet)
                    if message_box.RESULT_YES == nRet then
                        local menu_bot_data = require "menu_bot_data"
                        menu_bot_data.OpenRiskPage()
                        ui_window_mgr:ShowModule("ui_lobby")
                        ui_window_mgr:ShowModule("ui_void_ring")
                        ui_window_mgr:ShowModule("ui_void_ring_content")
                    end
                end, 0, lang.KEY_OK, lang.KEY_CANCEL)
    end
    if #context == 1 or (#context > 1 and (context[2] == "1" or context[2] == "")) then
        --contentText = lang.Get(18513)
        local titleInfo = game_scheme:RoleTitle_0(voidInfo.TitleItemID)
        contentText = string.format(lang.Get(19316), lang.Get(titleInfo.NameID))

    else
        local opponentName = context[2]
        --local winFlag = tonumber(context[3])
        contentText = string.format(lang.Get(true and 18501 or 18502), opponentName)
    end
    objTable.data.conClickFun = function()
        local net_arena_module = require "net_arena_module"
        local url = net_arena_module.GetReplayWebUrl(context[1])
        print("enSpeak_VoidAreanSuc 战斗分享url=", string.format(url, context[1]))
        http_inst.req_raw(string.format(url, context[1]), function(bytes, hasError)
            if hasError then
                flow_text.Add(lang.Get(18514))
                return
            end

            ui_window_mgr:CloseAll()
            ui_window_mgr:ShowModule("ui_loading")

            local battle_data = require "battle_data"
            local roleName = player_mgr.GetRoleName()
            battle_data.SetRoleID((data.name == roleName or context[2] == roleName) and player_mgr.GetPlayerRoleID() or data.roleid)
            battle_data.skipBattle = false
            local battle_manager = require "battle_manager"
            if context[4] ~= "" then
                if tonumber(context[4]) == common_new_pb.Arena then
                    --类型是竞技场的非多队类型就处理为单人类型
                    battle_manager.SetBattleType(common_new_pb.CrystalCrown)
                    battle_manager.SetSkipResult(false)
                    battle_manager.SetReturnArean(false)
                    net_arena_module.Send_ARENA_GET_RECORD_ID(common_new_pb.CrystalCrown, data.roleid, context[1])
                end
            end
            battle_manager.SetIsReplay(true)
            if context[5] ~= 0 then
                battle_manager.SetBattleRecordData(context[1], context[2], tonumber(context[5]))
            else
                battle_manager.SetBattleRecordData(context[1], context[2])
            end

            battle_manager.OnBattleReportReceived(context[4] ~= "" and tonumber(context[4]) or common_new_pb.Compete, bytes, false, function()
                local menu_bot_data = require "menu_bot_data"
                menu_bot_data.OpenChatPage()
                battle_data.SetRoleID(nil)
                battle_manager.SetIsReplay(false)
            end)
            battle_data.victory = true--(winFlag == 1)
        end)
    end
    --分享  虚空挑战成功
    objTable.data.pendant_luxiang.gameObject:SetActive(true)
    objTable.data.pendant_fightbtn.gameObject:SetActive(true)
    return contentText
end

function HandleShareMultiBattle(chatContentList, objTable, data)
    local chat_pb = require "chat_pb"
    local msg = chat_pb.tShareMultiBattleMsg()
    msg:ParseFromString(data.context)
    local contentText = string.format(lang.Get(msg.isVictory and 18501 or 18502), msg.defencePlayerInfo.name)

    objTable.data.conClickFun = function()
        -- 获取多场战斗id 请求战斗数据 显示总结算
        local net_arena_module = require "net_arena_module"
        local finallizeCallBack = function()
            ui_window_mgr:ShowModule("ui_chat_main_new")
        end
        local localRecordCount = net_arena_module.ReplayMultiTbs(msg.battleIds, finallizeCallBack)
        local startPlayRecord = function()
            local ui_multi_battle_result = require "ui_multi_battle_result"
            ui_multi_battle_result.SetRivalInfo({ msg.defencePlayerInfo }, msg.attackPlayerInfo)
            ui_multi_battle_result.SetArenaRsp(msg.areaRsp, msg.isVictory)
            local ui_multi_win_lose = require("ui_multi_win_lose")
            ui_multi_win_lose.SetRivalInfo({ msg.defencePlayerInfo }, msg.attackPlayerInfo)
            ui_window_mgr:CloseAll({ ui_multi_battle_result = 1 })
        end
        if localRecordCount == 0 then
            -- 请求到数据后直接打开结算界面
            net_arena_module.RequestOssMultiTbs(msg.battleIds, startPlayRecord, finallizeCallBack)
        else
            startPlayRecord()
        end

    end
    objTable.data.pendant_luxiang.gameObject:SetActive(true)
    return contentText
end

function HandleSharePitfall(chatContentList, objTable, data)
    return string.format(lang.Get(15922), data.context)
end

function HandleOccupyCell(chatContentList, objTable, data)
    if util.IsNullOrEmpty(data.context) then
        return ""
    end
    local context = util.SplitString(data.context, "#")
    local cfg = game_scheme:LeagueCompCell_0(tonumber(context[1]), tonumber(context[2]))
    local template = "%s(%d,%d)"
    local str = string.format(template, lang.Get(cfg.nDescID), tonumber(context[3]), tonumber(context[4]))
    local contentText = string.format(lang.Get(18530), str)
    objTable.data.conClickFun = function()
        LocateToSociatyFight(tonumber(context[3]), tonumber(context[4]))
    end
    return contentText
end

function HandleGetBossFirstBlood(chatContentList, objTable, data)
    if util.IsNullOrEmpty(data.context) then
        return ""
    end
    local context = util.SplitString(data.context, "#")
    local template = "(%d,%d)"
    local str = string.format(template, tonumber(context[1]), tonumber(context[2]))
    local contentText = string.format(lang.Get(18531), str)
    objTable.data.conClickFun = function()
        LocateToSociatyFight(tonumber(context[1]), tonumber(context[2]))
    end
    return contentText
end

function HandleOfficialMark(chatContentList, objTable, data)
    local context = util.SplitString(data.context, "#")
    local cfg = game_scheme:LeagueCompCell_0(tonumber(context[1]), tonumber(context[2]))
    local template = "%s(%d,%d)"
    local str = string.format(template, lang.Get(cfg.nDescID), tonumber(context[3]), tonumber(context[4]))
    local contentText = string.format(lang.Get(18532), str)
    objTable.data.conClickFun = function()
        LocateToSociatyFight(tonumber(context[3]), tonumber(context[4]))
    end
    return contentText
end

function HandleShareCell(chatContentList, objTable, data)
    local context = util.SplitString(data.context, "#", tonumber)
    -- local sociaty_fight = require "sociaty_fight"
    -- local template = sociaty_fight.GetShareTemplate(context[1])
    local length = #context
    local param = {}
    for i = 2, length do
        table.insert(param, context[i])
    end

    local str = ""
    -- if template and #param > 0 then
    --     str = string.format(template, unpack(param))
    -- end

    objTable.data.conClickFun = function()
        LocateToSociatyFight(tonumber(context[length - 1]), tonumber(context[length]))
    end
    return str
end

function HandleOccupyFortress(chatContentList, objTable, data)
    return string.format(lang.Get(15860), data.context)
end

function HandleLangText(chatContentList, objTable, data)
    local langData = data.langText
    local param = {}
    for k, v in ipairs(langData.arrLangParams) do
        table.insert(param, v)
    end
    local template = lang.Get(langData.iLangId)
    return string.format(template, unpack(param))
end

function HandleLeagueCompete(chatContentList, objTable, data)
    local context = util.SplitString(data.context, "#")
    local battleid = nil

    if battleid then
        objTable.data.conClickFun = function()
            local net_arena_module = require "net_arena_module"
            local url = net_arena_module.GetReplayWebUrl(battleid)
            http_inst.req_raw(string.format(url, battleid), function(bytes, hasError)
                if hasError then
                    flow_text.Add(lang.Get(18514))
                    return
                end
                local battle_data = require "battle_data"
                battle_data.SetRoleID(data.roleid)
                battle_data.skipBattle = false
                local battle_manager = require "battle_manager"
                battle_manager.SetIsReplay(true)

                ui_window_mgr:CloseAll()
                ui_window_mgr:ShowModule("ui_loading")
                battle_manager.OnBattleReportReceived(common_new_pb.Compete, bytes, false, function()

                    ui_window_mgr:ShowModule("ui_lobby", function()
                        util.DelayCall(0.2, function()
                            ui_window_mgr:ShowModule("ui_chat_main_new")
                        end)
                    end)
                    battle_data.SetRoleID(nil)
                    battle_manager.SetIsReplay(false)
                end)
            end)
        end
    end
    objTable.data.pendant_luxiang.gameObject:SetActive(true)
    if #context == 10 then
        battleid = context[8]
        local winlose = tonumber(context[7])
        return string.format(lang.Get(winlose == 1 and 18501 or 18502), context[10])
    end
    return ""
end

function HandleSendFlower(chatContentList, objTable, data)
    local contentText = string.format(lang.Get(15999), data.context)
    objTable.data.conClickFun = function()
        -- if explore_mgr.GetFlowerNum() == 0 then
        --     flow_text.Add(lang.Get(15915))
        --     return
        -- end
        -- if data.roleid == player_mgr.GetPlayerRoleID() then
        --     flow_text.Add(lang.Get(15916))
        --     return
        -- end
        -- if explore_mgr.GetTotalSendFlower() >= explore_mgr.GetTotalSendFlowerLimit() then
        --     flow_text.Add(lang.Get(101201))
        --     return
        -- end
        -- local memberInfo = explore_mgr.GetMemberDataByRoleID(data.roleid)
        -- if memberInfo and memberInfo.todayFlower == explore_mgr.GetSendFlowerLimit() then
        --     flow_text.Add(lang.Get(15917))
        --     return
        -- end
        -- local net_explore_module = require "net_explore_module"
        -- net_explore_module.Send_SEND_FLOWERS_TO_EACH_OTHER(data.roleid, data.name, 1)

        objTable.data.xianhuaEff.gameObject:SetActive(false)
        objTable.data.xianhuaEff.gameObject:SetActive(true)
    end
    objTable.data.pendant_xianhua.gameObject:SetActive(true)
    return contentText
end

function HandleVoice(chatContentList, objTable, data)
    return "暂无语音"
end

function RenderLike(chatContentList, objTable, data)
    objTable.data.likeLogo.gameObject:SetActive(true)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
end

function RenderLeagueRecruit(chatContentList, objTable, data)
    if data.leagueRecruit then
        objTable.data.unionObj.gameObject:SetActive(true)
        if not openNew then
            objTable.data.contentBtn.gameObject:SetActive(false)
            objTable.data.unionJoinBtn.onClick:RemoveAllListeners()
            objTable.data.unionJoin.onClick:RemoveAllListeners()
        end
        local LeagueId = data.guildid
        -- print("data.guildid:",data.guildid,"data.guildname:",data.guildname)
        -- print("LeagueRecruit:",data.leagueRecruit)
        local LeagueIcon = data.leagueRecruit.icon or 0
        local LeagueTitle = data.leagueRecruit.titleID or 0
        local LeagueNum = data.leagueRecruit.memberCount or 0
        local LeagueTotleNum = data.leagueRecruit.memberLimit or 0
        local LeagueName = data.guildname or ""

        if not openNew then
            local joinfun = function()
                local ui_pop_mgr = require "ui_pop_mgr"
                local isOpen = ui_pop_mgr.CheckIsOpen(54, true)
                if not isOpen then
                    return
                end
                local net_leaguepro_module = require "net_leaguepro_module"
                net_leaguepro_module.C2SLeagueInfoReq(LeagueId)
                -- local ui_sociaty_detail = require "ui_sociaty_detail"
                -- ui_sociaty_detail.SetData(4, LeagueId)
            end
            objTable.data.unionJoinBtn.onClick:AddListener(joinfun)
            objTable.data.unionJoin.onClick:AddListener(joinfun)
        else
            objTable.data.clickLeagueRecruit = function()
                local ui_pop_mgr = require "ui_pop_mgr"
                local isOpen = ui_pop_mgr.CheckIsOpen(54, true)
                if not isOpen then
                    return
                end
                local net_leaguepro_module = require "net_leaguepro_module"
                net_leaguepro_module.C2SLeagueInfoReq(LeagueId)
                -- local ui_sociaty_detail = require "ui_sociaty_detail"
                -- ui_sociaty_detail.SetData(4, LeagueId)
            end
        end

        if LeagueName then
            objTable.data.unionText.text = LeagueName .. "  [" .. LeagueNum .. "/" .. LeagueTotleNum .. "]"
        end
        if game_config.Q1SDK_DOMESTIC then
            objTable.data.joinText.text = lang.Get(15286)
        else
            objTable.data.joinText.text = data.context
        end

        local cfg = game_scheme:LeagueIcon_0(LeagueIcon)
        if cfg then
            objTable.data.sociatyIconUI = objTable.data.sociatyIconUI or sociaty_icon.CUIContent():Init(objTable.data.unionIcon.transform, cfg, nil, 0.560)
            objTable.data.sociatyIconUI:UpdateData(cfg)
        end
        objTable.data.unionIcon.gameObject:SetActive(cfg)
        if not util.IsObjNull(objTable.data.unionTitle) then
            local cfg = game_scheme:RoleTitle_0(LeagueTitle)
            if cfg then
                local data = { rivalType = cfg.rivalType, NameID = cfg.NameID, NameColor = cfg.NameColor, NameColorB = cfg.NameColorB }
                data.textScale = 1.5
                objTable.data.sociatyTitleUI = objTable.data.sociatyTitleUI or sociaty_title.CUIContent():Init(objTable.data.unionTitle.transform, data, nil, 0.560)
                objTable.data.sociatyTitleUI:UpdateData(data)
            end
            objTable.data.unionTitle.gameObject:SetActive(cfg)
        end
    end
    if objTable.data.sociatyIconUI then
        objTable.data.sociatyIconUI:Dispose()
        objTable.data.sociatyIconUI = nil
    end

    if objTable.data.sociatyTitleUI then
        objTable.data.sociatyTitleUI:Dispose()
        objTable.data.sociatyTitleUI = nil
    end
end

function DisposeLeagueRecruit(chatContentList, objTable)
    if objTable.data.sociatyIconUI then
        objTable.data.sociatyIconUI:Dispose()
        objTable.data.sociatyIconUI = nil
    end
end

function RenderLeagueMutualHelp(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
    objTable.data.helpObj.gameObject:SetActive(true)
    local selfRoleID = player_mgr.GetPlayerRoleID()
    if data.roleid == selfRoleID then
        objTable.data.helpBtn.gameObject:SetActive(false)
    end

    --只有同角色同HelpId才弹+1
    if chatContentList.helpOther then
        if chatContentList.helpOtherID and data.roleid == chatContentList.helpOtherID then
            local flow_custom_item = require "flow_custom_item"
            local text = "+1"
            flow_custom_item.CItem():Init(objTable.data.flowTrans, text)
        end
    end

    if data.leagueHelp then
        local helpInfo = chat_mgr_new.GetHelpInfoByID(data.roleid)

        local selfRoleID = player_mgr.GetPlayerRoleID()
        if helpInfo and helpInfo.helpRoleID[selfRoleID] then
            objTable.data.helpBtnGray:Switch(1)
        else
            objTable.data.helpBtnGray:Switch(0)
        end
        local progress = helpInfo and helpInfo.prog or 0
        local progressEnd = 0
        local cfg_help = game_scheme:LeagueHelp_0(data.leagueHelp.helpID)
        if cfg_help then
            progressEnd = cfg_help.helpNum
            local cfg_reward = game_scheme:Reward_0(cfg_help.seekHelpReward.data[0])
            if cfg_reward then
                if cfg_reward.iRewardType == 1 then
                    --物品
                    local goods_item = require "goods_item_new"
                    objTable.data.iconUI = objTable.data.iconUI or goods_item.CGoodsItem():Init(objTable.data.iconRoot, nil, 0.5)
                    objTable.data.iconUI:SetGoods(nil, cfg_reward.arrParam[0], cfg_reward.arrParam[1], function()
                        iui_item_detail.Show(cfg_reward.arrParam[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, cfg_help.seekHelpReward.data[0])
                    end)
                    objTable.data.iconUI:SetFrameBg(3)
                elseif cfg_reward.iRewardType == 2 then
                    --英雄
                    local hero_item = require "hero_item_new"
                    objTable.data.heroUI = objTable.data.heroUI or hero_item.CHeroItem():Init(objTable.data.iconRoot, function()
                        objTable.data.heroUI:DisplayInfo()
                    end, 0.5)
                    objTable.data.heroUI:SetHero({ heroID = cfg_reward.arrParam[0] }, function()
                        local ui_archive_detail = require "ui_archive_detail"
                        ui_archive_detail.SetData({ { heroID = cfg_reward.arrParam[0], starLv = cfg_reward.arrParam[1] } }, 1)
                        ui_window_mgr:ShowModule("ui_archive_detail")
                    end)
                end
            end
        end
        objTable.data.helpProgressText.text = string.format("%d/%d", progress, progressEnd)
        if progress == progressEnd then
            objTable.data.helpObj.transform.parent.gameObject:SetActive(false)
        else
            objTable.data.helpObj.transform.parent.gameObject:SetActive(true)
        end
        objTable.data.helpProgressImg.sizeDelta = { x = progress / progressEnd * 190, y = 18 }
        if not openNew then
            objTable.data.helpBtn.onClick:RemoveAllListeners()
            objTable.data.helpBtn.onClick:AddListener(function()
                local net_chat_module_new = require "net_chat_module_new"

                net_chat_module_new.Send_PLEAGUEHELP_AIDALLIES(data.roleid)

                local roleName = tostring(player_mgr.GetRoleName()) or ""
                local sociaty_data = require "sociaty_data"
                local baseData = sociaty_data.GetLeagueData()
                if baseData and baseData.id then
                    local json_str = json.encode({
                        guild_id = baseData.id,
                        guild_name = baseData.strName,
                        role_name = roleName
                    })
                    event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickehelp", json_str)
                end
            end)
        else
            objTable.data.clickLeagueMutualHelp = function()
                local net_chat_module_new = require "net_chat_module_new"

                net_chat_module_new.Send_PLEAGUEHELP_AIDALLIES(data.roleid, isTrial)

                local roleName = tostring(player_mgr.GetRoleName()) or ""
                local sociaty_data = require "sociaty_data"
                local baseData = sociaty_data.GetLeagueData()
                if baseData and baseData.id then
                    local json_str = json.encode({
                        guild_id = baseData.id,
                        guild_name = baseData.strName,
                        role_name = roleName
                    })
                    event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickehelp", json_str)
                end
            end
        end

        objTable.data.helpTips.text = string.format(lang.Get(16750), data.name)
    else
        objTable.data.helpProgressImg.sizeDelta = { x = 0, y = 18 }
    end
end

function DisposeLeagueMutualHelp(chatContentList, objTable)
    if objTable.data.iconUI then
        objTable.data.iconUI:Dispose()
        objTable.data.iconUI = nil
    end
    if objTable.data.heroUI then
        objTable.data.heroUI:Dispose()
        objTable.data.heroUI = nil
    end
end

function RenderChineseRedPacket(chatContentList, objTable, data)
    if data.redPacketInfo then
        local redPacketInfo = chat_mgr_new.GetRedPacketInfoById(data.redPacketInfo.redPacketId)
        if redPacketInfo then
            if not openNew then
                objTable.data.contentBtn.gameObject:SetActive(false)
            end
            objTable.data.redEnvelopeObj:SetActive(true)
            objTable.data.goRedEnvelopeBgGray:SetEnable(redPacketInfo.hadGet == 1)
            objTable.data.goRedEnvelopeArrowGray:SetEnable(redPacketInfo.hadGet == 1)
            if redPacketInfo.redEnvelopId == skep_mgr.const_id.anniversaryRedPackage1 then
                objTable.data.goRedEnvelopeArrowSw:Switch(0)
                objTable.data.goRedEnvelopeBgSw:Switch(0)
            elseif redPacketInfo.redEnvelopId == skep_mgr.const_id.anniversaryRedPackage2 then
                objTable.data.goRedEnvelopeArrowSw:Switch(1)
                objTable.data.goRedEnvelopeBgSw:Switch(1)
            else
                -- objTable.data.goRedEnvelopeArrowSw:Switch(2)
                objTable.data.goRedEnvelopeBgSw:Switch(2)
            end
            if not openNew then
                objTable.data.btnRedEnvelope.onClick:RemoveAllListeners()
                objTable.data.btnRedEnvelope.onClick:AddListener(function()
                    ---@type SendRedEnvelopeParam
                    local sendRedEnvelopeParam = {}
                    sendRedEnvelopeParam.redEnvelopeId = data.redPacketInfo.redPacketId
                    sendRedEnvelopeParam.roldId = data.roleid
                    local openModuleName
                    -- if redPacketInfo.hadGet == 1 then
                    --     if festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.NewYear then
                    --         openModuleName = "ui_chinese_red_envelope_rob_detail"
                    --     elseif festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.Anniversary then
                    --         openModuleName = "ui_anniversary_red_package_rob_detail"
                    --     end
                    --     local win = ui_window_mgr:ShowModule(openModuleName)
                    --     if win then
                    --         win:SetInputParam(sendRedEnvelopeParam)
                    --     end
                    -- else
                    --     if festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.NewYear then
                    --         openModuleName = "ui_chinese_red_envelope_open"
                    --     elseif festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.Anniversary then
                    --         openModuleName = "ui_anniversary_red_package_open"
                    --     end
                    --     if not ui_window_mgr:IsModuleShown(openModuleName) then
                    --         local win = ui_window_mgr:ShowModule(openModuleName)
                    --         if win then
                    --             win:SetInputParam(sendRedEnvelopeParam)
                    --         end
                    --     end
                    -- end
                end)
            else
                objTable.data.clickChineseRedPacket = function()
                    ---@type SendRedEnvelopeParam
                    local sendRedEnvelopeParam = {}
                    sendRedEnvelopeParam.redEnvelopeId = data.redPacketInfo.redPacketId
                    sendRedEnvelopeParam.roldId = data.roleid
                    -- local ui_anniversary_redenvelopes_mgr = require "ui_anniversary_redenvelopes_mgr"
                    -- if ui_anniversary_redenvelopes_mgr.GetIsUnlock() then
                    --     if redPacketInfo.hadGet == 1 then
                    --         if festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.NewYear then
                    --             openModuleName = "ui_chinese_red_envelope_rob_detail"
                    --         elseif festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.Anniversary then
                    --             openModuleName = "ui_anniversary_red_package_rob_detail"
                    --         end
                    --         local win = ui_window_mgr:ShowModule(openModuleName)
                    --         if win then
                    --             win:SetInputParam(sendRedEnvelopeParam)
                    --         end
                    --     else
                    --         if festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.NewYear then
                    --             openModuleName = "ui_chinese_red_envelope_open"
                    --         elseif festival_activity_mgr.nowRedPackageType == festival_activity_mgr.redPackageType.Anniversary then
                    --             openModuleName = "ui_anniversary_red_package_open"
                    --         end
                    --         if not ui_window_mgr:IsModuleShown(openModuleName) then
                    --             local win = ui_window_mgr:ShowModule(openModuleName)
                    --             if win then
                    --                 win:SetInputParam(sendRedEnvelopeParam)
                    --             end
                    --         end
                    --     end
                    -- else
                    --     flow_text.Add(lang.Get(101168))
                    -- end
                end
            end
        end
    else
        log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO, "chat_content_list, enSpeak_ChineseRedPacket redPacketInfo is nil")
    end
end

function RenderShareStarDiamond(chatContentList, objTable, data)
    if data.goods then
        local cfg = game_scheme:Item_0(data.goods.goodsId)
        if cfg then
            if not openNew then
                objTable.data.contentBtn.gameObject:SetActive(false)
            end
            objTable.data.shareGoodObj.gameObject:SetActive(true)
            local color
            if cfg.starLevel < 5 then
                color = "FFF25C"
            elseif cfg.starLevel == 5 then
                color = "FF5C5C"
            else
                color = "E1DFF6"
            end
            local name = string.format("<color=#%s>[%s]</color>", color, lang.Get(cfg.nameKey))
            if data.goods.lv == 0 then
                objTable.data.shareGoodName.text = name
            else
                objTable.data.shareGoodName.text = string.format(lang.Get(297082), name)
            end

            objTable.data.shareGoodBtn.onClick:RemoveAllListeners()
            local goods_item = require "goods_item_new"
            objTable.data.shareGoodIconUI = objTable.data.shareGoodIconUI or goods_item.CGoodsItem():Init(objTable.data.shareGoodRoot.transform, nil, 0.55)
            objTable.data.shareGoodIconUI:SetFrameBg(3)
            objTable.data.shareGoodIconUI:SetQualityImg(true)
            objTable.data.shareGoodIconUI:SetCountEnable(false)
            objTable.data.shareGoodIconUI:SetGoods(nil, data.goods.goodsId, 0)
            local clickfun = function()
                -- local win = ui_window_mgr:ShowModule("ui_force_gem_info")
                -- if win then
                --     local force_gem_info_widget = require "force_gem_info_widget"
                --     win:SetForceGemData({ goodsID = data.goods.goodsId }, force_gem_info_widget.ForceGemInfoShowEnum.Share)
                -- end
            end
            objTable.data.shareGoodBtn.onClick:AddListener(clickfun)
        end
    end
end

function DisposeShareGood(chatContentList, objTable)
    if objTable.data.shareGoodIconUI then
        objTable.data.shareGoodIconUI:Dispose()
        objTable.data.shareGoodIconUI = nil
    end
end

function RenderShareLabourDayInfo(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
        if objTable.data.laborDayObj == nil then
            objTable.data.contentBtn.transform.parent:SetActive(false)
            return
        end
        objTable.data.laborDayJoin.onClick:RemoveAllListeners()
    end

    objTable.data.laborDayObj.gameObject:SetActive(true)
    -- if data.shareInfo then
    --     local may_day_mgr = require "may_day_mgr"
    --     local priceColor = "E02B2B"
    --     local nowTime = may_day_mgr.GetServerTimestamp()
    --     local nowTime_zone = may_day_mgr.GetServerTimestamp_TimeZone(nowTime)
    --     local cacheValue = may_day_mgr.GetServerTimestamp_TimeZone(data.chatTime)
    --     if data.shareInfo.nHostId == player_mgr.GetPlayerRoleID() then
    --         objTable.data.laborDayJoin:SetActive(false)
    --         priceColor = "92600A"
    --     else
    --         objTable.data.laborDayJoin:SetActive(true)
    --     end

    --     if data.shareInfo.nDaySellPrice < may_day_mgr.GetTodayPrices() then
    --         priceColor = "1E951B"
    --     end
    --     --如果不是今天的分享
    --     if nowTime_zone > 0 and not util.IsSameDayNew(cacheValue, nowTime_zone) then
    --         --log.Log("caheValue = %s, nowTime_zone = %s", cacheValue, nowTime_zone)
    --         priceColor = "92600A"
    --     end

    --     if data.shareInfo.nShareType == 1 then
    --         --如果是集市分享
    --         objTable.data.laborDayJoinTxt.text = lang.Get(300418, nil, "点击串门")
    --         objTable.data.laborDayText.text = string.format(lang.Get(300417, nil, "我的集市今日胡萝卜价格<color=#%s>%s</color>，快来我这卖吧"), priceColor, data.shareInfo.nDaySellPrice)
    --         -- objTable.data.laborDayText.text = string.format(lang.Get(381617), priceColor, data.shareInfo.nDaySellPrice)
    --     elseif data.shareInfo.nShareType == 2 then
    --         --如果是小队分享
    --         objTable.data.laborDayJoinTxt.text = lang.Get(483303)  --点击加入
    --         objTable.data.laborDayText.text = lang.Get(300457)--快来加入我的小队，一起赚取兔子币吧！
    --         -- objTable.data.laborDayText.text = lang.Get(381657)--快来加入我的小队，一起赚取西域古币吧！
    --     end

    --     if not openNew then
    --         local laborjoin = function()

    --             if data.shareInfo.nShareType == 1 then
    --                 --判断每条消息的发送时间和现在时间相比是否算过期
    --                 if nowTime_zone > 0 and not util.IsSameDayNew(cacheValue, nowTime_zone) then
    --                     flow_text.Add(lang.Get(84715, nil, "已过期"))
    --                     return
    --                 end
    --                 --装载额外数据
    --                 local extendData = {}
    --                 extendData.nHostworldid = data.worldid   --摊主世界id
    --                 may_day_mgr.C2SLabourDayActivityStallREQ(data.shareInfo, extendData)
    --             elseif data.shareInfo.nShareType == 2 then
    --                 if data.shareInfo.nShareState == 1 then
    --                     flow_text.Add(lang.Get(84715, nil, "已过期"))
    --                     return
    --                 end
    --                 local net_may_day_module = require "net_may_day_module"
    --                 net_may_day_module.Request_LabourDayActivity_JoinTeam(player_mgr.GetPlayerRoleID(), data.shareInfo.nHostId)
    --                 if data.context then
    --                     net_may_day_module.SetTimeKey(data.context)
    --                 end
    --             end
    --         end
    --         objTable.data.laborDayJoin.onClick:AddListener(laborjoin)
    --     else
    --         objTable.data.clickLabourDay = function()
    --             if data.shareInfo.nShareType == 1 then
    --                 --判断每条消息的发送时间和现在时间相比是否算过期
    --                 if nowTime_zone > 0 and not util.IsSameDayNew(cacheValue, nowTime_zone) then
    --                     flow_text.Add(lang.Get(84715, nil, "已过期"))
    --                     return
    --                 end
    --                 --装载额外数据
    --                 local extendData = {}
    --                 extendData.nHostworldid = data.worldid   --摊主世界id
    --                 may_day_mgr.C2SLabourDayActivityStallREQ(data.shareInfo, extendData)
    --             elseif data.shareInfo.nShareType == 2 then
    --                 if data.shareInfo.nShareState == 1 then
    --                     flow_text.Add(lang.Get(84715, nil, "已过期"))
    --                     return
    --                 end
    --                 local net_may_day_module = require "net_may_day_module"
    --                 net_may_day_module.Request_LabourDayActivity_JoinTeam(player_mgr.GetPlayerRoleID(), data.shareInfo.nHostId)
    --                 if data.context then
    --                     net_may_day_module.SetTimeKey(data.context)
    --                 end
    --             end
    --         end
    --     end
    -- else
    --     log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO, "chat_content_list, enSpeak_ShareLabourDayInfo shareInfo is nil")
    -- end
end

function RenderWMTopRaceGuess(chatContentList, objTable, data)
    if not openNew then
        objTable.data.nHead.gameObject:SetActive(false)
        objTable.data.contentCST.gameObject:SetActive(false)
        objTable.data.title.gameObject:SetActive(false)
    end
    objTable.data.PeakGameSupport.gameObject:SetActive(true)
    local str = string.format("%s %s %s", data.name, lang.Get(280203), data.context)
    objTable.data.PeakGameSupportTxt.text = str
    UI.LayoutRebuilder.ForceRebuildLayoutImmediate(objTable.data.PeakGameSupportRect)
end

function RenderDimensionWarAssemble(chatContentList, objTable, data)
    -- if data.dimensionWarInfo then
    --     local dimension_war_data = require "dimension_war_data"
    --     local fortCfg = dimension_war_helper.GetFortCfgFromStationIndex(data.dimensionWarInfo.iPosIndex)
    --     if fortCfg then
    --         if not openNew then
    --             objTable.data.contentBtn.gameObject:SetActive(false)
    --         end
    --         if not util.IsObjNull(objTable.data.gatherObj) then
    --             objTable.data.gatherObj.gameObject:SetActive(true)
    --         end
    --         local buildingName = "building" .. fortCfg.iPointType
    --         dimension_war_data.SetBuildingImage(buildingName, objTable.data.gatherBuildingIcon)
    --         local scaleParame = fortCfg.iScale / 10000
    --         objTable.data.gatherBuildingIcon.transform.localScale = { x = scaleParame, y = scaleParame, z = 1 }

    --         objTable.data.gatherBuildingName.text = dimension_war_data.GetStationName(data.dimensionWarInfo.iPosIndex)

    --         if not openNew then
    --             objTable.data.gatherBtn.onClick:RemoveAllListeners()
    --             objTable.data.gatherBtn.onClick:AddListener(function()
    --                 if not dimension_war_helper.CanShowInChatList() then
    --                     return
    --                 end
    --                 local dimension_war_scene = require "dimension_war_scene"
    --                 local dimension_war_map_mgr = require "dimension_war_map_mgr"
    --                 if dimension_war_scene.IsShow() then
    --                     local ui_dimension_war_base = require "ui_dimension_war_base"
    --                     ui_window_mgr:UnloadModule("ui_chat_main_new")
    --                     ui_dimension_war_base.Quit()
    --                     dimension_war_map_mgr.MoveToTargetGrid(data.dimensionWarInfo.iPosIndex)
    --                 else
    --                     dimension_war_data.SetEnterBattleFieldParam(data.dimensionWarInfo.iPosIndex)
    --                     dimension_war_helper.EnterDimensionWar()
    --                 end
    --             end)
    --         else
    --             objTable.data.clickGather = function()
    --                 if not dimension_war_helper.CanShowInChatList() then
    --                     return
    --                 end
    --                 local dimension_war_scene = require "dimension_war_scene"
    --                 local dimension_war_map_mgr = require "dimension_war_map_mgr"
    --                 if dimension_war_scene.IsShow() then
    --                     local ui_dimension_war_base = require "ui_dimension_war_base"
    --                     ui_window_mgr:UnloadModule("ui_chat_main_new")
    --                     ui_dimension_war_base.Quit()
    --                     dimension_war_map_mgr.MoveToTargetGrid(data.dimensionWarInfo.iPosIndex)
    --                 else
    --                     dimension_war_data.SetEnterBattleFieldParam(data.dimensionWarInfo.iPosIndex)
    --                     dimension_war_helper.EnterDimensionWar()
    --                 end
    --             end
    --         end
    --     end
    -- end
end

function RenderDimensionWarOccupy(chatContentList, objTable, data)
    -- if data.dimensionWarInfo then
    --     local dimension_war_data = require "dimension_war_data"
    --     if not openNew then
    --         objTable.data.nHead.gameObject:SetActive(false)
    --         objTable.data.title.gameObject:SetActive(false)
    --         objTable.data.contentBtn.gameObject:SetActive(false)
    --     end
    --     if not util.IsObjNull(objTable.data.occupyObj) then
    --         objTable.data.occupyObj.gameObject:SetActive(true)
    --     end
    --     objTable.data.occupyName.text = data.dimensionWarInfo.attacker
    --     objTable.data.occupyFortName.text = dimension_war_data.GetStationName(data.dimensionWarInfo.iPosIndex)
    -- end
end

function RenderChristmasTeamData(chatContentList, objTable, data)
    -- --双旦拼团
    -- local christmas_newyear_mgr = require "christmas_newyear_mgr"
    -- christmas_newyear_mgr.ChatItemRender(objTable, data, openNew)
end

function RenderPickTheRouteShareArchived(chatContentList, objTable, data)
    if not openNew then
        objTable.data.contentBtn.gameObject:SetActive(false)
    end
    if objTable.data.pickTheRouteObj == nil then
        return
    end
    if data.pickTheRouteArchived then
        objTable.data.pickTheRouteObj.gameObject:SetActive(true)
        local levelId = data.pickTheRouteArchived.levelID
        local eventChooseID = data.pickTheRouteArchived.eventChooseID or {}
        local activityID = data.pickTheRouteArchived.activityID

        local pick_the_route_mgr = require "pick_the_route_mgr"
        local topicId = pick_the_route_mgr.GetCurTopicID()
        local cfgActivityTime = festival_activity_mgr.GetActivityTimeCfgByTopicID(topicId)
        if cfgActivityTime == nil or cfgActivityTime.AtyID ~= activityID then
            --- 过滤掉活动不一致的救援记录（先遣和正式服的分享问题）
            objTable.data.pickTheRouteObj.transform.parent:SetActive(false)
            return
        end

        local title = string.format(lang.Get(413612), tostring(levelId))
        local pickTheRouteCfgs = pick_the_route_mgr.GetPickTheRouteCfgs(activityID)
        local boxActivesCount = 0
        local receivedActives = {}
        if pickTheRouteCfgs and #pickTheRouteCfgs >= levelId and levelId > 0 then
            local pickTheRouteCfg = pickTheRouteCfgs[levelId]
            --title = title.." ".. (lang.Get(pickTheRouteCfg.nameLanID) or "")
            boxActivesCount = pickTheRouteCfg.rewardEventIds.count
            for i = 1, boxActivesCount do
                local isPass = false
                local evnetId = pickTheRouteCfg.rewardEventIds.data[i - 1]
                for _, chooseId in ipairs(eventChooseID) do
                    if chooseId == evnetId then
                        isPass = true
                        break
                    end
                end
                receivedActives[i] = isPass
            end
        end
        for i = 1, 3 do
            local boxObj = objTable.data["pickTheRouteBox" .. i]
            local receivedObj = objTable.data["pickTheRouteReceived" .. i]
            local isReceived = receivedActives[i] or false
            local isShow = (boxActivesCount >= i)

            if boxActivesCount < 3 then
                if i == 3 then
                    isReceived = receivedActives[boxActivesCount] or false
                    isShow = true
                elseif i >= boxActivesCount then
                    isReceived = false
                    isShow = false
                end
            end

            if not util.IsObjNull(boxObj) then
                boxObj:SetActive(isShow)
            end
            if not util.IsObjNull(receivedObj) then
                receivedObj:SetActive(isReceived)
            end
        end
        objTable.data.pickTheRouteLevel.text = title
        objTable.data.pickTheRouteStepCount.text = string.format(lang.Get(413116), tostring(math.min(#eventChooseID, 50)))
        if not openNew then
            objTable.data.pickTheRouteViewBtn.onClick:RemoveAllListeners()
            objTable.data.pickTheRouteViewBtn.onClick:AddListener(function()
                ClickPickTheRouteViewBtn(data)
            end)
        else
            objTable.data.clickPickTheRouteView = function()
                ClickPickTheRouteViewBtn(data)
            end
        end
    else
        objTable.data.pickTheRouteObj.transform.parent:SetActive(false)
        log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO, "chat_content_list, enSpeak_PickTheRouteShareArchived shareInfo is nil")
    end
end

function RenderMakeFoodInfo(chatContentList, objTable, data)
    if objTable.data.makeFoodsGameOrderObj then
        if data.exchangeInfo then
            if objTable.data.makeFoodsGameOrderObj.gameObject.activeSelf ~= true then
                objTable.data.makeFoodsGameOrderObj.gameObject:SetActive(true)
            end
            local _langIDs = {
                exchangeBtnText = 384234, --交易
                expireBtnText = 384264, --已过期
                expireTips = 384281, --过期点击提示
                sendTips = 486464, --我发起的一条材料交易单
                materialLimit = 384255, --材料不足
                numLimit = 384311, --今日交易已达上限，休息一下明天继续
                conditionLimit = 384298, --不满足条件，无法交易
            }
            local colorStr = {
                OriginalItemColor = "<color=#FF4747>-%s</color>",
                TargetItemColor = "<color=#0FEB19>+%s</color>",
                myDesColor = "<color=#3A6AA9>%s</color>",
                otherDesColor = "<color=#3A6AA9>%s</color>",
            }
            local goods_item = require "goods_item_new"
            local iui_item_detail = require "iui_item_detail"
            -- local festival_make_foods_game_mgr = require "festival_make_foods_game_mgr"
            local lingshi_data = require "lingshi_data"
            local isSkipClientMsgLimit = lingshi_data.GetSkipClientMsgLimit()
            local isMine = data.roleid and data.roleid == player_mgr.GetPlayerRoleID()  --是否是玩家自己发的单子
            local isNotExpire = data.exchangeInfo.createTime and util.IsSameDay(data.exchangeInfo.createTime, os.server_time()) or false--是否过期
            local isMaterialEnough = false
            --festival_make_foods_game_mgr.CheckExchangeMaterialEnough(data.exchangeInfo.targetItemId, data.exchangeInfo.targetItemNum) --玩家是否拥有足够材料
            local index = isMine and 1 or 0
            local _pageState, _2, isClose = chat_mgr_new.GetPageState()
            --聊天界面未关闭且是联盟或私聊
            local flag2 = not isClose and (_pageState == chat_mgr_new.enum_pState.guide or _pageState == chat_mgr_new.enum_pState.privateView)
            -- print("[chat_content_list]>> chat: ",chat_mgr_new.GetPageState())
            -- print("[chat_content_list]>> chat: ",flag2)
            objTable.data.contentBtn.gameObject:SetActive(false)
            local tips = objTable.data.makeFoodsGameOrderObj:Get("tips")
            local itemTargetRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot")
            local itemOriginalRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot")
            local exchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtn")
            local expireBtn = objTable.data.makeFoodsGameOrderObj:Get("expireBtn")
            local noExchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("noExchangeBtn")
            local exchangeBtnText = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtnText")
            local expireBtnText = objTable.data.makeFoodsGameOrderObj:Get("expireBtnText")
            local noExchangeBtnText = objTable.data.makeFoodsGameOrderObj:Get("noExchangeBtnText")
            local bg = objTable.data.makeFoodsGameOrderObj:Get("bg")
            local buttonContent = objTable.data.makeFoodsGameOrderObj:Get("buttonContent")
            local root = objTable.data.makeFoodsGameOrderObj:Get("root")
            tips.text = lang.Get(_langIDs.sendTips) --string.format(isMine and colorStr.myDesColor or colorStr.otherDesColor,lang.Get(_langIDs.sendTips))
            objTable.data.originalGoodsItem = objTable.data.originalGoodsItem or goods_item.CGoodsItem():Init(itemOriginalRoot, nil, 0.43)
            objTable.data.originalGoodsItem:SetGoods(nil, data.exchangeInfo.originItemId, nil, function()
                iui_item_detail.Show(data.exchangeInfo.originItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
            end)
            local str1 = tostring(data.exchangeInfo.originItemNum) or "0"
            objTable.data.originalGoodsItem:SetDescription(string.format(colorStr.OriginalItemColor, str1))

            objTable.data.targetGooodsItem = objTable.data.targetGooodsItem or goods_item.CGoodsItem():Init(itemTargetRoot, nil, 0.43)
            objTable.data.targetGooodsItem:SetGoods(nil, data.exchangeInfo.targetItemId, nil, function()
                iui_item_detail.Show(data.exchangeInfo.targetItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
            end)
            local str2 = tostring(data.exchangeInfo.targetItemNum) or "0"
            objTable.data.targetGooodsItem:SetDescription(string.format(colorStr.TargetItemColor, str2))

            exchangeBtnText.text = lang.Get(_langIDs.exchangeBtnText)
            expireBtnText.text = lang.Get(_langIDs.expireBtnText)
            noExchangeBtnText.text = lang.Get(_langIDs.materialLimit)
            -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.createTime)
            -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemId)
            -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemNum)
            -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemId)
            -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemNum)
            bg:Switch(index)
            --自己发的单子隐藏按钮
            buttonContent:SetActive(not isMine)
            if not isMine and flag2 then
                if isNotExpire then
                    --未过期
                    if isMaterialEnough then
                        --玩家具有足够的材料数量
                        exchangeBtn:SetActive(true)
                        noExchangeBtn:SetActive(false)
                    else
                        --玩家不具有足够的材料数量
                        exchangeBtn:SetActive(false)
                        noExchangeBtn:SetActive(true)
                    end
                    expireBtn:SetActive(false)
                    --设置零点刷新时间
                    local endTime = util.GetTimeOfZero(os.server_zone(), data.exchangeInfo.createTime)
                    chatContentList:SetRefreshTime(endTime)
                else
                    --已过期
                    exchangeBtn:SetActive(false)
                    expireBtn:SetActive(true)
                end
                objTable.data.makeFoodsGameOrderObj.InvokeFunc = function(funcName)
                    if isSkipClientMsgLimit then
                        -- local ui_window_mgr = require "ui_window_mgr"
                        -- local win = ui_window_mgr:ShowModule("ui_make_foods_game_order_exchange")
                        -- win:SetInputParam(data.exchangeInfo)
                        return
                    end
                    --快速点击屏蔽
                    if block_cache.IfBlock(string.format("makeFoodsGameOrderClick%s", data.exchangeInfo.orderId), 1) then
                        flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
                        return
                    end
                    if funcName == "exchange" then
                        --进行交易按钮
                        -- local num = festival_make_foods_game_mgr.GetRemainOrdersExchange() --玩家是否拥有足够交换次数
                        -- if not num or num <= 0 then
                        --     local flow_text = require "flow_text"
                        --     flow_text.Add(lang.Get(_langIDs.numLimit))
                        --     return
                        -- end
                        -- local ui_window_mgr = require "ui_window_mgr"
                        -- local win = ui_window_mgr:ShowModule("ui_make_foods_game_order_exchange")
                        -- win:SetInputParam(data.exchangeInfo)
                    elseif funcName == "expire" then
                        --过期按钮
                        local flow_text = require "flow_text"
                        flow_text.Add(lang.Get(_langIDs.expireTips))
                    elseif funcName == "noExchange" then
                        --材料不足按钮
                        local flow_text = require "flow_text"
                        flow_text.Add(lang.Get(_langIDs.conditionLimit))
                    end
                end
            end
            UI.LayoutRebuilder.ForceRebuildLayoutImmediate(root)
        end
    end
end

function ClickPickTheRouteViewBtn(data)
    local pick_the_route_mgr = require "pick_the_route_mgr"
    local activityId = data.pickTheRouteArchived.activityID
    local levelId = data.pickTheRouteArchived.levelID
    local eventChooseIDs = data.pickTheRouteArchived.eventChooseID
    local maxUnlockLevel = pick_the_route_mgr.GetMaxUnlockLevel(activityId)
    local topicId = pick_the_route_mgr.GetCurTopicID()
    local cfgActivityTime = festival_activity_mgr.GetActivityTimeCfgByTopicID(topicId)
    repeat
        if cfgActivityTime == nil or cfgActivityTime.AtyID ~= activityId then
            flow_text.Add(lang.Get(104870))
            break
        end

        local endTimestamp = festival_activity_mgr.GetAcitivityEndStampByActivityID(activityId)
        local now = os.server_time()
        if endTimestamp == nil or now >= endTimestamp then
            flow_text.Add(lang.Get(413443))
            break
        end

        if levelId > maxUnlockLevel then
            flow_text.Add(lang.Get(413607))
            break
        end
        local win = ui_window_mgr:ShowModule("ui_pick_the_route_archived")
        if win then
            win:SetInputParam(data.roleid, data.name, activityId, levelId, eventChooseIDs)
        end
    until true
end

function RenderPumnkinShareArchived(chatContentList, objTable, data)
    -- if not util.IsObjNull(objTable.data.contentBtn) then
    --     objTable.data.contentBtn.gameObject:SetActive(false)
    -- end
    -- if data.pumpkinData then
    --     if objTable.data.pumnkinShareObj and not util.IsObjNull(objTable.data.pumnkinShareObj) then
    --         objTable.data.pumnkinShareObj.gameObject:SetActive(true)
    --         local halloween_day_mgr = require "halloween_day_mgr"
    --         local net_halloween_day_moudle = require "net_halloween_day_moudle"
    --         local cfg = halloween_day_mgr.GetScoreCfgInfo()
    --         local id = data.pumpkinData.scoreId
    --         if id and cfg and cfg[id] then
    --             local text = string.format("<color=#FEF555>%s</color>", data.pumpkinData.score)
    --             objTable.data.desc.text = string.format(lang.Get(cfg[id].shareEva), text)
    --         end
    --         objTable.data.clickFunc = function()
    --             net_halloween_day_moudle.MSG_ALLSAINTSDAY_DETAILS_REQ(data.pumpkinData.pumpkinId)
    --             local ui_halloween_other_pumkin_pop = require "ui_halloween_other_pumkin_pop"
    --             local _data = {
    --                 name = data.name,
    --                 faceId = data.faceId,
    --                 avatarFrame = data.avatarFrame,
    --             }
    --             ui_halloween_other_pumkin_pop.SetInputParam(_data)
    --         end
    --     end
    -- end
end

function RenderMakeFoodInfoShareArchived(chatContentList, objTable, data)
    if objTable.data.makeFoodsGameOrderObj then
        if data.exchangeInfo then
            if data.exchangeInfo.activityID and data.exchangeInfo.activityID ~= 0 then
                chatContentList:OnItemRenderOrder1(objTable, data)
            else
                chatContentList:OnItemRenderOrder(objTable, data)
            end
        end
    end
end

function DisposeMakeFoodInfoShareOrder(chatContentList, objTable)
    if objTable.data.originalGoodsItem then
        objTable.data.originalGoodsItem:Dispose()
        objTable.data.originalGoodsItem = nil
    end
    if objTable.data.targetGooodsItem then
        objTable.data.targetGooodsItem:Dispose()
        objTable.data.targetGooodsItem = nil
    end
    if objTable.data.makeFoodsGameOrderObj then
        objTable.data.makeFoodsGameOrderObj.InvokeFunc = nil
    end
end

function M:OnItemRenderOrder1(objTable, data)
    if objTable.data.makeFoodsGameOrderObj.gameObject.activeSelf ~= true then
        objTable.data.makeFoodsGameOrderObj.gameObject:SetActive(true)
    end
    local _langIDs = {
        exchangeBtnText = 423508, --交换
        expireBtnText = 423500, --已过期
        expireTips = 423501, --过期点击提示
        sendTips = 423507, --我发起的一则道具交易
        conditionLimit = 423489, --当前装扮道具数量不足
    }
    local colorStr = {
        -- OriginalItemColor="<color=#FF4747>-%s</color>",
        -- TargetItemColor="<color=#0FEB19>+%s</color>",
        myDesColor = "<color=#3A6AA9>%s</color>",
        otherDesColor = "<color=#3A6AA9>%s</color>",
    }
    local goods_item = require "goods_item_new"
    local iui_item_detail = require "iui_item_detail"
    -- local halloween_day_mgr = require "halloween_day_mgr"
    local lingshi_data = require "lingshi_data"
    local isSkipClientMsgLimit = lingshi_data.GetSkipClientMsgLimit()
    local isMine = data.roleid and data.roleid == player_mgr.GetPlayerRoleID()  --是否是玩家自己发的单子
    local isNotExpire = false
    --data.exchangeInfo.createTime and halloween_day_mgr.IsSameDay(data.exchangeInfo.createTime, os.server_time()) or false--是否过期
    local isMaterialEnough = false
    --halloween_day_mgr.CheckExchangeMaterialEnough(data.exchangeInfo.targetItemId, data.exchangeInfo.targetItemNum) --玩家是否拥有足够材料
    local index = isMine and 1 or 0
    local _pageState, _2, isClose = chat_mgr_new.GetPageState()
    --聊天界面未关闭且是联盟或私聊
    local flag2 = not isClose and (_pageState == chat_mgr_new.enum_pState.guide or _pageState == chat_mgr_new.enum_pState.privateView or _pageState == chat_mgr_new.enum_pState.world)
    -- print("[chat_content_list]>> chat: ",chat_mgr_new.GetPageState())
    -- print("[chat_content_list]>> chat: ",flag2)
    -- objTable.data.contentBtn.gameObject:SetActive(false)
    local tips = objTable.data.makeFoodsGameOrderObj:Get("tips")
    local itemTargetRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot")
    local itemOriginalRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot")
    local exchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtn")
    local expireBtn = objTable.data.makeFoodsGameOrderObj:Get("expireBtn")
    --默认关闭"材料不足"按钮
    local noExchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("noExchangeBtn")
    noExchangeBtn:SetActive(false)
    local exchangeBtnText = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtnText")
    local expireBtnText = objTable.data.makeFoodsGameOrderObj:Get("expireBtnText")
    local bg = objTable.data.makeFoodsGameOrderObj:Get("bg")
    local buttonContent = objTable.data.makeFoodsGameOrderObj:Get("buttonContent")
    local root = objTable.data.makeFoodsGameOrderObj:Get("root")
    tips.text = string.format(isMine and colorStr.myDesColor or colorStr.otherDesColor, lang.Get(_langIDs.sendTips))
    objTable.data.originalGoodsItem = objTable.data.originalGoodsItem or goods_item.CGoodsItem():Init(itemOriginalRoot, nil, 0.43)
    objTable.data.originalGoodsItem:SetGoods(nil, data.exchangeInfo.originItemId, nil, function()
        iui_item_detail.Show(data.exchangeInfo.originItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end)
    objTable.data.targetGooodsItem = objTable.data.targetGooodsItem or goods_item.CGoodsItem():Init(itemTargetRoot, nil, 0.43)
    objTable.data.targetGooodsItem:SetGoods(nil, data.exchangeInfo.targetItemId, nil, function()
        iui_item_detail.Show(data.exchangeInfo.targetItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end)
    exchangeBtnText.text = lang.Get(_langIDs.exchangeBtnText)
    expireBtnText.text = lang.Get(_langIDs.expireBtnText)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.createTime)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemId)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemNum)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemId)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemNum)
    bg:Switch(index)
    --自己发的单子隐藏按钮
    buttonContent:SetActive(not isMine)
    if not isMine and flag2 then
        if isNotExpire then
            --未过期
            exchangeBtn:SetActive(true)
            expireBtn:SetActive(false)
            --设置零点刷新时间
            local endTime = util.GetTimeOfZero(os.server_zone(), data.exchangeInfo.createTime)
            self:SetRefreshTime(endTime)
        else
            --已过期
            exchangeBtn:SetActive(false)
            expireBtn:SetActive(true)
        end
        objTable.data.makeFoodsGameOrderObj.InvokeFunc = function(funcName)
            if isSkipClientMsgLimit then
                -- local ui_window_mgr = require "ui_window_mgr"
                -- local win = ui_window_mgr:ShowModule("ui_dress_game_order_exchange")
                -- win:SetInputParam(data.exchangeInfo)
                return
            end
            --快速点击屏蔽
            if block_cache.IfBlock(string.format("dressGameOrderClick%s", data.exchangeInfo.orderId), 1) then
                flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
                return
            end
            if funcName == "exchange" then
                --进行交易按钮
                -- if not isMaterialEnough then
                --     --材料不足提示
                --     local flow_text = require "flow_text"
                --     flow_text.Add(lang.Get(_langIDs.conditionLimit))
                --     return
                -- end
                -- local ui_window_mgr = require "ui_window_mgr"
                -- local win = ui_window_mgr:ShowModule("ui_dress_game_order_exchange")
                -- win:SetInputParam(data.exchangeInfo)
            elseif funcName == "expire" then
                --过期按钮
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(_langIDs.expireTips))
            end
        end
    end
    UI.LayoutRebuilder.ForceRebuildLayoutImmediate(root)
end
function M:OnItemRenderOrder(objTable, data)
    if objTable.data.makeFoodsGameOrderObj.gameObject.activeSelf ~= true then
        objTable.data.makeFoodsGameOrderObj.gameObject:SetActive(true)
    end
    local _langIDs = {
        exchangeBtnText = 384234, --交易
        expireBtnText = 384264, --已过期
        expireTips = 384281, --过期点击提示
        sendTips = 486464, --我发起的一条材料交易单
        materialLimit = 384255, --材料不足
        numLimit = 384311, --今日交易已达上限，休息一下明天继续
        conditionLimit = 384298, --不满足条件，无法交易
    }
    local colorStr = {
        OriginalItemColor = "<color=#FF4747>-%s</color>",
        TargetItemColor = "<color=#0FEB19>+%s</color>",
        myDesColor = "<color=#A9F0FF>%s</color>",
        otherDesColor = "<color=#92600A>%s</color>",
    }
    local goods_item = require "goods_item_new"
    local iui_item_detail = require "iui_item_detail"
    -- local festival_make_foods_game_mgr = require "festival_make_foods_game_mgr"
    local lingshi_data = require "lingshi_data"
    local isSkipClientMsgLimit = lingshi_data.GetSkipClientMsgLimit()
    local isMine = data.roleid and data.roleid == player_mgr.GetPlayerRoleID()  --是否是玩家自己发的单子
    local isNotExpire = data.exchangeInfo.createTime and util.IsSameDay(data.exchangeInfo.createTime, os.server_time()) or false--是否过期
    local isMaterialEnough = false
    --festival_make_foods_game_mgr.CheckExchangeMaterialEnough(data.exchangeInfo.targetItemId, data.exchangeInfo.targetItemNum) --玩家是否拥有足够材料
    local index = isMine and 1 or 0
    local _pageState, _2, isClose = chat_mgr_new.GetPageState()
    --聊天界面未关闭且是联盟或私聊
    local flag2 = not isClose and (_pageState == chat_mgr_new.enum_pState.guide or _pageState == chat_mgr_new.enum_pState.privateView)
    -- print("[chat_content_list]>> chat: ",chat_mgr_new.GetPageState())
    -- print("[chat_content_list]>> chat: ",flag2)
    -- objTable.data.contentBtn.gameObject:SetActive(false)
    local tips = objTable.data.makeFoodsGameOrderObj:Get("tips")
    local itemTargetRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot")
    local itemOriginalRoot = isMine and objTable.data.makeFoodsGameOrderObj:Get("itemOriginalRoot") or objTable.data.makeFoodsGameOrderObj:Get("itemTargetRoot")
    local exchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtn")
    local expireBtn = objTable.data.makeFoodsGameOrderObj:Get("expireBtn")
    local noExchangeBtn = objTable.data.makeFoodsGameOrderObj:Get("noExchangeBtn")
    local exchangeBtnText = objTable.data.makeFoodsGameOrderObj:Get("exchangeBtnText")
    local expireBtnText = objTable.data.makeFoodsGameOrderObj:Get("expireBtnText")
    local noExchangeBtnText = objTable.data.makeFoodsGameOrderObj:Get("noExchangeBtnText")
    local bg = objTable.data.makeFoodsGameOrderObj:Get("bg")
    local buttonContent = objTable.data.makeFoodsGameOrderObj:Get("buttonContent")
    local root = objTable.data.makeFoodsGameOrderObj:Get("root")
    tips.text = lang.Get(_langIDs.sendTips) --string.format(isMine and colorStr.myDesColor or colorStr.otherDesColor,lang.Get(_langIDs.sendTips))
    objTable.data.originalGoodsItem = objTable.data.originalGoodsItem or goods_item.CGoodsItem():Init(itemOriginalRoot, nil, 0.43)
    objTable.data.originalGoodsItem:SetGoods(nil, data.exchangeInfo.originItemId, nil, function()
        iui_item_detail.Show(data.exchangeInfo.originItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end)
    local str1 = tostring(data.exchangeInfo.originItemNum) or "0"
    objTable.data.originalGoodsItem:SetDescription(string.format(colorStr.OriginalItemColor, str1))

    objTable.data.targetGooodsItem = objTable.data.targetGooodsItem or goods_item.CGoodsItem():Init(itemTargetRoot, nil, 0.43)
    objTable.data.targetGooodsItem:SetGoods(nil, data.exchangeInfo.targetItemId, nil, function()
        iui_item_detail.Show(data.exchangeInfo.targetItemId, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
    end)
    local str2 = tostring(data.exchangeInfo.targetItemNum) or "0"
    objTable.data.targetGooodsItem:SetDescription(string.format(colorStr.TargetItemColor, str2))

    exchangeBtnText.text = lang.Get(_langIDs.exchangeBtnText)
    expireBtnText.text = lang.Get(_langIDs.expireBtnText)
    noExchangeBtnText.text = lang.Get(_langIDs.materialLimit)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.createTime)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemId)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.originItemNum)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemId)
    -- print("[chat_content_list]>> enSpeak_MakeFoodInfo: ",data.exchangeInfo.targetItemNum)
    bg:Switch(index)
    --自己发的单子隐藏按钮
    buttonContent:SetActive(not isMine)
    if not isMine and flag2 then
        if isNotExpire then
            --未过期
            if isMaterialEnough then
                --玩家具有足够的材料数量
                exchangeBtn:SetActive(true)
                noExchangeBtn:SetActive(false)
            else
                --玩家不具有足够的材料数量
                exchangeBtn:SetActive(false)
                noExchangeBtn:SetActive(true)
            end
            expireBtn:SetActive(false)
            --设置零点刷新时间
            local endTime = util.GetTimeOfZero(os.server_zone(), data.exchangeInfo.createTime)
            self:SetRefreshTime(endTime)
        else
            --已过期
            exchangeBtn:SetActive(false)
            expireBtn:SetActive(true)
        end
        objTable.data.makeFoodsGameOrderObj.InvokeFunc = function(funcName)
            if isSkipClientMsgLimit then
                -- local ui_window_mgr = require "ui_window_mgr"
                -- local win = ui_window_mgr:ShowModule("ui_make_foods_game_order_exchange")
                -- win:SetInputParam(data.exchangeInfo)
                return
            end
            --快速点击屏蔽
            if block_cache.IfBlock(string.format("makeFoodsGameOrderClick%s", data.exchangeInfo.orderId), 1) then
                flow_text.AddLangRes(lang_res_key.KEY_OPERATE_COOLING)
                return
            end
            if funcName == "exchange" then
                --进行交易按钮
                -- local num = festival_make_foods_game_mgr.GetRemainOrdersExchange() --玩家是否拥有足够交换次数
                -- if not num or num <= 0 then
                --     local flow_text = require "flow_text"
                --     flow_text.Add(lang.Get(_langIDs.numLimit))
                --     return
                -- end
                -- local ui_window_mgr = require "ui_window_mgr"
                -- local win = ui_window_mgr:ShowModule("ui_make_foods_game_order_exchange")
                -- win:SetInputParam(data.exchangeInfo)
            elseif funcName == "expire" then
                --过期按钮
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(_langIDs.expireTips))
            elseif funcName == "noExchange" then
                --材料不足按钮
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(_langIDs.conditionLimit))
            end
        end
    end
    UI.LayoutRebuilder.ForceRebuildLayoutImmediate(root)
end

function RenderLikeCombo(chatContentList, objTable, data)
    --点赞combo
    local maxNum = 16
    objTable.data.comboNumText.text = string.format(lang.Get(913), #data < 100 and tostring(#data) or "99+")
    if #objTable.data.headTableArr < #data and #objTable.data.headTableArr < maxNum then
        local needNum = math.min(#data - #objTable.data.headTableArr, maxNum - #objTable.data.headTableArr)
        for i = 1, needNum do
            local headTable = {}
            headTable.headRoot = GameObject.Instantiate(objTable.data.headTemplate, objTable.obj.transform)
            headTable.iconRoot = headTable.headRoot.transform:Find("iconRoot").gameObject
            headTable.dianzanGo = headTable.headRoot.transform:Find("dianzan").gameObject
            headTable.omitGo = headTable.headRoot.transform:Find("huizong").gameObject
            headTable.faceItem = face_item_new.CFaceItem():Init(headTable.iconRoot.transform, nil, 0.87)

            table.insert(objTable.data.headTableArr, headTable)
        end
    end
    for i = 1, #objTable.data.headTableArr do
        local headTable = objTable.data.headTableArr[i]
        local roleData = data[i]
        if i > #data then
            headTable.headRoot.gameObject:SetActive(false)
        else
            headTable.headRoot.gameObject:SetActive(true)

            if i == maxNum and i ~= #data then
                headTable.dianzanGo:SetActive(false)
                headTable.iconRoot:SetActive(false)
                headTable.omitGo:SetActive(true)
            else
                headTable.dianzanGo:SetActive(true)
                headTable.omitGo:SetActive(false)
                headTable.faceItem:FrameEffectEnable(true, chatContentList.curOrder + 1, 1)
                local faceInfo = roleData.faceId
                if roleData.faceStr and not string.IsNullOrEmpty(roleData.faceStr) then
                    faceInfo = roleData.faceStr
                end
                headTable.faceItem:SetFaceInfo(faceInfo,
                        function()
                            --短按
                            chatContentList:ClickHeadCB(roleData)
                        end,
                        nil,
                        function()
                            --长按
                            if chatContentList.headLCB then
                                chatContentList.headLCB(data)
                            end
                        end)
                headTable.faceItem:SetFrameID(roleData.avatarFrame, true)
            end
        end
    end
end

---渲染联盟公告
local function RenderAllianceNotice(chatContentList, objTable, data)
    local ui_chat_data_gw = require "ui_chat_data_gw"
    ui_chat_data_gw.TrySetShowDeleteBtn(objTable.data)
    ui_chat_data_gw.SetAllianceNoticeItemData(objTable, data)
    ui_chat_data_gw.RegisterAllianceNoticeSelfBtn_Jump2Detail(objTable, data)
    objTable.data.showTranslateAndHideOriginal = false

end

---渲染联盟紧急公告
local function RenderAllianceUrgentNotice(chatContentList, objTable, data)
    local ui_chat_data_gw = require "ui_chat_data_gw"
    ui_chat_data_gw.TrySetShowDeleteBtn(objTable.data)
    ui_chat_data_gw.SetAllianceNoticeItemData(objTable, data)
    ui_chat_data_gw.RegisterAllianceNoticeSelfBtn_Jump2Detail(objTable, data)
    objTable.data.showTranslateAndHideOriginal = false
end

---渲染联盟频道公告
local function RenderChannelAllianceNotice(chatContentList, objTable, data)
    local objData = objTable.data
    local item = objData.instance:GetComponent(typeof(ScrollRectItem))
    local txt_content = item:Get("Content")
    local line = item:Get("line")
    local titileBg_normal = item:Get("titileBg_normal")
    local titileBg_urgent = item:Get("titileBg_urgent")
    --local btn_translate = item:Get("btn_translate") --额外函数处理翻译
    --local txt_content_translate = item:Get("Content2")--翻译有另外函数处理
    --正文内容
    txt_content.text = data.context

    objTable.data.showTranslateAndHideOriginal = false
    titileBg_normal:SetActive(data.sType == mq_common_pb.enSpeak_AnnounceForChannel)
    titileBg_urgent:SetActive(data.sType == mq_common_pb.enSpeak_UrgentAnnounceForChannel)
end

local function DisposeAllianceNotice(chatContentList, objTable)
    if objTable.data.btn_translate then
        objTable.data.btn_translate:RemoveAllListeners()
    end
    if objTable.data.btn_close then
        objTable.data.btn_close:RemoveAllListeners()
    end
    if objTable.data.btn_turnUrgent then
        objTable.data.btn_turnUrgent:RemoveAllListeners()
    end
end

function M:ClickHeadCB(data)
    if data.roleid ~= player_mgr.GetPlayerRoleID() then

        local pageState, curSessionData, isClose = chat_mgr_new.GetPageState()
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowRoleInfoView(data.roleid, pageState)

        --local pageState,curSessionData,isClose = chat_mgr_new.GetPageState()
        --local net_arena_module = require "net_arena_module"
        --net_arena_module.Send_PLAYER_DETAILS(data.roleid, common_new_pb.emDetailType_TopCE)
        --local ui_player_detail_info_ex = require "ui_player_detail_info_ex"
        --ui_player_detail_info_ex.SetHeroInfo(data.faceId, data.roleLv, data.name, data.roleid, nil, nil, nil, data.context, nil, data.avatarFrame)
        --ui_player_detail_info_ex.SetChatInfo(pageState)
        --ui_player_detail_info_ex.SetShowType(ui_player_detail_info_ex.Show_Type_Enum.Chat, nil, true)
        --ui_player_detail_info_ex.SetNationalFlag()
        --ui_player_detail_info_ex.ShowCoolieBtn(ui_player_detail_info_ex.Source_Window_Enum.Chat)
        --ui_window_mgr:ShowModule("ui_player_detail_info_ex")
        ----构建lineUps结构
        --local arena_pb = require "arena_pb"
        --local arenaLineUp = arena_pb.ArenaLineUp()
        --local lineUps = { [1] = arenaLineUp }
        --arenaLineUp.roleID = data.roleid
        --arenaLineUp.personalCE = data.ce
        --local dbid = data.roleid
        --ui_player_detail_info_ex.PlayerDetailFunc(nil, lineUps, data.guildname, nil, nil, data.faceId, data.name, data.roleLv, data.passStage, data.worldid, dbid, data.avatarFrame,nil,nil,nil,data.arrTreasure)
    end
end

function M:OnItemDispose(objTable)
    if openNew then
        if objTable.chatHandle then
            if objTable.chatHandle.disposeProcess then
                for _, func in ipairs(objTable.chatHandle.disposeProcess) do
                    func(self, objTable)
                end
            end
        end
        return
    end
    if objTable.style == 0 or objTable.style == 1 then
        --角色speak
        if objTable.data.roleFace then
            objTable.data.roleFace:Dispose()
        end
        if objTable.data.OnTranslateEvent then
            event.Unregister(event.TRANSLATE_RSP, objTable.data.OnTranslateEvent)
            objTable.data.OnTranslateEvent = nil
        end
    elseif objTable.style == 2 then
        --点赞combo
        for i = 1, #objTable.data.headTableArr do
            local headTable = objTable.data.headTableArr[i]
            if headTable.faceItem then
                headTable.faceItem:Dispose()
            end
        end
    end

    if objTable.data.iconUI then
        objTable.data.iconUI:Dispose()
        objTable.data.iconUI = nil
    end

    if objTable.data.heroUI then
        objTable.data.heroUI:Dispose()
    end
    if objTable.data.equipIconUI then
        objTable.data.equipIconUI:Dispose()
        objTable.data.equipIconUI = nil
    end
    if objTable.data.equipRecastIconUI then
        objTable.data.equipRecastIconUI:Dispose()
        objTable.data.equipRecastIconUI = nil
    end

    if objTable.data.shareGoodIconUI then
        objTable.data.shareGoodIconUI:Dispose()
        objTable.data.shareGoodIconUI = nil
    end

    if objTable.data.sociatyIconUI then
        objTable.data.sociatyIconUI:Dispose()
        objTable.data.sociatyIconUI = nil
    end

    if objTable.data.originalGoodsItem then
        objTable.data.originalGoodsItem:Dispose()
        objTable.data.originalGoodsItem = nil
    end
    if objTable.data.targetGooodsItem then
        objTable.data.targetGooodsItem:Dispose()
        objTable.data.targetGooodsItem = nil
    end
    if objTable.data.makeFoodsGameOrderObj then
        objTable.data.makeFoodsGameOrderObj.InvokeFunc = nil
    end
    if objTable.data.sandboxPosObj then
        event.Unregister(sand_ui_event_define.GW_SAND_SEARCH_MONSTER_DATA_Get)
        event.Unregister(sand_ui_event_define.GW_SAND_SEARCH_CARRIAGE_DATA_Get)
        objTable.data.sandboxPosObj:Dispose()
        objTable.data.sandboxPosObj = nil
    end
    if objTable.data.gatherTeamObj then
        timer_mgr:RemoveTimer(objTable.data.gatherTeamObj, timer)
        objTable.data.gatherTeamObj, timer = nil
        event.Unregister(sand_ui_event_define.GW_SAND_SHARE_DATA_CHANGE)
        objTable.data.gatherTeamObj:Dispose()
        objTable.data.gatherTeamObj = nil
    end
    if objTable.data.treasureObj then
        --event.Unregister(sand_ui_event_define.GW_SAND_SEARCH_MONSTER_DATA_Get)
        objTable.data.treasureObj:Dispose()
        objTable.data.treasureObj = nil
    end

    if objTable.data.achievementObj then
        timer_mgr:RemoveTimer(objTable.data.achievementObj, timer)
        objTable.data.achievementObj.timer = nil
        objTable.data.achievementObj:Dispose()
        objTable.data.achievementObj = nil
    end

    if objTable.data.ncOccupiedObj then
        objTable.data.ncOccupiedObj:Dispose()
        objTable.data.ncOccupiedObj = nil
    end
end
--屏蔽盟战跳转
function LocateToSociatyFight(_x, _y)
end

function FormatTime(timestamp)
    --local date = os.date("*t", nTime)
    --local hour = date.hour > 9 and tostring(date.hour) or "0" .. date.hour
    --local min = date.min > 9 and tostring(date.min) or "0" .. date.min
    --local month = date.month > 9 and tostring(date.month) or "0" .. date.month
    --local day = date.day > 9 and tostring(date.day) or "0" .. date.day
    --return string.format("[%s:%s %s-%s]", hour, min, month, day)
    if not timestamp then
        return ""
    end

    -- 当前时间的信息
    local currentTime = os.date("*t", os.time()) -- 当前时间
    local givenTime = os.date("*t", timestamp)  -- 传入的时间

    -- 判断是否是今天
    if currentTime.year == givenTime.year and currentTime.yday == givenTime.yday then
        -- 如果是今天，展示时:分
        return os.date("%H:%M", timestamp)
    else
        -- 如果不是今天，展示月-日 时:分
        return os.date("%m-%d %H:%M", timestamp)
    end
end

function M:SetUpdateHelp()
    self.isHelp = true
end

function M:SetHelpOther(id)
    self.helpOther = true
    self.helpOtherID = id
end

function InitChatHandle()
    local chat_handle_builder = require "chat_handle_builder"
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_Text)
            :InstantiateCommon()
            :InstantiateContentText()
            :InstantiateTranslateItem()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderTranslation, RenderContentText)
            :AddBeforeProcess(RegisterTranslateEvent)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_At)
            :InstantiateCommon()
            :InstantiateContentText()
            :InstantiateTranslateItem()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderTranslation, HandleAtName, RenderContentText)
            :AddBeforeProcess(RegisterTranslateEvent)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareSkin)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareSkin, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareHero)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareHero, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareGoods)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareGoods, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_FarmLottery)
            :InstantiateCommon()
            :InstantiateShareGood()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderFarmLottery)
            :AddDisposeProcess(DisposeShareGood)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareDecorate)
            :InstantiateCommon()
            :InstantiateEquipmentObject()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareDecorate)
            :AddDisposeProcess(DisposeShareEquipment)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareTreasureRare) --这个类型的点击显示的界面有问题，原本就有的问题，不清楚是否还在用
            :InstantiateCommon()
            :InstantiateEquipmentObject()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareTreasureRare)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AnniversaryJackpock)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleAnniversaryJackpock, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareEquip)
            :InstantiateCommon()
            :InstantiateEquipmentObject()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareEquipment)
            :AddDisposeProcess(DisposeShareEquipment)
            :Build()

    chat_handle_builder
            .New()
            :SetTarget(function(itemStyle, itemData)
        return (itemData.sType == mq_common_pb.enSpeak_ShareEquipRecast or itemData.sType == mq_common_pb.enSpeak_ShareEquipResonace)
                and (itemStyle == 0 or itemStyle == 1)
    end)
            :InstantiateCommon()
            :InstantiateEquipmentRecast()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareEquipmentRecast)
            :AddDisposeProcess(DisposeShareEquipmentRecast)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareLottery)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareLottery, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareBattle)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareBattle, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_VoidAreanSuc)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleVoidAreanSuc, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareMultiBattle)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareMultiBattle, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SharePitfall)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleSharePitfall, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_OccupyCell)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleOccupyCell, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_GetBossFirstBlood)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleGetBossFirstBlood, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_OfficialMark)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleOfficialMark, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareCell)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleShareCell, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_OccupyFortress)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleOccupyFortress, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_LangText)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleLangText, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_LeagueCompete)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleLeagueCompete, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SendFlower)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleSendFlower, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_Voice)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleVoice, RenderContentText)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_Like)
            :InstantiateCommon()
            :InstantiateLike()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderLike)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_LeagueRecruit)
            :InstantiateCommon()
            :InstantiateLeagueRecruit()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderLeagueRecruit)
            :AddDisposeProcess(DisposeLeagueRecruit)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_LeagueMutualHelp)
            :InstantiateCommon()
            :InstantiateLeagueMutualHelp()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderLeagueMutualHelp)
            :AddDisposeProcess(DisposeLeagueMutualHelp)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ChineseRedPacket)
            :SafeInstantiateCheckItemExist("RedEnvelopeObj")
            :InstantiateCommon()
            :InstantiateChineseRedPacket()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderChineseRedPacket)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareStarDiamond)
            :InstantiateCommon()
            :InstantiateShareGood()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareStarDiamond)
            :AddDisposeProcess(DisposeShareGood)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ShareLabourDayInfo)
            :SafeInstantiateCheckItemExist("LaborDayObj")
            :InstantiateCommon()
            :InstantiateLabourDay()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderShareLabourDayInfo)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_DimensionWarAssemble)
            :SafeInstantiateCheckItemExist("GatherObj")
            :InstantiateCommon()
            :InstantiateDimensionWarAssemble()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderDimensionWarAssemble)
            :Build()

    chat_handle_builder
            .New()
    --:SetTargetType(mq_common_pb.enSpeak_DimensionWarOccupy)
    --:SetTargetType(mq_common_pb.enSpeak_SandboxTreasureFinish)

            :SafeInstantiateCheckItemExist("OccupyObj")
            :InstantiateDimensionWarOccupy()
            :AddProcess(RenderDimensionWarOccupy)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ChristmasTeamData)
            :SafeInstantiateCheckItemExist("LaborDayObj")
            :InstantiateCommon()
            :InstantiateLabourDay()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderChristmasTeamData)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_PickTheRouteShareArchived)
            :SafeInstantiateCheckItemExist("PickTheRouteObj")
            :InstantiateCommon()
            :InstantiatePickTheRouteShareArchived()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderPickTheRouteShareArchived)
            :Build()

    chat_handle_builder
            .New()
            :SetTarget(function(itemStyle, itemData)
        return itemStyle == 2
    end)
            :SetInstantiate(function(chatContentList, itemStyle, parentTransform)
        local itemGameObject = GameObject.Instantiate(chatContentList.listItems:Find("comboItem"), parentTransform)

        local objTable = { obj = itemGameObject, transform = itemGameObject.transform, style = itemStyle, layoutElement = itemGameObject:GetComponent(LayoutElementType), new = true }
        objTable.data = {}
        objTable.data.comboNumText = itemGameObject:Find("comboInfo/Text"):GetComponent(typeof(UI.Text))
        objTable.data.headTemplate = itemGameObject:Find("head")
        objTable.data.headTableArr = {}  --{{headRoot = xx,faceItem = xx,dianzanGo = xx,omitGo = xx},{},...}
        return objTable
    end)
            :AddProcess(RenderLikeCombo)
            :AddDisposeProcess(DisposeLikeCombo)
            :Build()

    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SpringFestivalBlessing)
            :InstantiateCommon()
            :InstantiateContentText()
            :AddProcess(RenderHead, RenderTitle, RenderVip, HandleVoidAreanSuc, RenderContentText)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllSaintsDay_Pumpkin)
            :SafeInstantiateCheckItemExist("pumnkinShareObj")
            :InstantiateCommon()
            :InstantiatePumnkinShareArchived()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderPumnkinShareArchived)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_MakeFoodInfo)
            :SafeInstantiateCheckItemExist("MakeFoodsGameOrderObj")
            :InstantiateCommon()
            :InstantiateMakeFoodInfoShareArchived()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderMakeFoodInfoShareArchived)
            :AddDisposeProcess(DisposeMakeFoodInfoShareOrder)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_WMTopRaceGuess)
            :InstantiatePeakGameSupport()
            :AddProcess(RenderWMTopRaceGuess)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_CarriageTruckShare)
            :InstantiateCommon()
            :InstantiateCarriageDetail()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderCarriageDetailShare)
            :Build()
    ------新增沙盘定位分享类型-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SandboxMarkPos)
            :InstantiateCommon()
            :InstantiateSandBoxPosition()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderSandBoxSharePosition)
            :Build()
    ----------------------------------
    ------新增灭火定位分享类型-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_CityFireShare)
            :InstantiateCommon()
            :InstantiateFireCityPosition()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderCityFirePosition)
            :Build()
    ----------------------------------    
    ------新增游荡怪集结分享-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SandboxMass)
            :InstantiateCommon()
            :InstantiateSandBoxMass()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderSandBoxShareMass)
            :AddRecoveryProcess(DisposeSandBoxShareMass)
            :Build()
    ----------------------------------
    ------新增雷达挖掘宝藏分享-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SandboxTreasure)
            :InstantiateCommon()
            :InstantiateTreasurePosition()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderRadarTreasureSharePos)
            :Build()
    ----------------------------------

    ------新增酒馆任务分享-----------
    --chat_handle_builder
    --        .New()
    --        :SetTargetType(mq_common_pb.enSpeak_SandboxTreasure)
    --        :InstantiateCommon()
    --        :InstantiateTavernPosition()
    --        :AddProcess(RenderHead, RenderTitle, RenderVip, RenderTavernTaskSharePos)
    --        :Build()
    ----------------------------------

    ----新增挖掘宝藏完成通知-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SandboxTreasureFinish)
            :SafeInstantiateCheckItemExist("DigTreasureObj")
    --:SafeInstantiateCheckItemExist("OccupyObj")
            :InstantiateDigTreasure()
            :AddProcess(RenderDigTreasure)
            :Build()
    ----------------------------------

    ----新增双倍奖励通知-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_SandboxKastenboxMultipleReward)
            :SafeInstantiateCheckItemExist("DoubleRewardObj")
            :InstantiateDoubleReward()
            :AddProcess(RenderDoubleReward)
            :Build()
    ----------------------------------

    ------新增联盟成就分享-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_LeagueAchievement)
            :InstantiateCommon()
            :InstantiateAchievementShare()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderAllianceAchievement)
            :Build()
    ----------------------------------

    ------新增中立城池占领信息-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_NC_Occupied)
            :InstantiateOccupyNeutralCity()
            :AddProcess(RenderOccupyNeutralCity)
            :AddRecoveryProcess(DisposeOccupyNeutralCity)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_NC_Abandon)
            :InstantiateAbandonNeutralCity()
            :AddProcess(RenderAbandonNeutralCity)
            :Build()
    --------------------------------
    ------新增普通联盟公告-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_Announcement)
            :InstantiateAllianceNotice()
            :InstantiateTranslateItem()
            :AddBeforeProcess(RegisterTranslateEvent, SetTranslateLogo2Root_AllianceNotice)
            :AddProcess(RenderAllianceNotice, RenderTranslation)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()
    -----------------------------------

    ----新增紧急联盟公告-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_UrgentAnnouncement)
            :InstantiateAllianceUrgentNotice()
            :InstantiateTranslateItem()
            :AddBeforeProcess(RegisterTranslateEvent, SetTranslateLogo2Root_AllianceNotice)
            :AddProcess(RenderAllianceUrgentNotice, RenderTranslation)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()
    --------------------------------
    ----新增联盟聊天普通公告-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AnnounceForChannel)
            :InstantiateCommon()
            :InstantiateChannelAllianceNotice()
            :InstantiateTranslateItem()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderChannelAllianceNotice, RenderTranslation)
            :AddBeforeProcess(RegisterTranslateEvent)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()
    --------------------------------

    ----新增联盟聊天紧急公告-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_UrgentAnnounceForChannel)
            :InstantiateCommon()
            :InstantiateChannelUrgentAllianceNotice()
            :InstantiateTranslateItem()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderChannelAllianceNotice, RenderTranslation)
            :AddBeforeProcess(RegisterTranslateEvent)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()
    --------------------------------
    ------新增联盟火车分享类型-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllianceTrain)
            :InstantiateCommon()
            :InstantiateAllianceTrainShare()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderAllianceTrainSharePosition)
            :AddRecoveryProcess(DisposeAllianceTrainUI)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllianceTrainPush)
            :InstantiateAllianceTrainDetail()
            :AddProcess(RendeAllianceTrainDetail)
            :AddRecoveryProcess(DisposeAllianceTrainUI)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllianceTrainPlunder)
            :InstantiateAllianceTrainBattle()
            :AddProcess(RendeAllianceTrainBattle)
            :AddRecoveryProcess(DisposeAllianceTrainUI)
            :Build()
    --------------------------------
    ------新增酒馆藏宝图分享类型-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AcornPubTreasure)
            :InstantiateCommon()
            :InstantiateTavernTreasure()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderTavernTreasure)
            :Build()
    ------新增集结大作战分享类型-----------
    --chat_handle_builder
    --        .New()
    --        :SetTargetType(mq_common_pb.enSpeak_Gathering)
    --        :InstantiateWarRally()
    --        :AddProcess(RenderWarRallyShare)
    --        :AddRecoveryProcess(DisposeWarRallyShare)
    --        :Build()
    --------------------------------
    --惊喜盒分享
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_GoldenEggs)
            :InstantiateCommon()
            :InstantiateSurpriseBag()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderSurPriseBag)
            :AddRecoveryProcess(DisposeSurpriseBagUI)
            :Build()
    --惊喜盒运气最佳
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_GoldenEggsMaxReward)
            :InstantiateSurpriseBagLuckly()
            :AddProcess(RenderSurPriseBagLuckly)
            :Build()
    --增援私聊
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_ReinforcePrivateChat)
            :InstantiateCommon()
            :InstantiateContentText()
            :InstantiateTranslateItem()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderTranslation, RenderContentText)
            :AddBeforeProcess(RegisterTranslateEvent)
            :AddRecoveryProcess(DisposeTranslateItem)
            :Build()
    ------新增联盟邀请分享类型-----------
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllianceShare)
            :InstantiateCommon()
            :InstantiateAllianceShare()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderAllianceShare)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_AllianceOutFire)
            :InstantiateCommon()
            :InstantiateGetLikeObj()
            :AddProcess(RenderHead, RenderTitle, RenderVip, RenderGetLikeObj)
            :Build()

    --新聊天版本提示
    chat_handle_builder
            .New()
            :SetTargetType(chat_mgr_pro.GetOldChatTargetTypeMsg())
            :InstantiateNotStyleObj()
            :AddProcess(RenderNotStyleObj)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_BattleVicReportShare)
            :InstantiateCommon()
            :InstantiateBattleReportSareObj()
            :AddProcess(RenderHead, RenderTitle, RenderVip,RenderBattleReportShareObj)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_BattleDefReportShare)
            :InstantiateCommon()
            :InstantiateBattleReportSareObj()
            :AddProcess(RenderHead, RenderTitle, RenderVip,RenderBattleReportShareObj)
            :Build()
    chat_handle_builder
            .New()
            :SetTargetType(mq_common_pb.enSpeak_DetectReportShare)
            :InstantiateCommon()
            :InstantiateDetectReportSareObj()
            :AddProcess(RenderHead, RenderTitle, RenderVip,RenderDetectReportShareObj)
            :Build()
    
end

InitChatHandle()

local class = require "class"
local object = require "object"
NewObject = class(object, nil, M)