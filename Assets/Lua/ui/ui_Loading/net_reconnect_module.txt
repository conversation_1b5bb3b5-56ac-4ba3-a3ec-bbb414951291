---------------------------------------------------------
-- 网络重连模块
-- 许德纪
-- 2017-11-16 
---------------------------------------------------------
local require = require
local print = print
local tostring = tostring

local game = require "game"
local event = require "event"
local net_login_module = require "net_login_module"
local net = require "net"
local gateway_pb = require "gateway_pb"
local msg_pb = require "msg_pb"
local net_route = require "net_route"
local log = require "log"
local Lang = require "lang_res_key"
local lang = require "lang"
local net_logic_module = require "net_logic_module"
local error_code_pb = require "error_code_pb"
local util = require "util"
local windowMgr = require "ui_window_mgr"
local log_debug = require "log_debug"

local Time = CS.UnityEngine.Time
local ConnectErrorCode = CS.War.Script.Net.ConnectErrorCode
local SOCKET_ERROR = CS.War.Base.SOCKET_ERROR
local MainLoop = CS.War.Script.MainLoop

module("net_reconnect_module")

--[[
RESULT_YES = 1
RESULT_NO = 2

KEY_DISCONNECT = 4									-- 断线消息
KEY_CONNENT_BATTLE_FAIL = 5							-- 连接战斗服失败
KEY_CONNENT_LOBBY_FAIL = 6							-- 连接大厅服失败
KEY_LOGIN_FAIL = 7									-- 登陆错误
]]

-- 重连超时时间
local timeout = 30
local stopConnect = false
local reconnectTicker = nil

-- 记录断线重连逻辑
local reconnect_info = 
{
	pauseStatus = false, 	--是否切后台
	toFrontTime = 0,		--后台切换到前台时间
	reconnectTime = 0,		--发起重连打点时间
	isReconnecting = false,	--是否正在重连
	isConnectSuccessed = false, --是否重连成功,仅统计断线重连，不统计正常登录失败
	intervalSinceLast = 9999999, --秒，距离上次断线重连默认间隔时间。 一个月为 60 * 60 * 24 * 30 = 2592000 s
	accumulatedInterval = 120, -- 秒，在此时间内对触发次数进行累计
	triggerTimes = 0, -- accumulatedInterval 时间内触发断线重连累计次数
}

--成功连接,关闭窗口
function OnConnectSucceess()
	------print("成功连接")
	--log_debug.LogWarning("net_reconnect_module 接到连接成功消息，关闭计时器等操作", "Net Debug")
	local MsgBox = require "message_box"
	MsgBox.Close()
	if windowMgr:IsModuleShown("net_reloading") then
		--log_debug.LogWarning("取消断网提示", "Net Debug")
		windowMgr:UnloadModule("net_reloading")	
	end
	if reconnectTicker then
		reconnectTicker.co = nil
		reconnectTicker = nil
	end
	stopConnect = false

	-- 处理重连打点数据
	--log.Warning("reconnect, 连接成功")
	reconnect_info.isConnectSuccessed = true
	if not reconnect_info.isReconnecting then
		return
	end
	--log.Warning("reconnect, 重连成功")
	event.Trigger(event.NET_RECONNECT_SUCCESS_EVENT)
	reconnect_info.isReconnecting = false
	event.EventReport("net_reconnect_success", 
	{
		reason = reconnect_info.reason
	})
end

--1断开连接
function OnDisconnect(eventName, nConnectCount)
	if net.IsConnected() == true then
		log_debug.LogWarning("检测到网络异常，但net.IsConnected()检测正常，未处理异常", "Net Debug")
		return
	end
	------print("断网重新连接大厅") 
	net_login_module.ConnectLobby() --重新连接大厅
end

--连接大厅失败
function OnLobbyConnectFailed(eventName, errCode)
	--MsgBox.Close()
	------print("连接大厅失败", errCode) 
	if 0 ~= errCode then
		if not reconnectTicker then
			if stopConnect == true then
				stopConnect = false
			else
				log_debug.LogWarning("显示断网提示", "Net Debug")
				windowMgr:ShowModule("net_reloading")
				net_login_module.ConnectLobby()
			end
			
            log_debug.LogWarning("连接网络失败，开启超时计时器", "Net Debug")

			reconnectTicker = util.DelayCall(timeout, function()
				if reconnectTicker then
					reconnectTicker.co = nil
					reconnectTicker = nil
				end
				if windowMgr:IsModuleShown("net_reloading") then
					log_debug.LogWarning("取消断网提示", "Net Debug")
					windowMgr:UnloadModule("net_reloading")	
				end
				if net.IsConnected() == true then
					return
				end	
				stopConnect = true

    			if windowMgr:IsModuleShown("ui_inotice") then
        			-- 显示服务器维护中弹窗时不弹窗 "网络连接异常,请重新登陆"
        			log_debug.LogWarning("连接网络"..timeout.."秒超时，已显示服务器维护中，不弹出重连失败窗口", "Net Debug")
        		else
        			log_debug.LogWarning("连接网络"..timeout.."秒超时，弹出重连失败窗口", "Net Debug")
					local MsgBox = require "message_box"
					MsgBox.Open(Lang.KEY_CONNENT_LOBBY_FAIL, MsgBox.STYLE_YES, OnReLogin, 0, Lang.KEY_OK,Lang.KEY_CANCEL,nil,MsgBox.en_msgbox_net_type)

					-- 连接超时弹窗上报
					OnNetErrorConnectFailed()
    			end

				event.Trigger("SERVERERROR_LOG_INFO",tostring(errCode),lang.Get(Lang.KEY_CONNENT_LOBBY_FAIL))
				ReportOfflineEvent(errCode, lang.Get(Lang.KEY_CONNENT_LOBBY_FAIL))
			end)
		else
			log_debug.LogWarning("显示断网提示", "Net Debug")
			windowMgr:ShowModule("net_reloading")
			log_debug.LogWarning("检测到网络连接失败，且已在超时计时中，发起重连", "Net Debug")
			net_login_module.ConnectLobby() --重新连接大厅
		end
	end
end

--返回登陆界面
function OnReLogin()
	local login_module = require "net_login_module"
	login_module.ReturnLogin()
end

--返回登陆界面自动登录
function OnAutoReLogin()
	local login_module = require "net_login_module"
	login_module.AutoReturnLogin()
end

-- 通知客户端断开连接
function OnDisconnectClient(msg)  
	net.Disconnect()
    
    --通知踢人
    local event = require "event"
    event.Trigger(event.KICKOUT_CLIENT)
    
	-- 打个error
	if 0 ~= msg.errorCode then

		if net.IsConnected() == true then
			return
		end

		local errorMsg = lang.Get(Lang.KEY_RECONNECT)
		if msg.errorCode == error_code_pb.EKickoutClient_Reason_NoPing then
			-- 心跳包踢人，客户端再尝试重连
			windowMgr:ShowModule("net_reloading")
			net_login_module.ConnectLobby()
			-- 服务器心跳包超时重连
			OnNetReconnectErrorEvent({ reason = "server no ping" })
		else
			function okCall() 
				--2022.8.17 停服过程，返回到登录界面时，重新拉下serverinfo刷新服务器列表----start
				if msg.errorCode == error_code_pb.EKickoutClient_Reason_StopZoneSvr then
					print("zzd____停服过程，返回到登录界面时，重新拉下serverinfo")
					local server_data = require "server_data"
					server_data.ReqData()
				end
				--2022.8.17 停服过程，返回到登录界面时，重新拉下serverinfo刷新服务器列表----end
				OnReLogin()
			end
			
			local strMsg = ""
			if msg.errorCode == error_code_pb.EKKickoutClient_Reason_Reconnect then
				strMsg = lang.Get(Lang.KEY_LOGIN_IN_OTHER) 
			else
				strMsg = lang.Get(Lang.KEY_DISCONNECT_SERVER) .." "..lang.Get(Lang.KEY_ERROR_CODE)..":"..msg.errorCode
			end
			errorMsg = strMsg
			local MsgBox = require "message_box"
			MsgBox.Open(strMsg , MsgBox.STYLE_YES,okCall, 0, Lang.KEY_OK,Lang.KEY_CANCEL,nil,MsgBox.en_msgbox_net_type)
			-- 上报掉线
			ReportOfflineEvent(msg.errorCode, errorMsg)
			-- 服务器踢人重连
			OnNetReconnectErrorEvent({ reason = "server error:"..tostring(errorMsg) })
		end
	end
end

function ReportOfflineEvent(err, strMsg)
	if not game.actors or #game.actors <= 0 then
		return
	end

    event.Trigger("SERVERERROR_LOG_INFO",err,strMsg)

	local q1sdk = require "q1sdk"
	local player_mgr = require "player_mgr"
	local roleID = player_mgr.GetPlayerRoleID()
	local userID = player_mgr.GetPlayerUserID()
	if roleID == nil or userID == nil then
		return
	end
	local setting_server_data = require "setting_server_data"
	local worldID = setting_server_data.GetLoginWorldID()
	local roleName = player_mgr.GetRoleName()
	local roleLv = player_mgr.GetPlayerLV()
	q1sdk.UserEvent(worldID, roleID, roleName, roleLv, userID, "offLine", "error="..tostring(err)..";errorMsg="..strMsg)
end

function RegisterBackgroundListener(bRegister)
	if not MainLoop.Instance.RegisterApplicationPauseHandler then
		log.Error("MainLoop 尚未初始化")
		return
    end
    if bRegister then
    	MainLoop.Instance:RegisterApplicationPauseHandler(OnApplicationPause)
    else
    	MainLoop.Instance:UnRegisterApplicationPauseHandler(OnApplicationPause)
    end
end

-- net_login_module 重连失败调用处理
-- errCode : ConnectErrorCode
-- public enum ConnectErrorCode
-- {
--     Unknow = -1,
--     Success = 0,
--     Error = 1,
--     Lost = 2
-- }
-- errReason : SOCKET_ERROR
-- public enum SOCKET_ERROR
-- {
--     SOCKET_ERROR_NON = 0,
--     SOCKET_ERROR_CONNECT_FAIL, //建立连接失败
--     SOCKET_ERROR_CONNECT_CLOSE, //连接错误，接收的字节是0
--     SOCKET_ERROR_CONNECT_ERROR, //连接错误
--     SOCKET_ERROR_NO_CONNECT ,   //网络不通，客户端没有连接
--     SOCKET_ERROR_SERVER_DISCONNECT , //服务器主动断开连接
--     SOCKET_ERROR_CLIENT_DISCONNECT , //客户端主动断开连接
--     SOCKET_ERROR_CLIENT_HANDSHAKE_TIMEOUT , //客户端握手心跳超时,主动断开网络
--     SOCKET_ERROR_SERVER_HANDSHAKE_DISCONNECT , //服务器握手心跳超时,通知断开网络
--    

--     SOCKET_ERROR_MAX ,
-- }
local ConnectErrorCode2Str = 
{
	[ConnectErrorCode.Unknow] = "Unknow",
	[ConnectErrorCode.Success] = "Success",
	[ConnectErrorCode.Error] = "Error",
	[ConnectErrorCode.Lost] = "Lost",
}
local SOCKET_ERROR2Str = 
{
	[SOCKET_ERROR.SOCKET_ERROR_NON] = "None",
	[SOCKET_ERROR.SOCKET_ERROR_CONNECT_FAIL] = "建立连接失败",
	[SOCKET_ERROR.SOCKET_ERROR_CONNECT_CLOSE] = "连接错误，接收的字节是0",
	[SOCKET_ERROR.SOCKET_ERROR_CONNECT_ERROR] = "连接错误",
	[SOCKET_ERROR.SOCKET_ERROR_NO_CONNECT] = "网络不通，客户端没有连接",
	[SOCKET_ERROR.SOCKET_ERROR_SERVER_DISCONNECT] = "服务器主动断开连接",
	[SOCKET_ERROR.SOCKET_ERROR_CLIENT_DISCONNECT] = "客户端主动断开连接",
	[SOCKET_ERROR.SOCKET_ERROR_CLIENT_HANDSHAKE_TIMEOUT] = "客户端握手心跳超时,主动断开网络",
	[SOCKET_ERROR.SOCKET_ERROR_SERVER_HANDSHAKE_DISCONNECT] = "服务器握手心跳超时,通知断开网络",
}
-- 连接网关失败错误处理
function OnConnectGatewayError(reason, netErrCode, netErrReason)
	if not reconnect_info.isConnectSuccessed then
		return
	end
	reconnect_info.isConnectSuccessed = false

	log.Warning("reconnect, 网关中断上报")
    local reasonTable = 
    {
    	reason = reason,
    	net_err_code = tostring(ConnectErrorCode2Str[netErrCode]),
    	net_err_reason = tostring(SOCKET_ERROR2Str[netErrReason])
    }

	event.Trigger("SERVERERROR_LOG_INFO",tostring(net_err_code),net_err_reason)
    -- 网关网络中断上报
    OnNetReconnectErrorEvent(reasonTable)
end

-- 多次重连失败，上报弹出连接失败提示窗
function OnNetErrorConnectFailed()
 	local reasonTable = {}
 	-- 区分连接失败弹窗原因，是否为重连失败，还是主动登录失败
 	if reconnect_info.isReconnecting then
 		reasonTable.reason = "reconnect failed"
 	else
 		reasonTable.reason = "login connect failed"
 	end
 	event.EventReport("connect_failed_window", reasonTable)
 end

-- 上报重连事件
function OnNetReconnectErrorEvent(reasonTable)
	if reconnect_info.pauseStatus then
		-- 如果在后台触发的重连，不加入统计
		log.Warning("reconnect, 后台中断线重连不统计")
		return
	end

	local intervalFromBackground = 0
	if reconnect_info.toFrontTime > 0 then
		intervalFromBackground = Time.realtimeSinceStartup - reconnect_info.toFrontTime
		if intervalFromBackground < net_logic_module.GetPingTimeOutInterval() + 10 then
			-- 如果从后台切换回来 pingTriggerConnect + 10 秒 内触发重连，也不计入统计
			log.Warning("reconnect, 后台切换回前台断线重连不统计")
			return
		end
	end

	log.Warning("reconnect, 断线重连统计", reasonTable.reason)

	if reconnect_info.reconnectTime > 0 then
		reconnect_info.intervalSinceLast = Time.realtimeSinceStartup - reconnect_info.reconnectTime
		if reconnect_info.intervalSinceLast < reconnect_info.accumulatedInterval then
			reconnect_info.triggerTimes = reconnect_info.triggerTimes + 1
		else
			reconnect_info.triggerTimes = 0
		end
	end
	reconnect_info.reconnectTime = Time.realtimeSinceStartup
	reconnect_info.isReconnecting = true
	reconnect_info.reason = reasonTable.reason

	reasonTable.interval_since_last = reconnect_info.intervalSinceLast
	reasonTable.accumulate_trigger_times = reconnect_info.triggerTimes
	reasonTable.lastest_background_interval = intervalFromBackground

	event.Trigger("SERVERERROR_LOG_INFO","连接失败",reasonTable.reason)
	event.EventReport("net_reconnect_error", reasonTable)
end

-- 主动断开网络，不纳入断线重连统计，包括主动切换服务器，切换账号等原因引起主动断线
function DisconnectActivity()
	log.Warning("reconnect, 主动断线，不参与统计")

	event.Trigger("SERVERERROR_LOG_INFO","主动断线，不参与统计","")
	reconnect_info.isConnectSuccessed = false
end

function OnApplicationPause(pauseStatus)
	log.Warning("reconnect, 后台切换", pauseStatus)
	if (not pauseStatus) and reconnect_info.pauseStatus then
		-- 更新切后台时间
		reconnect_info.toFrontTime = Time.realtimeSinceStartup
	end
	reconnect_info.pauseStatus = pauseStatus
end

function Init()
	RegisterBackgroundListener(true)
end

Init()

--订阅连接失败消息
event.Register(event.CONNECT_FAILED_DISCONNECT, OnDisconnect)          -- 网关连接失败  刚开始断线 断线30s之后还没有连上就弹框登录到大厅
event.Register(event.CONNECT_SUCCESS, OnConnectSucceess)               -- 网关连接成功
event.Register(event.CONNECT_FAILED_LOBBY, OnLobbyConnectFailed)       -- 1min没收到服务器心跳   网关连接不上 

local MessageTable = 
{
	-- 服务器主动通知断开
	{msg_pb.MSG_GATEWAY_KICKOUT_CLIENT,    				OnDisconnectClient,         			gateway_pb.TMSG_GATEWAY_KICKOUT_CLIENT, },
}

net_route.RegisterMsgHandlers(MessageTable)