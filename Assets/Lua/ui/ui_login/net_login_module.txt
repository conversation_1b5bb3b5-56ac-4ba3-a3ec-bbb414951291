local require = require
local print = print
local string = string
local tonumber = tonumber
local tostring = tostring
local table = table
local os = os

local log = require "login_log"
local game = require "game"
local event = require "event"
local Lang = require "lang_util"
local lang = require "lang"
local activity_indicator = require "activity_indicator"
local AutoLogin = require "ui_iauto_login"
local util = require "util"
local lobby_pb = require "account_pb"
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local ui_setting_data = require "ui_setting_data"
local q1sdk = require "q1sdk"
local goldfinger_pb = require "goldfinger_pb"
local game_config = require "game_config"
local prop_pb = require "prop_pb"
local error_code_pb = require "error_code_pb"
local Debug = CS.UnityEngine.Debug
local GameObject = CS.UnityEngine.GameObject

local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey
local Time = CS.UnityEngine.Time
local Utility = CS.War.Script.Utility
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local WWW = CS.UnityEngine.WWW

module("net_login_module")

local net = require "net"
local net_route = require "net_route"
local msg_pb = require "msg_pb"
local login_pb = require "login_pb"
local net_script = require "net_script"
local net_gateway_module = require "net_gateway_module"

local verifying = false
local delayCallKey = nil

local userRealAge = 0
local u8UserInfo = {}

-- 是否切换服务器
local isSwitchServer = false


--登陆的现场记录在这里
local loginContext  = login_pb.TMSG_LOGIN_LOGIN_REQ()

--自动登陆的key
local loginKey = ""

--是否自动登陆
local bAutoLogin = false

-- 是否自动返回大厅
local bAutoReturnLoby = false

--大厅场景的索引
local ROBBY_SCENE_INDEX = -1
local ROBBY_SCENE_ID = 3

--服务器时间,lua中无参数调用：os.time(), 直接返回time(NULL)，即UNIX时间戳，也即UTC时间 1970-01-01 00:00:00 至今的秒数
--此时初始化后，未经过服务器时间校准前，会受到本地时间修改影响
local serverTime = os.time()
local serverZone = 0
local serverTimeUpdateTick = Time.realtimeSinceStartup
local serverNextZeroTime = {time = 0,hasTrigger = false} --下一次零点时间戳

local crossDayTicker = nil 
--返回大厅现场
local returnLobbyContext = {}
	--[[
	returnLobbyContext.lobbyIP = {}  --ip 
	returnLobbyContext.lobbyPort= {}--端口
	returnLobbyContext.lobbyUDPPort ={}
	returnLobbyContext.lobbysvrid = {} -- 大厅服ID
	returnLobbyContext.dbid= 0 
	returnLobbyContext.loginKey= ""
	]]
	
	
-- 进入战场的现场,服务器下发的切换现场
--重新连接大厅现场
local enterBattleMsgContext = {}
local LoginedBCUserID = nil

LoginLastWindows = {}
ACCOUNT_ISBIND = 0

--用于打点上报
local recActorInfoTime = 0

local isLoginServerMaintenance = false

--握手消息回复的区ID（合区后的区服id）
local handShakeWorldID = 0

-- 大区id
local areaID = 0

function PushLastWindow(name)
	table.insert(LoginLastWindows, name)
end

function PopLastWindow()
	if #LoginLastWindows > 0 then
		local name = LoginLastWindows[#LoginLastWindows]
		table.remove(LoginLastWindows, #LoginLastWindows)
		return name
	end
	return nil
end

function GetSvrIDUserID()
	return returnLobbyContext.stALSess.iLobbySvrID, returnLobbyContext.stALSess.iUserDBID
end

function GetUserIDRoleID()
	return returnLobbyContext.stALSess.iUserDBID, returnLobbyContext.stALSess.iRoleDBID
end

function GetAlSession()
	return returnLobbyContext.stALSess
end

function GetBCUserID()
	return LoginedBCUserID or ""
end

function GetPartnerID()
	return loginContext.stLoginData.partnerId
end

function SetPartnerID(partnerID)
	loginContext.stLoginData.partnerId = partnerID
end

function IsLoginServerMaintenance()
	if isLoginServerMaintenance then
		log.DirectWarning("服务器维护中")
	end
	return isLoginServerMaintenance
end

--[[登录回复]]
function S2CLoginRsp(msg)
	---- print("登录回复返回的值：" .. msg.errCode, msg.curTime, Time.time, Time.realtimeSinceStartup)
	log.LoginWarning("登录回复返回的值：", msg.errCode, msg.curTime, Time.time, Time.realtimeSinceStartup)

	local ui_login_main_mgr = require "ui_login_main_mgr"
	local isLogined = ui_login_main_mgr.GetOnceLogined()

	event.RecodeTrigger("receive_s2c_login_rsp", {
		isLogined = isLogined
	})
	if msg and msg.errCode and msg.errCode ~= 0 then
		event.EventReport("receive_s2c_login_error_code", {
			error_code = msg.errCode
		})
	end
	
	event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT,{mark = "receive_s2c_login_rsp"})

	serverTimeUpdateTick = Time.realtimeSinceStartup
	serverTime = msg.curTime
	
	local server_data = require "server_data"
   	server_data.UpdateServerTime()

	isLoginServerMaintenance = (msg.errCode == error_code_pb.enErr_Login_ServerMaintenance)
	
	--if msg.errCode ~= 0 then
		event.Trigger(event.LOGIN_RESPONSE_ERROR, msg.errCode, msg.allowTime)
	--end

	util.PeekWatch("Login", "收到登录回复消息")
	--EnterLobby()
end

function S2CHandShake(msg)
	--发送登陆消息
	print("发送登录消息", msg.errCode)
	if 0 ~= msg.errCode then
		local strTips = lang.Get(Lang.KEY_LOGIN_FAIL)..","..lang.Get(Lang.KEY_ERROR_CODE)..":".. msg.errCode
		local systemTitle = Lang.KEY_SYSTEM_TIPS

		log.LoginWarning("服务器握手失败", systemTitle, strTips)
		event.RecodeTrigger("handshake_error", {error_code = tonumber(msg.errCode)})

		event.Trigger("SERVERERROR_LOG_INFO", tostring(msg.errCode), "服务器握手失败")

		net.Disconnect()
		local message_box = require "message_box"
		function okCall(callbackData, nRet) 
			if message_box.RESULT_YES == nRet then
				ReturnLogin()
			end
		end
		message_box.Open(strTips, message_box.STYLE_YES, okCall, 0, Lang.KEY_OK,Lang.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type)
		return
	end
	handShakeWorldID = msg.worldID
	LoginGameServer(msg.gameWorldName)
end

function LoginGameServer(gameWorldName)
	log.LoginWarning("发送登录消息")
	event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_发送登录消息")
	--手动登陆
	if bAutoLogin==false then
		log.LoginWarning("LoginServer")
		LoginServer(gameWorldName)
	else
		log.LoginWarning("AutoLoginServer")
		AutoLoginServer()  --自动登陆
	end
end

--获取握手消息回复的区ID
function  GetHandShakeWorldID()
	return handShakeWorldID
end

--[[
local OnBeforeLoadMap = nil
OnBeforeLoadMap = function()
	event.Unregister(event.BEFORE_SCENE_LOAD, OnBeforeLoadMap)
	--这里要先判断一下当前
	game.UnloadMap(ROBBY_SCENE_ID,ROBBY_SCENE_INDEX)
end
]]

local OnSceneLoaded = nil
OnSceneLoaded = function ()
    -- Debug.LogWarning("OnSceneLoaded>>>>>>>>>>>> ")
    log.LoginWarning("场景加载完成")

	event.Unregister(event.SCENE_LOADED, OnSceneLoaded)
	--event.Register(event.BEFORE_SCENE_LOAD, OnBeforeLoadMap)
	if game.actors and #game.actors > 0 then
		--发送登陆完成事件
		 event.Trigger(event.ACTOR_LOGIN_FINISH, nil)	
	else
		game.EnterState(game.STATE_CREATE_ACTOR)
	end

end

function S2CActorInfo(actorInfo)
	recActorInfoTime = os.clock()
    -- Debug.LogWarning("receive actor info>>>>>>>>>>>> ")
    util.PeekWatch("Login", "登录收到角色数据")
    log.LoginWarning("登录收到角色数据")
    event.RecodeTrigger("receive_actor_info", {})
	event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT,{mark = "receive_actor_info"})
	event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_收到登录回复")
	-- 这里提前赋值，创建角色verifying就return了，赋不上值
	if actorInfo ~= nil then
        game.actors = actorInfo.actor
        if game.actors and #game.actors > 0 then
            SetLoginAreaID(tonumber(game.actors[1].areaID))
        end
    end

	--log.WarningFormat("客户端收到：角色数据({0})", msg_pb.MSG_LOGIN_ACTOR_INFO_NTF)
	event.Trigger(event.RECEIVE_ACTORINFO_MSG, actorInfo)
	activity_indicator.Hide("Login")
	
	local send_storyMsg = require "send_storyMsg"
	send_storyMsg.OnEnterStroyReq()

	--接受角色数据时不再关闭自动登录提示界面，AutoLogin跟随ui_login_main一起关闭
	local ui_login_main = require "ui_login_main"
	if not ui_login_main.IsVisible() then
		AutoLogin.Close()
	end

	if verifying == false then
		--发送登陆完成事件
		event.Trigger(event.ACTOR_LOGIN_FINISH, nil)
		
		-- 如果是创建角色，成功之后这里会return
		if game.GetCurState() ~= game.STATE_MAKE_MATCH_V then
			-- local controller = require "controller_cutscene"
			-- if controller.CheckEnterNewPlayerCutscene() == false then
			-- 	-- local ui_window_mgr = require "ui_window_mgr"	
			-- 	-- ui_window_mgr:ShowModule("ui_time_loading")
			-- 	-- game.EnterState(game.STATE_MAKE_MATCH_V)
			-- 	--EnterLobbyState()
			-- 	EnterLobby()
			-- end
			EnterLobby()

		end

		--上报user_login 创建角色成功后会走这里来
		ReportUserLogin()

		return
	end
	print('verifying', verifying, game.GetCurState())
	verifying = false
	bAutoReturnLoby = false
	
	-- 断线重连不修改状态
	if game.GetCurState() ~= game.STATE_MAKE_MATCH_V and game.GetCurState() ~= game.STATE_NEW_PLAYER_CUTSCENE then
		game.EnterState(game.STATE_LOGINED)
	end
	
		--print("收到消息ActorInfo:", actorInfo)
	local map = require "map"

	--销毁地图
	local mapid = game.GetCurMapID()
	local robbyID = 0
	if map.vertical then
		robbyID = 3
	else
		robbyID = ROBBY_SCENE_ID
	end

	if mapid ~= robbyID then
		if mapid ~= nil then
			game.UnloadMap(mapid)
		end 
		
		--local EventEnterHall = AdjustEvent("kifj6m")
		--Adjust.trackEvent(EventEnterHall)
		
		--加载地图
		
		if map.vertical then
			game.LoadMap(robbyID)
		else
			game.LoadMap(ROBBY_SCENE_ID,ROBBY_SCENE_INDEX)
		end
		
		event.Unregister(event.SCENE_LOADED, OnSceneLoaded)
		event.Register(event.SCENE_LOADED, OnSceneLoaded)
	
		event.Trigger(event.PLAYER_START, {x=0, y= 0, z= 0})	
	else
		--直接回调场景加载完毕
		OnSceneLoaded()
	end

	if game.actors and #game.actors > 0 then
		if game.GetCurState() ~= game.STATE_MAKE_MATCH_V then
			if game.GetCurState() ~= game.STATE_NEW_PLAYER_CUTSCENE then
				-- local controller = require "controller_cutscene"
				-- local ui_window_mgr = require "ui_window_mgr"
				--ui_window_mgr:CloseAll({ui_auto_login_impl = 1, new_hook_scene = 1})
				-- if controller.CheckEnterNewPlayerCutscene() == false then
				-- 	-- local ui_window_mgr = require "ui_window_mgr"	
				-- 	-- ui_window_mgr:ShowModule("ui_time_loading")
				-- 	-- game.EnterState(game.STATE_MAKE_MATCH_V)

				-- 	-- 同时关闭所有场景,准备回到大厅
				-- 	event.Trigger(event.CLOSE_ALL_SCENE)

				-- 	--EnterLobbyState()
				-- 	EnterLobby()
				-- 	--[[
				-- 	local ui_login_main_mgr = require "ui_login_main_mgr"
  				-- 	if ui_login_main_mgr.IsAutoLogin() then
				-- 		net_route.SetCachingAllMsg(true)
				-- 	end
				-- 	]]
				-- end

				local puzzlegame_mgr = require "puzzlegame_mgr"
				if not puzzlegame_mgr.IsSockPackage() then
					event.Trigger(event.CLOSE_ALL_SCENE)
				end
				EnterLobby()


			end
		end
		--发送登陆完成事件
		event.Trigger(event.ACTOR_LOGIN_FINISH, nil)

		--上报user_login 已有角色会走这里来
		ReportUserLogin()

		-- 临时登录方案
		local lobbymatch_pb = require "lobbymatch_pb"
		local common_pb = require "common_pb"
		local msg = lobbymatch_pb.TMSG_LOBBY_MATCH_BEGIN_REQ()
		msg.enMode = common_pb.enMatchModeSolo
		net.SendMessage(net.Endpoint_Client, net.Endpoint_AccountLogin, 0, msg_pb.MSG_LOBBY_MATCH_BEGIN_REQ, msg)		
	else
		-- 服务器自动创建角色，不由客户端请求
		game.EnterState(game.STATE_CREATE_ACTOR)
		local ui_create_actor_data = require "ui_create_actor_data"
		ui_create_actor_data.CreateActor()

		-- local ui_window_mgr = require "ui_window_mgr"
		-- ui_window_mgr:UnloadModule("ui_login_main")
	end
	event.Trigger(event.ACTOR_LOGIN_NTF, nil)
end

function EnterLobbyState()
	game.EnterState(game.STATE_MAKE_MATCH_V)
end

function EnterLobby()
	event.Unregister(event.UPDATE_RECHARGE_NUM, GetExclusiveConfig)
	local exc = require "ui_exclusive_customer_service_data"
	exc.GetVipOpenConfig()
	GetExclusiveConfig = function()
		local net_vip_module = require "net_vip_module"
		local RechargeNum = net_vip_module.GetNewRechargeNum() or 0
		log.DirectWarning("[customer] 专属客服 获取充值金额", RechargeNum)
		if RechargeNum > 0 then
			exc.GetExclusiveConfig()
		end
	end
	event.Register(event.UPDATE_RECHARGE_NUM, GetExclusiveConfig)
    GetExclusiveConfig()
	
	EnterLobbyState()

	event.Trigger(event.SET_FLOW_STATE_CACHE,"enter_lobby","1")
	event.Trigger(event.LOGIN_ENTER_LOBBY)
    event.Trigger(event.FIRST_LOGIN_OPTIMIZE_REQUIRE_STEP,1)
	local ui_login_progress = require "ui_login_progress"
	ui_login_progress:SetPercent(0.85)
end
-- old code
function EnterLobbyxxxx()
	local puzzlegame_mgr = require "puzzlegame_mgr"
	if puzzlegame_mgr.IsSockPackage() then
		local uiUpdateNode = GameObject.Find("driver_UIUpdateNode/UIUpdate(Clone)")
        if uiUpdateNode then
            uiUpdateNode:SetActive(false)
        end
		--local ui_login_main = require "ui_login_main"
		--ui_login_main.OnCloseUI()
		return
	end
	-- print("EnterLobby()")
	log.LoginWarning("开始加载大厅")
	event.RecodeTrigger("start_load_lobby", {})
	event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT,{mark = "start_load_lobby"})
	-- event.Trigger(event.GAME_LOGIN_PRELOAD_OPEN_LOBBY, false)--关闭主界面预加载状态，打开后直接显示
	
	game.EnterState(game.STATE_MAKE_MATCH_V)


	local ui_window_mgr = require "ui_window_mgr"
	local const = require("const")
	if const.OPEN_NEW_HOOK_SCENE then
		local laymain_mgr = require("laymain_mgr")
		laymain_mgr.Show(laymain_mgr.HUNT_SCENE)
	end

	-- event.Unregister(event.UPDATE_RECHARGE_NUM, GetExclusiveConfig)

	
	-- local exc = require "ui_exclusive_customer_service_data"
	-- exc.GetVipOpenConfig()
	
	-- GetExclusiveConfig = function()
	-- 	local net_vip_module = require "net_vip_module"
	-- 	local RechargeNum = net_vip_module.GetNewRechargeNum() or 0
	-- 	log.DirectWarning("专属客服 获取充值金额",RechargeNum)
	-- 	if RechargeNum > 0 then
	-- 		exc.GetExclusiveConfig()
	-- 	end
	-- end
	
	-- event.Register(event.UPDATE_RECHARGE_NUM, GetExclusiveConfig)
	-- GetExclusiveConfig()

	if not const.USE_MAIN_SLG then
		ui_window_mgr:ShowModule("ui_lobby")

		ui_window_mgr:ShowModuleImmediate("ui_menu_top")
		ui_window_mgr:ShowModuleImmediate("ui_menu_bot")
	else
		-- local main_slg_mgr = require "main_slg_mgr"
		-- main_slg_mgr.InitScene()
		event.Trigger(event.SET_FLOW_STATE_CACHE,"enter_lobby","1")
	end


	event.Trigger(event.LOGIN_ENTER_LOBBY)
	
	
	-- ui_window_mgr:ShowModule("ui_menu_side")
end

-- 收到 Actor 数据时上传数据
function ReportUserLogin(  )
	local setting_server_data = require "setting_server_data"
	local worldID = setting_server_data.GetLoginWorldID()
	q1sdk.UserEvent(worldID, 0, game.actors[1].name, game.actors[1].level, "", "userLogin", "sex="..game.actors[1].sex)
	setting_server_data.ReportSelectServerReport("user_login")

	q1sdk.UserEvent(worldID, 0, game.actors[1].name, game.actors[1].level, "", "selectServer", "")
	q1sdk.U8SubmitExtraData(1)
end

---保存外部key
function SetAutoLoginKey(strLoginKey)
	 if strLoginKey==nil then
		 log.ErrorFormat("SetAutoLoginKey:返回大厅的秘钥为空：strLoginKey==nil") 
		return  
	end
	
	loginKey = strLoginKey
end

function SetLoginServer(_server_info)
	local setting_server_data = require "setting_server_data"
	setting_server_data.SetLoginServer(_server_info)
end
function SetLoginContext(userName, password, partnerID, token)
	print("SetLoginContext>>>>>>>>>>",userName, password, partnerID, token)
	loginContext.stLoginData.userName = userName
	loginContext.stLoginData.password = password
	loginContext.stLoginData.session = token
	loginContext.stLoginData.partnerId = partnerID
	loginContext.stLoginData.uuid = q1sdk.GetUUID()

	local setting_server_data = require "setting_server_data"
	local worldid = setting_server_data.GetLoginWorldID()
	loginContext.stLoginData.sdkType = setting_server_data.GetRegionID(worldid)
end

--[[设置登录授权码]]
function SetLoginCode(strCode)
	loginContext.stLoginData.verifyCode = strCode
end

-- 向服务器发送登录的区服id -> 合服后，服务器需要了解玩家原来所在区服
function SetLoginWorldID( fromWorldID )
	log.DirectWarning("set send worldID:", fromWorldID)
	loginContext.stLoginData.fromWorldID = fromWorldID
end

-----------------------------------------------------------------------------------------------------------------主动登陆相关  begin
local channelData = 
{
	["com.yxzj.aw"] = 2124902,  -- 爱玩1
	["com.yxzj.awxx.shfz.xx"] = 2124903,  -- 爱玩2
	["com.yxzj.awxx.wjztapi.xx"] = 2124903,
	["com.yxzj.awxx.yxyz1.xxgame"] = 2124903,
	["com.yxzj.awxx.wjzt2.xx"] = 2124903,
	["com.yxzj.awxx.wjzt1.xx"] = 2124903,
	["com.yxzj.awxx.wjzt.xx"] = 2124903,
	["com.yxzj.tw.yxdjtt"] = 2124904, -- 贪玩混服
	["com.yxzj.tw.mtyxtt"] = 2124904,
	["com.yxzj.tw.mtyxttapi"] = 2124904,
	["com.yxzj.tw.tltt"] = 2124904,
	["com.yxzj.tw.yxdjks"] = 2124904,
	["com.yxzj.tw.mtyxks"] = 2124904,
	["com.yxzj.tw.tlks"] = 2124904,
}

-- 去掉，获取pid的接口，直接使用game_config.channelID
-- function GetFixPID()
-- 	local packageName = q1sdk.getPackageName()
-- 	if packageName and (packageName~="") and Application.version == "1.0.31" and channelData[packageName] then
-- 		return channelData[packageName]
-- 	end
	
-- 	local game_config = require "game_config"
-- 	local CHANNEL_ID = game_config.CHANNEL_ID

-- 	local const = require "const"
-- 	local pid = const.GetQ1PID() or CHANNEL_ID
-- 	if pid == 0 then
-- 		pid = game_config.CHANNEL_ID_OLD or CHANNEL_ID
-- 	end
-- 	return pid
-- end

--登陆到服务器
function LoginServer(gameWorldName)
	print("etloginmodule登录到服务器LoginServer")
	local log = require "log"
	--log.WarningFormat("客户端收到：握手通知({0})", msg_pb.MSG_LOGIN_HANDSHAKE_NTF)
	
	local net_recharge_module = require "net_recharge_module"
	-- print(">>>>>>>>>>>>>>>>>>>登录成功获取谷歌商店消息>>>>>>>>>>>>>>>>>")
	if net_recharge_module.isGooglePlay() then
		net_recharge_module.SetGoogleShopData()
	else
		net_recharge_module.ClearGoogleShopData()
	end
	
	--重置战力可计算状态
	-- local calpro_mgr = require "calpro_mgr"
	-- calpro_mgr.ResetAndClearCanCalState()
	
	verifying = true
	local game = require "game"
	game.gameWorldName = gameWorldName 

	--获取语言索引
	local selLang = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
	local nLangIndex = tonumber(selLang)
	
	loginContext.stLoginData.clientVersion = util.GetClientVersion()
	loginContext.stLoginData.macAddress = Utility.GetMacAddress()
	loginContext.stLoginData.gameWorldName = gameWorldName
	loginContext.stLoginData.loginType = login_pb.TMSG_LOGIN_LOGIN_REQ.NONE
	loginContext.stLoginData.channelID = game_config.CHANNEL_ID or 1 --game_config.channelID or 1 --jenkins上不存在channelID,只有 CHANNEL_ID，是否使用有误？
	loginContext.stLoginData.operSystem = login_pb.enSystemType_Android
	if Application.platform == RuntimePlatform.IPhonePlayer then
		loginContext.stLoginData.operSystem = login_pb.enSystemType_IOS
	end
	local setting_server_data = require "setting_server_data"
	local worldid = setting_server_data.GetLoginWorldID()
	loginContext.stLoginData.sdkType = setting_server_data.GetRegionID(worldid)
	
	loginContext.stLoginExContext.bIsReturnBattle = false				-- 登录到服务器，默认不返回战场
	local ui_setting_cfg = require "ui_setting_cfg"
	nLangIndex = ui_setting_cfg.LangMapToServer[nLangIndex]
	loginContext.stLoginExContext.nLanguage = nLangIndex				-- 设置语言
	loginContext.stLoginExContext.channelmark = util.GetChannelMark()
	loginContext.stLoginExContext.adid = ""
	log.Warning(tostring(loginContext))

	-- u8渠道
    if game_config.ENABLE_Q1SDK_CHANNEL then
        local const = require "const"
        loginContext.stLoginData.SKDChannelID = game_config.CHANNEL_ID
        loginContext.stLoginData.SDKUserID = u8UserInfo.sdkUserID or ""
        loginContext.stLoginData.SDKUserName = u8UserInfo.sdkUserNam or ""
        loginContext.stLoginData.ChannelUserID = u8UserInfo.userID or ""
        loginContext.stLoginData.ChannelUserName = u8UserInfo.username or ""
        loginContext.stLoginData.age = userRealAge
        loginContext.stLoginData.timestamp = os.time()
        loginContext.stLoginData.strTimeStamp = u8UserInfo.timestamp or ""
        loginContext.stLoginData.partnerId = login_pb.enLoginPartnerID_U8
        loginContext.stLoginData.session = u8UserInfo.session or ""
        -- 这里针对1.0.31版本的联运包，外网获取pid异常，需要特殊处理一下,校正pid的值
        local packageName = q1sdk.getPackageName()
        if Application.version == "1.0.31" and channelData[packageName] then
            event.Trigger(event.GAME_EVENT_REPORT, "u8channel_login_info_change_pid",
                { oldPid = loginContext.stLoginData.SKDChannelID, newPid = channelData[packageName] })
            loginContext.stLoginData.SKDChannelID = channelData[packageName]
        end

        log.Warning("登录时间戳：", loginContext.stLoginData.strTimeStamp, u8UserInfo.sdkUserID, u8UserInfo.userID,
            u8UserInfo.sdkUserNam, u8UserInfo.username, userRealAge, u8UserInfo.session)
        log.Warning("login SKDChannelID：", loginContext.stLoginData.SKDChannelID)
        -- 上报SKDChannelID是否是正确的id

        local properties = {
            packageName = q1sdk.getPackageName(),
            version = Application.version,
            sdkchannelID = loginContext.stLoginData.SKDChannelID
        }
        event.Trigger(event.GAME_EVENT_REPORT, "U8CHANNEL_LOGIN_INFO", properties)
    end
	
	ModifyLoginInfo(loginContext)

	net.SendMessage(net.Endpoint_Client, net.Endpoint_Login, 0, msg_pb.MSG_LOGIN_LOGIN_REQ, loginContext)
	--log.WarningFormat("客户端发送：登录请求({0}), 期望服务器回复：角色数据({1})",msg_pb.MSG_LOGIN_LOGIN_REQ, msg_pb.MSG_LOGIN_ACTOR_INFO_NTF)
	bAutoLogin = false
end

function ModifyLoginInfo(loginContext)
	local val = require "val"
	local add = tonumber(val.get("sw_role_add", 0)) + tonumber(val.get("sw_role_add_10", 0)) * 10
	if add > 0 then
		loginContext.stLoginData.testUserName = loginContext.stLoginData.userName .. "_" .. add
        log.DirectWarning("loginContext.stLoginData.testUserName", add, loginContext.stLoginData.userName,
        loginContext.stLoginData.testUserName)
		local testUserName = loginContext.stLoginData.testUserName
        local c = PlayerPrefs.GetString("login_testUserName", "")
		if string.find(c, "|"..testUserName.."|") == nil then
			c = c .. "|" .. testUserName
			PlayerPrefs.SetString("login_testUserName", c)
		end
	end

end

--发送消息自动登陆到大厅(通常是从战场返回到大厅服务器)
function  AutoLoginServer(bErrorReturn)
	--还没有收到
	if nil == returnLobbyContext.stALSess  then
		 log.ErrorFormat("AutoLoginServer nil==returnLobbyContext.stALSess")
		ReturnLogin() 
		return 
	end
	
	--获取语言索引
	local selLang = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
	local nLangIndex = tonumber(selLang)
	
	local autoLoginContext = login_pb.TMSG_LOGIN_AUTO_LOGIN_REQ_V2()
	
	autoLoginContext.stALSess.iUserDBID = returnLobbyContext.stALSess.iUserDBID
	autoLoginContext.stALSess.iRoleDBID = returnLobbyContext.stALSess.iRoleDBID
	autoLoginContext.stALSess.iTime = returnLobbyContext.stALSess.iTime
	autoLoginContext.stALSess.iLobbySvrID = returnLobbyContext.stALSess.iLobbySvrID
	autoLoginContext.stALSess.strToken = returnLobbyContext.stALSess.strToken
	autoLoginContext.stALSess.iBCUserID = returnLobbyContext.stALSess.iBCUserID
	autoLoginContext.stLoginExContext.bIsReturnBattle = bAutoReturnLoby	-- 默认退出战斗
	local ui_setting_cfg = require "ui_setting_cfg"
	nLangIndex = ui_setting_cfg.LangMapToServer[nLangIndex]
	autoLoginContext.stLoginExContext.nLanguage = nLangIndex			-- 设置语言
	
	--登陆结构
	autoLoginContext.stLoginData.clientVersion = loginContext.stLoginData.clientVersion
	autoLoginContext.stLoginData.userName = loginContext.stLoginData.userName
	-- 这里的userName是服务器用来做唯一标识
	if game_config.ENABLE_Q1SDK_CHANNEL then
		autoLoginContext.stLoginData.userName = u8UserInfo.userID or loginContext.stLoginData.userName
	end
	autoLoginContext.stLoginData.password = loginContext.stLoginData.password
	autoLoginContext.stLoginData.macAddress = loginContext.stLoginData.macAddress
	autoLoginContext.stLoginData.verifyCode = loginContext.stLoginData.verifyCode
	autoLoginContext.stLoginData.gameWorldName = loginContext.stLoginData.gameWorldName
	autoLoginContext.stLoginData.loginType = loginContext.stLoginData.loginType
	autoLoginContext.stLoginData.partnerId = loginContext.stLoginData.partnerId
	autoLoginContext.stLoginData.serialNo = loginContext.stLoginData.serialNo
	autoLoginContext.stLoginData.session = loginContext.stLoginData.session
	autoLoginContext.stLoginData.uuid = loginContext.stLoginData.uuid
	autoLoginContext.stLoginData.channelID = game_config.CHANNEL_ID or 1 --game_config.channelID or 1  --jenkins上不存在channelID,只有 CHANNEL_ID，是否使用有误？
	autoLoginContext.stLoginData.operSystem = login_pb.enSystemType_Android
	if Application.platform == RuntimePlatform.IPhonePlayer then
		autoLoginContext.stLoginData.operSystem = login_pb.enSystemType_IOS
	end
	autoLoginContext.stLoginData.fromWorldID = loginContext.stLoginData.fromWorldID

	log.DirectWarning("set autoLogin send worldID:", loginContext.stLoginData.fromWorldID)

	autoLoginContext.stLoginExContext.adid = "" --strAdid


	-- u8渠道
	if game_config.ENABLE_Q1SDK_CHANNEL then
		local const = require "const"
		loginContext.stLoginData.SKDChannelID = game_config.CHANNEL_ID
		loginContext.stLoginData.SDKUserID = u8UserInfo.sdkUserID or ""
		loginContext.stLoginData.SDKUserName = u8UserInfo.sdkUserNam or ""
		loginContext.stLoginData.ChannelUserID = u8UserInfo.userID or ""
		loginContext.stLoginData.ChannelUserName = u8UserInfo.username or ""
		loginContext.stLoginData.age = userRealAge
		loginContext.stLoginData.timestamp = os.time()
		loginContext.stLoginData.strTimeStamp = u8UserInfo.timestamp or ""
		autoLoginContext.stLoginData.partnerId = login_pb.enLoginPartnerID_U8
		autoLoginContext.stLoginData.session = u8UserInfo.session or ""
	end
	
	net.SendMessage(net.Endpoint_Client, net.Endpoint_Login, 0, msg_pb.MSG_LOGIN_AUTO_LOGIN_REQ_V2, autoLoginContext)
	
	--log.WarningFormat("发送自动登陆请求",msg_pb.MSG_LOGIN_LOGIN_REQ, msg_pb.MSG_LOGIN_ACTOR_INFO_NTF)
				
	bAutoLogin = false
	verifying = true
end

function ResetLoginSession(session)
	loginContext.stLoginData.session = session
	if autoLoginContext ~= nil then
		autoLoginContext.stLoginData.session = loginContext.stLoginData.session
	end
end

function GetLoginSession()
	return loginContext.stLoginData.session
end

-----------------------------------------------------------------------------------------------------------------主动登陆相关  end
-- 保存当前登录的ip 和 port ,用于断线重连重传功能
function SaveConnectedContext(ip, port, connect_type)
	if connect_type == net_gateway_module.CONNECT_TYPE_TCP then
		returnLobbyContext.lobbyIP = {ip}
		returnLobbyContext.lobbyPort = {port}
	end
	if connect_type == net_gateway_module.CONNECT_TYPE_UDP then
		returnLobbyContext.lobbyUDPIP = {ip}
		returnLobbyContext.lobbyUDPPort = {port}
	end
end

--返回登陆状态
function ReturnLogin(bLogout)
	local main_slg_mgr = require "main_slg_mgr"
	main_slg_mgr.NetCloseReturnLogin()
	print("net ReturnLogin",bLogout)
	event.Trigger(event.SET_FLOW_STATE_CACHE,"return_to_login","1")

	local net_reconnect_module = require "net_reconnect_module"
	net_reconnect_module.DisconnectActivity()
	
	net.Disconnect()

	local ui_window_mgr = require "ui_window_mgr"
	local const = require "const"
	ui_window_mgr:CloseAllUIAndScene(const.ELoginEnterCullLua, true)

	--if bLogout ~= false then
	--	q1sdk.Logout()
	--end
	q1sdk.LogoutLogic()
	
	if game.GetCurState() ~= game.STATE_LOGIN_TYPE then
		game.EnterState(game.STATE_LOGIN_TYPE)
	else
		ui_window_mgr:ShowModule("ui_login_main")
	end

    if bLogout ~= false then
        event.Trigger(event.SCENE_DESTROY)
    end

	local mapid = game.GetCurMapID()
	if mapid ~= nil then
		--通知销毁场景
		local map = require "map"
		map.Unload(mapid)
	end
	game.NotifySceneDestroy()
	
	bAutoLogin = false

	-- 添加自动登录逻辑：如果当前是自动登录模式，延迟0.5秒后执行自动登录重连
	local ui_login_main_mgr = require "ui_login_main_mgr"
	if ui_login_main_mgr.IsAutoLogin() then
		-- 延迟0.5秒后执行自动登录
		local util = require "util"
		util.DelayCall(0.5, function()
			log.Warning("ReturnLogin 触发自动登录重连")
			AutoLogin.Show()
		end)
	end

	if bLogout ~= false then
		event.Trigger(event.ACCOUNT_LOGOUT, false)
		print("Trigger event.ACCOUNT_LOGOUT")
	end
	return true
end

-- 返回登录界面自动登录
function AutoReturnLogin()
	log.Warning("AutoReturnLogin")
	local ui_window_mgr = require "ui_window_mgr"
	local const = require "const"
	ui_window_mgr:CloseAllUIAndScene(const.ELoginEnterCullLua, true)
	if game.GetCurState() ~= game.STATE_LOGIN_TYPE then
		game.EnterState(game.STATE_LOGIN_TYPE)
	else
		ui_window_mgr:ShowModule("ui_login_main")
	end
	local mapid = game.GetCurMapID()
	if mapid ~= nil then
		--通知销毁场景
		local map = require "map"
		map.Unload(mapid)
	end
	game.NotifySceneDestroy()
	
	AutoLogin.Show()
	--ReturnLogin()
end

--请求连接大厅(客户端主动调用请求返回大厅)
function ConnectLobby(bErrorReturn)
	--print("ConnectLobby>>>>>>>>")
	log.LogWarning("net_login_module ConnectLobby", "Net Debug")
	if delayCallKey then
		util.RemoveDelayCall(delayCallKey)
		delayCallKey = nil
	end
	print("ConnectLobby>>>>>>>>>>",bErrorReturn)

	local util = require "util"
	delayCallKey = util.DelayCall(1, function()
		--有密钥自动重连
		if returnLobbyContext.stALSess ~=nil then
			bAutoLogin = true
		end

        local connect_test = require "connect_test"
		connect_test.ChangeNextConnect()
		
		bAutoReturnLoby = bErrorReturn or false		
		
		--第一次登陆的,初始化
		-- if nil==returnLobbyContext.lobbyIP then
		-- 	returnLobbyContext.lobbyIP = net_gateway_module.GetTCPIPList()  --tcp ip 
		-- 	returnLobbyContext.lobbyPort= net_gateway_module.GetTCPPortList()  -- tcp port
		-- 	returnLobbyContext.lobbyUDPIP =  net_gateway_module.GetUDPIPList()  -- udp ip
		-- 	returnLobbyContext.lobbyUDPPort =  net_gateway_module.GetUDPPortList()  --udo port
		-- end
		local setting_server_data = require "setting_server_data"
		local worldID=setting_server_data.GetLoginWorldID()
		local tcpips ,tcpPorts,udpips,udpPorts = net_gateway_module.GetConnectConfig(worldID)
		returnLobbyContext.lobbyIP = tcpips
		returnLobbyContext.lobbyPort= tcpPorts
		returnLobbyContext.lobbyUDPIP =  udpips
		returnLobbyContext.lobbyUDPPort =  udpPorts

		
		local tcp_ip = returnLobbyContext.lobbyIP
		local tcp_port= returnLobbyContext.lobbyPort
		local udp_ip= returnLobbyContext.lobbyUDPIP
		local udp_port= returnLobbyContext.lobbyUDPPort
		
		local util = require "util"
		local netType = util.GetNetworkType()
		if netType ~= nil and netType == util.ENETWORK_TYPE.MOBILE then
			udp_ip = nil
			udp_port = nil
		end
		
		-- 断线重连
		net_gateway_module.ConnectGateway(tcp_ip,tcp_port,udp_ip,udp_port,function(errCode, worldID, errReason)
			if errCode == 0 then
				-- 保存本次登录状态

				-- 切换服务器时发起了重连，重新登录打断重连，重连模块经过延时后再次重连并打断了登录的情况下，导致登录模块未能完成上传服务器的 fromWorldID 的设置
				-- local worldID=setting_server_data.GetLoginWorldID() 在请求切换服务器时，已修改。此处可以使用
				-- 由于换区时登录流程可能被打断，登录并未将换区设置保存到本地，所以不能使用本地缓存区服 id ...
				-- 而合区后，服务器在登录前不能确定玩家在哪个区，所以此处返回的 worldID 其实也不可靠 ...
				local worldID = setting_server_data.GetLoginWorldID()
				if worldID and worldID > 0 then
					SetLoginWorldID(worldID)

					local ui_login_main = require "ui_login_main"
					ui_login_main.SaveWorldId(worldID)
				end
			else
				event.Trigger("SERVERERROR_LOG_INFO", tostring(errCode), errReason)
				net_gateway_module.OnConnectGatewayError(errCode, errReason)
			end
			
		end,nil,net_gateway_module.CONNECT_STATE_RETUREN_HALL)

		if delayCallKey then
			util.RemoveDelayCall(delayCallKey)
			delayCallKey = nil
		end
	end)
end


--更新登陆秘钥
function OnUpdateALSession(msg)
	print("OnUpdateALSession>>>>>>>>>>")
	returnLobbyContext.stALSess = msg.stALSess
		--[[
	message TAutoLoginSession
{
	required int32 iUserDBID = 1;		//  帐号ID
	required int32 iRoleDBID = 2;	   //  角色ID  
	required int32 iTime = 3;		   //  token产生时间
	required int32 iLobbySvrID = 4;	 //  大厅服ID
	required string strToken = 5;	   // 登录密钥
}
]]
	if LoginedBCUserID ~= msg.stALSess.iBCUserID and game.actors and #game.actors > 0 then
		LoginedBCUserID = msg.stALSess.iBCUserID
	end

end

--登陆出错 
function OnLoginError(msg) 
	local ui_login_main = require "ui_login_main"
	ui_login_main:OnLoginError(msg)

	activity_indicator.Hide("Login")
	AutoLogin.Close()
	local stALSess = GetAlSession()
	local userId = stALSess and stALSess.iUserDBID or 0
	--session校验失败，sdk打点上报
	if msg.iErrorType == login_pb.enLET_LobbySvr_Verify then
		q1sdk.trackUserLoginSessionError(userId, msg.iError)
	end

	--登陆出错,删除快速重连秘钥
	returnLobbyContext.stALSess = nil
	
	bAutoLogin = false
	local lang_res_key = require "lang"
	local strError = ""
	if msg.iErrorType == 23 and msg.iError == 6 then
		--未实名错误特殊处理
		strError = lang.Get(808)
	else
		strError = lang.Get(lang_res_key.KEY_LOGIN_FAIL) .. ":"..lang.Get(lang_res_key.KEY_ERROR_TYPE)..":"..msg.iErrorType..","..
			lang.Get(lang_res_key.KEY_ERROR_CODE)..":".. msg.iError
	end

	local strTips = strError
	local systemTitle = lang_res_key.KEY_SYSTEM_TIPS

	event.Trigger("SERVERERROR_LOG_INFO", systemTitle, strTips)

	log.LoginWarning("登录出错", systemTitle, strTips)
	event.RecodeTrigger("game_login_error", {error_code = tonumber(msg.iError), error_type = tostring(msg.iErrorType)})

	local message_box = require "message_box"
	message_box.Open(strTips, message_box.STYLE_YES, ReturnLogin, 0, lang_res_key.KEY_OK,lang_res_key.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type)
	
	-- 登录出错上报
	local eventName = "loginError" 
	if msg.iErrorType == login_pb.enLET_LoginSvr_Login then
		eventName = "userLoginError"
	end
	--doc https://q1doc.yuque.com/kiob3t/gynxh4/xt3dx5x3chbafi3e
	local msgs = "error="..msg.iError .. ";errorStr=" .. msg.errorInfo
	q1sdk.UserEvent(0, 0, "null", 0, "null" , eventName, msgs)
end

-- 服务器踢人
function OnServerKickOut(msg)
	local keys = {
		[login_pb.enKickReason_RepeatLogin] = lang.KEY_KICKOUT_TIPS_BASE + login_pb.enKickReason_RepeatLogin,
		[login_pb.enKickReason_GMKick] = lang.KEY_LOGIN_GM_KICK,
	}
	local key = keys[msg.kickReason] or 39
	local tips = lang.Get(key)

	local systemTitle = lang.KEY_SYSTEM_TIPS
	local message_box = require "message_box"
	local function OnConfirm()
		ReturnLogin()
	end 

	message_box.Open(tips, message_box.STYLE_YES,
		OnConfirm, 0, lang.KEY_OK,lang.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type)
end

-- 服务器封号 
function OnServerBanAccount(msg)
	log.DirectWarning("OnServerBanAccount")
	local systemTitle = lang.KEY_SYSTEM_TIPS
	local message_box = require "message_box"
	local url_mgr = require "url_mgr"
	local time = msg.EndTime
	print("OnServerBanAccount>>>>>>>>>>",time,msg:HasField("PlayerName"),msg:HasField("Content_zh"),msg:HasField("Content_eh"))
	
	if time < 0 then 
		local displayUrl = "\t"..url_mgr.FACEBOOK_ROOT_URL.."\t"
		--local tips = string.format(lang.Get(3608), displayUrl)
		local tips = lang.Get(3608)
		message_box.Open(tips, message_box.STYLE_YES,
			function()
				ReturnLogin()
				local url = url_mgr.FACEBOOK_ROOT_URL --"https://www.facebook.com/Xhero2019/"
				q1sdk.ApplicationOpenURL(url)
			end, 
			0, lang.KEY_OK,lang.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type)
			return
	end
	
	local strTime = util.FormatTime(time)
	if msg:HasField("PlayerName") and msg.PlayerName~="" and
		msg:HasField("Content_zh") and msg.Content_zh~="" and
		msg:HasField("Content_eh") and msg.Content_eh~="" 
	then
		local selLang = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
		local nLangIndex = tonumber(selLang)
		local tips = (nLangIndex==1 or nLangIndex==2) and msg.Content_zh or msg.Content_eh
		--tips = WWW.UnEscapeURL(tips)
		tips = string.gsub(tips,"%[name%]",msg.PlayerName)
		tips = string.gsub(tips,"%[time%]",strTime)

		message_box.Open(tips, message_box.STYLE_YESNO,
			function (callbackData, nRet)
				if nRet == message_box.RESULT_YES then
					q1sdk.ApplicationOpenURL(GetUrl())
				else
					ReturnLogin()
				end		   
			end,
			0, lang.KEY_OK,lang.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type, nil, nil, true, 
			function()
				q1sdk.ApplicationOpenURL(GetUrl())
			end)
	else
		-- 由于你违反游戏规则，账号已被封禁至%s
		local tips = string.format(lang.Get(3603),strTime)
		message_box.Open(tips, message_box.STYLE_YES,
			function()
				ReturnLogin()
			end, 
			0, lang.KEY_OK,lang.KEY_CANCEL,systemTitle,message_box.en_msgbox_net_type)
	end
	
end

--[[获取当前跳转的链接]]
function GetUrl()
	local const = require "const"
	local url_mgr = require "url_mgr"
	local url = url_mgr.FACEBOOK_ROOT_URL
	if const.IsJapanChannel() then
		--日语包使用twitter,domain配置的链接
		url = url_mgr.TWITTWER_URL
	elseif const.IsKoreaChannel() then
		--韩语包使用韩语社区
		url = url_mgr.NAVERGAME_URL
	end
	return url
end

--[[获取服务器时间]]
function GetServerTime()
	return serverTime + Time.realtimeSinceStartup - serverTimeUpdateTick
end

--[[获取服务器时区]]
function GetServerTimeZone()
	return serverZone
end

function GetTime(time)
	return serverTime + time - serverTimeUpdateTick
end

function LobbyErrNTF(msg)
	ShowErrTip(msg.enErr)
end

function GetServerNextZeroTime()
	return serverNextZeroTime.time
end
--[[同步服务器时间]]
-- 这条消息服务器废弃掉了
function SynServerTime(msg)
	print("收到服务端修改时间通知：", msg.timeStamp, Time.time)
	serverTimeUpdateTick = Time.realtimeSinceStartup
	serverTime = msg.timeStamp
end

function NetTimeSync(msg)
	local oldSeverTime = serverTime
	CheckIsCrossHour(oldSeverTime, msg.serverTime)
    serverTimeUpdateTick = Time.realtimeSinceStartup
    serverTime = msg.serverTime
    if msg.serverZone then
        serverZone = msg.serverZone
    end
    if msg.serverNextZeroTime then
        serverNextZeroTime.time = msg.serverNextZeroTime
        if serverNextZeroTime.time ~= msg.serverNextZeroTime then
            print("hjh 服务器同步时间", "服务器时间=", GetServerTime(), "零点时间戳=", GetServerNextZeroTime(), "距离零点时间=", GetServerNextZeroTime() - GetServerTime())
        end
    end
    CheckIsCrossDay()
    event.Trigger(event.SYNC_SERVER_TIME)
end
net_script.RegisterResponseLuaFuncNew("NetTimeSync", NetTimeSync)

--服务器跨天时，客户端触发跨天事件 event.SERVER_NEXT_ZERO_TIME
--每天零点触发
function CheckIsCrossDay()
	if crossDayTicker and serverNextZeroTime.hasTrigger==false then
		util.RemoveDelayCall(crossDayTicker)
		crossDayTicker =nil 
	end
	local interval = 15
	local delay = GetServerNextZeroTime() - GetServerTime()+1
	if delay>0 and delay <= interval and serverNextZeroTime.hasTrigger == false then
		serverNextZeroTime.hasTrigger = true
		crossDayTicker = util.DelayCallOnce(delay > 0 and delay or 0,function()
			print("hjh 客户端触发跨天事件","服务器时间=",GetServerTime(),"零点时间戳=",GetServerNextZeroTime())
			event.Trigger(event.SERVER_CROSS_DAY)
			serverNextZeroTime.hasTrigger = false
		end)
	end
end

--检查是否到整点
function CheckIsCrossHour(oldSeverTime, newSeverTime)
	local oldSeverTimeData = os.date("*t", oldSeverTime)
	local newSeverTimeData = os.date("*t", newSeverTime)
	if oldSeverTimeData.hour ~= newSeverTimeData.hour then
		local data = {
		}
		event.Trigger(event.GAME_EVENT_REPORT, "online_Role", data)
	end
end

function ShowErrTip(iErrorCode)

	local szContent = lang.Get(Lang.KEY_ERROR_CODE_SERVER_BASE+iErrorCode) 
	if nil ~= szContent and ""~=szContent then
		local flow_text = require "flow_text"
		flow_text.Clear()
		flow_text.Add(szContent)
	end
end


function SetVerifying(bool)
	print("SetVerifying>>>>>>>>>>>>>>>>>>",bool)
	verifying=bool
end

function S2CTimezoneSyncTime(msg)
	print("收到服务端修改时间通知：", msg.timestamp, Time.time, Time.realtimeSinceStartup)
	serverTimeUpdateTick = Time.realtimeSinceStartup
	serverTime = msg.timestamp
	event.Trigger(event.SYNC_SERVER_TIME)
end

function GetRecActorInfoTime( )
	return recActorInfoTime
end

function SetSwitchServer(value)
	isSwitchServer = value
end

function GetSwitchServer()
	return isSwitchServer
end

-- 设置大区id
function SetLoginAreaID(value)
    if value then
        log.Warning("SetLoginAreaID:", value)
        areaID = value
		local property = {
			login_areaID = value,
		}
		PlayerPrefs.SetInt("areaid", value)
		event.Trigger(event.GAME_EVENT_REPORT, "Set_Login_AreaID", property)
    end
end

-- 获取大区ID，登录之后可用
function GetLoginAreaID()
	local setting_server_data = require "setting_server_data"
	if areaID == 0 then
		return setting_server_data.GetLoginWorldAreaID()
	end
    return areaID or 1
end

--------------------u8 玩家数据--------------------
function SetUserAge(age)
	userRealAge = age or 0
end

function GetUserAge()
	return userRealAge
end

function SetU8UserInfo(data)
	if data then
		u8UserInfo = data
		event.Trigger(event.GAME_EVENT_REPORT, "u8_set_userinfo", data)
	end
end

function GetU8UserInfo()
	return u8UserInfo
end

-- 如果是渠道包 userID要替换成username上报
function GetU8UserName()
	if u8UserInfo and u8UserInfo.username then
		return u8UserInfo.username
	end
	return ""
end

function GetU8SDKUserID()
	if u8UserInfo and u8UserInfo.sdkUserID then
		return u8UserInfo.sdkUserID
	end
	return ""
end
--------------------u8 玩家数据-结束-------------------
-------------------------------------------------------------------
-- /// 注册消息
-------------------------------------------------------------------
local MessageTable = 
{
	{msg_pb.MSG_LOGIN_LOGIN_RSP,		S2CLoginRsp,	   login_pb.TMSG_LOGIN_LOGIN_RSP, },
	
	{msg_pb.MSG_LOGIN_ACTOR_INFO_NTF,   S2CActorInfo,	   login_pb.TMSG_LOGIN_ACTOR_INFO_NTF, },
	
	--接收登陆秘钥
	{msg_pb.MSG_LOGIN_UPDATE_ALSESSION_NTF,		OnUpdateALSession,		login_pb.TMSG_LOGIN_UPDATE_ALSESSION_NTF, },
	{msg_pb.MSG_LOGIN_LOGIN_ERROR_NTF ,		OnLoginError,		login_pb.TMSG_LOGIN_LOGIN_ERROR_NTF, },
	
	-- 服务器踢人
	{msg_pb.MSG_LOGIN_KICKOUT_NTF ,		OnServerKickOut,		login_pb.TMSG_LOGIN_KICKOUT_NTF, },
	
	-- 封号
	{msg_pb.MSG_LOGIN_BAN_ACCOUNT_NTF ,		OnServerBanAccount,		login_pb.TMSG_LOGIN_BAN_ACCOUNT_NTF, },
	
	-- 服务器大厅错误通知
	{msg_pb.MSG_LOBBY_ERROR_L2C_NTF,  	LobbyErrNTF,   lobby_pb.TMSG_LOBBY_ERROR_L2C_NTF,},

	--服务器下发的同步时间
	{msg_pb.MSG_SERVER_TIME_MODIFY_NTF,  	SynServerTime,   goldfinger_pb.TMSG_SERVER_TIME_MODIFY_NTF,},
	{msg_pb.MSG_ZONE_SERVER_TIMESTAMP_NTF ,        S2CTimezoneSyncTime,       prop_pb.TMSG_ZONE_SERVER_TIMESTAMP_NTF},
}

net_route.RegisterMsgHandlers(MessageTable)