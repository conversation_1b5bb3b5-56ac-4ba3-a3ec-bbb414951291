local require = require
local string = string
local print = print
local type=type
local math = math
local os = os
local table = table
local ipairs = ipairs
local tostring = tostring

local event = require "event"
local game = require "game"
local msg_syn = require "msg_syn"
local util = require "util"
local server_info = require "server_info"

local Utility = CS.War.Script.Utility
local time = CS.UnityEngine.Time
local BaseObject = CS.War.Script.BaseObject
local PlayerPrefs = CS.UnityEngine.PlayerPrefs

module("net_gateway_module")

local net = require "net"
local gateway_pb = require "gateway_pb"
local msg_pb = require "msg_pb"
local net_route = require "net_route"
local login_pb = require "login_pb"
local Lang = require "lang_res_key"
local files_version_mgr = require "files_version_mgr"
local ReviewingUtil = require "ReviewingUtil"

local log =require "login_log"
local isWebSocket = util.IsWebSocket()

math.randomseed(os.time())

local login_module = require "net_login_module"

--连接状态
local bConnectSuccess = false
local bChangeConnectType = false

local server_data_modlue = require "server_data"
--登陆链接的枚举
CONNECT_TYPE_UDP = server_data_modlue.CONNECT_TYPE_UDP
CONNECT_TYPE_TCP = server_data_modlue.CONNECT_TYPE_TCP
CONNECT_TYPE_MAX = server_data_modlue.CONNECT_TYPE_MAX
CONNECT_TYPE_WEB = server_data_modlue.CONNECT_TYPE_WEB

local cur_connect_type = isWebSocket and CONNECT_TYPE_WEB or CONNECT_TYPE_TCP

CONNECT_STATE_LOGIN = 1 --登陆的连接
CONNECT_STATE_BATTLE = 2 --战斗服的连接
CONNECT_STATE_RETUREN_HALL = 3 --大厅的连接

connect_state = CONNECT_STATE_LOGIN

local connect_tcp_ip_list = {}
local connect_tcp_port_list = {}
local connect_udp_ip_list = {}
local connect_udp_port_list = {}
local connect_callback = nil
local handshake_callback = nil

local cur_connect_index = 0
local cur_reconnect_time = 0 --当前重新连接次数
local last_error_code = 0
local last_error_reason = 0

--连接网关原因
ENUM_CON_REASON={
    login=1,  --登录
    changeWorld=2,    --换区
}

local sConReason=ENUM_CON_REASON.login

local curUcode = 0   -- 连接网关当前的ucode
local webPort = -1   -- webUrl连接端口号 不用加端口，根据路由匹配
local webLoginWorldid = nil -- 如果本地取到worldid，weburl直接发送worldid，如果无，使用new，进新区
local clearLoginTag = false

function SetWebLoginWorldid(worldid)
    if worldid then
        log.DirectWarning("SetWebLoginWorldid:", worldid)
        webLoginWorldid = worldid
    end
end

function ClearWebLoginWorldid()
    clearLoginTag = true
end

function IsEnableInsideServer()
    local insideServer = PlayerPrefs.GetInt("Port:81", 0)
    local log = require "log"
    log.Warning("IsPort:81 H5 PlayerPrefs:", insideServer)
    return insideServer == 1
end

-- 随机连接一个网关
function ConnectServer(callback, worldID, conReason)
    connect_state = CONNECT_STATE_LOGIN
    local tcpips ,tcpPorts,udpips,udpPorts = GetConnectConfig(worldID)
    log.LogWarning("ConnectServer connect state:"..connect_state, "Net Debug")
    ConnectGateway(tcpips, tcpPorts, udpips, udpPorts, callback, nil, CONNECT_STATE_LOGIN,conReason)
end

local function safe_merge(list, target, expected_type)
    if not list or type(list) ~= "table" then return end
    for i = 1, #list do
        local item = list[i]
        if type(item) == expected_type then
            target[#target + 1] = item
        end
    end
end

---@funtion GetConnectConfig 获取服务器数据列表 （ip和端口）
---@param worldID number 当前的服务器id
---@return tcpips,tcpPorts,udpips,udpPorts 服务器列表数据
function GetConnectConfig(worldID)
    local net_prop_module = require "net_prop_module"
    worldID = worldID or net_prop_module.WorldID
    local tcpips = {}
    local tcpPorts = {}
    local udpips = {}
    local udpPorts = {}
    local setting_server_data = require "setting_server_data"
    local tcpServerList = worldID and setting_server_data.GetServerList(worldID)
    if tcpServerList then
        for _, info in ipairs(tcpServerList) do
            safe_merge(info.ipList, tcpips, "string")
            safe_merge(info.portList, tcpPorts, "number")
        end
    end
    local udpServerList = worldID and setting_server_data.GetUdpServerList(worldID)
    if udpServerList then
        for _, info in ipairs(udpServerList) do
            safe_merge(info.ipList, udpips, "string")
            safe_merge(info.portList, udpPorts, "number")
        end
    end 
    if worldID == nil or (tcpServerList == nil and udpServerList == nil) then
        local gates_tcp = server_info.gates_tcp
        for idx, si in ipairs(gates_tcp) do
            tcpips[idx] = si.ip
            tcpPorts[idx] = si.port
        end

        local gates_udp = server_info.gates_udp or {}
        for idx, si in ipairs(gates_udp) do
            udpips[idx] = si.ip
            udpPorts[idx] = si.port
        end
    end
    local net_login_module = require "net_login_module"
    net_login_module.SetLoginWorldID(worldID)
    local ui_login_main = require "ui_login_main"
    ui_login_main.SaveWorldId(worldID)
    return tcpips,tcpPorts,udpips,udpPorts
end

-- 高效生成元素哈希（支持简单表和基础类型）
local function fastHash(val)
    if type(val) == "table" then
        local parts = {}
        for k, v in pairs(val) do
            parts[#parts+1] = tostring(k) .. ":" .. fastHash(v)
        end
        table.sort(parts) -- 保证一致性
        return "{" .. table.concat(parts, ",") .. "}"
    elseif type(val) == "string" then
        return val
    else
        return tostring(val)
    end
end
---@funtion UpdateServerList 更新服务器数据列表 
---@param target_list table 当前的服务器列表
---@param add_list table 需要添加的服务器列表
---@return target_list table 服务器列表数据
function UpdateServerList(target_list, add_list)
    if type(target_list) ~= "table" or type(add_list) ~= "table" then
        return target_list
    end

    local exist_map = {}
    -- 标记已有元素hash
    for _, v in ipairs(target_list) do
        local key = (type(v) == "table") and fastHash(v) or v
        exist_map[key] = true
    end

    -- 只插入未出现过的元素
    for _, v in ipairs(add_list) do
        local key = (type(v) == "table") and fastHash(v) or v
        if not exist_map[key] then
            table.insert(target_list, v)
            exist_map[key] = true
        end
    end
    return target_list
end

--发送连接失败事件
function FireConnectErrorEvent()
    --回调外部
    if connect_callback then
        connect_callback(last_error_code, nil ,last_error_reason)
    end
	
	log.DirectWarning("FireConnectErrorEvent>>>>>>>>GetCurState"..game.GetCurState())
	-- 如果是在登录界面，什么都不做
	if game.GetCurState() == game.STATE_LOGIN_TYPE then
        --log.DirectWarning("登录界面不做重连", "Net Debug")
        local ui_login_main_mgr = require "ui_login_main_mgr"
        if ui_login_main_mgr.IsAutoLogin() then
            NotifyNetErrorConnectFailed()
        end
		return
	end
	
	-- 如果是在登录状态，直接弹框处理，不需重连
	if game.GetCurState() == game.STATE_CONNECT then
		NotifyNetErrorConnectFailed()
        --log.DirectWarning("登录状态直接弹窗不做重连", "Net Debug")
		return
	end
    --log.DirectWarning("发送连接失败事件", "Net Debug")
    event.Trigger(event.CONNECT_FAILED_LOBBY, last_error_code)

        event.Trigger("SERVERERROR_LOG_INFO", tostring(last_error_code), last_error_reason)
	return
end

function NotifyNetErrorConnectFailed()
    local ui_window_mgr = require "ui_window_mgr"
    if ui_window_mgr:IsModuleShown("ui_inotice") then
        -- 显示服务器维护中弹窗时不弹窗 "网络连接异常,请重新登陆"
        log.DirectWarning("已显示服务器维护中，不弹出 ‘网络连接异常,请重新登陆’", "Net Debug")
        return
    end

    local net_reconnect_module = require "net_reconnect_module"
    -- 网络连接失败弹窗上报
    net_reconnect_module.OnNetErrorConnectFailed()

    local MsgBox = require "message_box"
    MsgBox.Open(Lang.KEY_CONNENT_LOBBY_FAIL, MsgBox.STYLE_YES, function()
            login_module.ReturnLogin()
        end, 0, Lang.KEY_OK,Lang.KEY_CANCEL,nil,MsgBox.en_msgbox_net_type)
end

--连接服务器
function StartConnect(ip, port, connect_type)
    log.LogWarning("net_gateway_module StartConnect,ip:"..tostring(ip)..",port:"..tostring(port)..",connect_type:"..tostring(connect_type), "Net Debug")
    if nil == ip then
        ip = ""
    end

    if port == nil then
        port = 0
    end

    PlayerPrefs.SetString("IpPort",ip..":"..port)

    local url_mgr = require "url_mgr"
    local insideServerPort = 81
    local enableInsideServer = IsEnableInsideServer()
    if files_version_mgr.IsUseWebUrlLogin() then
               
        local webUrl = url_mgr.SelectUrlByMode(url_mgr.WEB_URL)
        if ReviewingUtil.IsReviewing() then
            -- Todo H5审核服域名
            webUrl = "wss://wss-sh.you03.com"
        end
        if webLoginWorldid and not clearLoginTag and not ReviewingUtil.IsReviewing() then
            ip = webUrl.."/"..webLoginWorldid
            clearLoginTag = false
        else
            ip = webUrl.."/new"
        end

        local setting_server_data = require "setting_server_data"
        local regionID = setting_server_data.GetRegionID()

        local game_config = require("game_config")
        print("IsUseWebUrlLogin local regionID",game_config.REGION_ID,"/GetRegionID:",regionID)
        -- 预演服重定向到另一个地址,这个替换url操作应该只有在预演服中才进行
        local web_ws_url = files_version_mgr.GetWebWSUrl()
		-- 预演情况下 读取本地配置 regionId = 3 默认url 或者
        if string.empty(web_ws_url) and not ReviewingUtil.IsReviewing() 
            and (game_config.REGION_ID == 3 or regionID == 4) then
            -- Todo H5预演域名
            web_ws_url = "wss-xgame.q1.com"
        end
        
        if not string.empty(web_ws_url) then
            webUrl = "wss://"..web_ws_url
            ip = webUrl.."/46"
        end

        -- 验区使用
        if enableInsideServer then
            ip = ip .. "/"..81
        end

        port = webPort
        connect_type = isWebSocket and CONNECT_TYPE_WEB or CONNECT_TYPE_TCP
        log.DirectWarning("net_gateway_module StartConnect-WebSocket,ip:"..tostring(ip)..",port:"..tostring(port), "Net Debug")
    else
        if enableInsideServer then
            ip = ip .. "/"..81
        end
        log.DirectWarning("net_gateway_module StartConnect,ip:"..tostring(ip)..",port:"..tostring(port), "Net Debug")
    end

    bConnectSuccess = false
    net.Disconnect()
    net.Connect(
        ip,
        port,
        connect_type,
        function(errCode, reason)
            --保存错误码
            last_error_code = errCode
            last_error_reason = reason

            if nil == reason then
                reason = 0
            end

            if errCode == 0 then
                util.PeekWatch("Login", "socket 成功")

                log.LogWarning("socket 连接成功,ip:"..tostring(ip)..",port:"..tostring(port), "Net Debug")

                SendHandShake()
                --改变连接状态
                bConnectSuccess = true
                login_module.SaveConnectedContext(ip, port, connect_type)
                --发送连接成功的消息
                event.Trigger(event.CONNECT_SUCCESS, errCode)

                --回调外部
                --[[ 不知为何之前放这里，现在放到HandShake回复那里处理
                    if connect_callback then
                    connect_callback(last_error_code)
                end
                ]]
                
            else
                log.LogError("socket 连接检测到异常,ip:"..tostring(ip)..",port:"..tostring(port), "Net Debug")

                event.Trigger("SERVERERROR_LOG_INFO", tostring(last_error_code), last_error_reason)
                local reasonToAdjustEvents = {
                    -- SOCKET_ERROR.SOCKET_ERROR_CONNECT_FAIL
                    [1] = "ouox6f", --ConnectionFailed
                    -- SOCKET_ERROR.SOCKET_ERROR_CONNECT_ERROR
                    [3] = "jhvl7w", --ConnectionError
                    -- SOCKET_ERROR.SOCKET_ERROR_CLIENT_HANDSHAKE_TIMEOUT
                    [7] = "acz1pl",
                    -- SOCKET_ERROR.SOCKET_ERROR_SERVER_HANDSHAKE_DISCONNECT
                    [8] = "bx00o2" --ConnectionError
                }

                local adEvent = reasonToAdjustEvents[reason]
                if adEvent then
                --print("上报网络错误", adEvent)
                --local ev = AdjustEvent(adEvent)
                --Adjust.trackEvent(ev)
                end

                if true == bConnectSuccess then
                    if connect_state == game.CONNECT_STATE_LOGIN or connect_state == game.STATE_LOGIN_TYPE then
                        -- 登录界面 或 在登录状态 不走 net_reconnect_module.OnDisconnect 的重连机制，否则 ui_login_main 中 ConnectServer
                        -- 的握手成功回调将被 net_login_module->ConnectLobby->net_gateway_module.ConnectGateway 的回调方法覆盖，
                        -- 这里通过多次 socket 重连， 失败后走 FireConnectErrorEvent() -> login_module.ReturnLogin()，在主界面显示函数中调用自动登录/登录界面流程
                        log.LoginWarning("登录过程中 socket 异常，防止可能的握手时异常引用发的登录数据不全报错")
                        LoginStateClearCache()
                        ChangeNextConnect()
                    else
                        --发送断线的消息
                        event.Trigger(event.CONNECT_FAILED_DISCONNECT, errCode, true)
                    end

                    bConnectSuccess = false
                else
                    ChangeNextConnect()					
                end
            end
        end
    )
end
---@function 更新连接类型
function UpdateConnectType()
    if cur_connect_type == CONNECT_TYPE_TCP then
        cur_connect_type = CONNECT_TYPE_WEB
    elseif cur_connect_type == CONNECT_TYPE_WEB then
        cur_connect_type= CONNECT_TYPE_TCP
    end
end

--转换一次连接
function ChangeNextConnect()
    log.DirectWarning("ChangeNextConnect, connect type:"..cur_connect_type, "Net Debug")
    cur_connect_index = cur_connect_index + 1
    cur_reconnect_time = cur_reconnect_time + 1

    if CONNECT_TYPE_UDP == cur_connect_type then
        local udp_count = table.getn(connect_udp_ip_list)
        --已经是最后一次连接了,转换模式,链接tcp
        if cur_reconnect_time > udp_count then
            cur_connect_type = isWebSocket and CONNECT_TYPE_WEB or CONNECT_TYPE_TCP
            local tcp_count = table.getn(connect_tcp_ip_list)
            cur_connect_index = math.random(1, tcp_count)
            cur_reconnect_time = 0
            ChangeNextConnect()
        else --使用udp链接
            local udp_count = table.getn(connect_udp_ip_list)

            --取模,转换一下下标
            cur_connect_index = math.mod(cur_connect_index, udp_count)
            if cur_connect_index == 0 then
                cur_connect_index = udp_count
            end

            local udp_ip = connect_udp_ip_list[cur_connect_index]
            local udp_port = connect_udp_port_list[cur_connect_index]

            StartConnect(udp_ip, udp_port, CONNECT_TYPE_UDP)
        end
    else ------CONNECT_TYPE_TCP
        local tcp_count = table.getn(connect_tcp_ip_list)
        --已经是最后一次连接了
        if cur_reconnect_time > tcp_count then
            -- 如果没试过切换连接类型，则切换类型
            if not bChangeConnectType then
                bChangeConnectType = true
                -- 如果一个类型的连接全部失败，则切换类型，再试一次
                -- 例如WebSocket的iplist轮询都失败了，切换到TCP轮询
                log.LogWarning("ChangeNextConnectType, connect type:"..cur_connect_type, "Net Debug")
                cur_reconnect_time = 0
                cur_connect_index = 0
                UpdateConnectType()
                ChangeNextConnect()
                return
            end
            event.Trigger("SERVERERROR_LOG_INFO", "ChangeNextConnect 连接网关失败", "Net Debug")
            --log.Error("连接网关失败")
            log.LogError("ChangeNextConnect 连接网关失败", "Net Debug")
            FireConnectErrorEvent()
            return
        else --使用tcp链接
            --取模,转换一下下标
            local tcp_ip
            local tcp_port
            local check_ip = false
            for i = 0, #connect_tcp_ip_list-1 do
                local offset = (i+cur_connect_index)%tcp_count
                if offset == 0 then
                    offset = tcp_count
                end
                --判断ip格式是否符合连接的类型(TCP或WebSocket)
                local cur_ip = connect_tcp_ip_list[offset]
                local cur_port = connect_tcp_port_list[offset]
                log.LogWarning("ChangeNextConnect, connect cur_ip:"..cur_ip.." cur_port:"..cur_port, "Net Debug")
                local is_ws_ip = string.sub(cur_ip, 1, 5) == "ws://" or string.sub(cur_ip, 1, 6) == "wss://"
                if cur_connect_type == CONNECT_TYPE_WEB then
                    if is_ws_ip then
                        cur_connect_index = offset
                        tcp_ip = cur_ip
                        tcp_port = cur_port
                    else
                        log.Warning("网关地址切换错误,WebSocket需要带ws://开头  下标",offset,"  ip:",cur_ip,"  port:",cur_port)
                        -- 轮询ip
                        check_ip = true
                    end
                    break
                elseif cur_connect_type == CONNECT_TYPE_TCP then
                    if not is_ws_ip then
                        cur_connect_index = offset
                        tcp_ip = cur_ip
                        tcp_port = cur_port
                    else
                        log.Warning("网关地址切换错误,TCP不带ws://开头  下标",offset,"  ip:",cur_ip,"  port:",cur_port)
                        check_ip = true
                    end
                    break
                else
                    log.Error("网关类型错误：",cur_connect_type,"  ip:",cur_ip,"  port:",cur_port)
                end
            end
            -- 如果需要轮询ip，则继续轮询
            if check_ip then
                ChangeNextConnect()
                return
            end
            -- 设置并记录当前连接类型
            local setting_server_data = require "setting_server_data"
            setting_server_data.SetConnectType(cur_connect_type)
            if tcp_ip == nil or tcp_port == nil then
                log.Error("ChangeNextConnect 链接网关失败,网关组找不到匹配的ip地址(检查网关组ip wx://开头是否有问题),  链接类型:",cur_connect_type)
                FireConnectErrorEvent()
                return
            end

            print("zzd____tcp_port:",tcp_port)
            --2022.8.17 客户端控制台加个开关，开启后，连接服务器的端口强制换成801----start

            if PlayerPrefs.GetInt("Port:801",0) == 1 then
                tcp_port = 801
            end
            if PlayerPrefs.GetInt("Port:802",0) == 1 then
                tcp_port = 802
            end
            if PlayerPrefs.GetInt("Switch:ws://*************:801",0) == 1 then
                tcp_ip = "ws://*************"
                tcp_port = 801
            end
            --print(PlayerPrefs.GetInt("Port:801",0),PlayerPrefs.GetInt("Port:801",0))
            --print("zzd____",tcp_port)
            --2022.8.17 客户端控制台加个开关，开启后，连接服务器的端口强制换成801----end
            StartConnect(tcp_ip, tcp_port, cur_connect_type)
        end
   
        local connect_test = require "connect_test"
        connect_test.ChangeNextConnect()
    end

end

function SetPort801()
    util.RegisterConsole("Port:801",0,function ( s )
        if s == 1 then
            print("zzd___Port:801   1")
            --tcp_port = 801
            local mark = "Port:802"
            if PlayerPrefs.GetInt(mark,0) == 1 then
                print("zzd___Port:801   1  ___Port:802 ===   1    需要修改为 0")
                local swifunc = {}
                local st = PlayerPrefs.GetInt(mark, 0)
                local tst = 1-st
                PlayerPrefs.SetInt(mark, tst)
                local desc = mark..':'..st..' to '..tst
                local LunarConsole = require "lunar_console"
                LunarConsole.UnregisterAction(desc)

                local debug_action = require "debug_action"
                debug_action.Unregister(desc)
                debug_action.Unregister4(desc)

                print("    显示Port:802")
                SetPort802()
            end
        end
    end)
end
function SetPort802()
    util.RegisterConsole("Port:802",0,function ( s )
        if s == 1 then
            print("zzd___Port:802   1")
            local mark = "Port:801"
            if PlayerPrefs.GetInt(mark,0) == 1 then
                print("zzd___Port:802   1  ___Port:801 ===   1    需要修改为 0")
                local swifunc = {}

                local st = PlayerPrefs.GetInt(mark, 0)
                local tst = 1-st
                PlayerPrefs.SetInt(mark, tst)
                local desc = mark..':'..st..' to '..tst
                local LunarConsole = require "lunar_console"
                LunarConsole.UnregisterAction(desc)

                local debug_action = require "debug_action"
                debug_action.Unregister(desc)
                debug_action.Unregister4(desc)

                SetPort801()
            end
        end
    end)
end

function SetTestIP()
    util.RegisterConsole("Switch:ws://*************:801",0,function ( s )

    end)
end

--连接网关按列表连接,要求list个数全部一样
function ConnectGateway(
    tcp_ip_list,
    tcp_port_list,
    udp_ip_list,
    udp_port_list,
    socket_callback,
    gateway_callback,
    cur_connect_state,conReason)

    --log.LogWarning("net_gateway_module ConnectGateway 开始连接网关", "Net Debug")
    util.PeekWatch("Login", "开始连接网关")
    log.LoginWarning("开始连接网关")
    event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_开始连接网关")
    
    --连接网关原因
    if conReason==ENUM_CON_REASON.changeWorld then
        sConReason=ENUM_CON_REASON.changeWorld
    else
        sConReason=ENUM_CON_REASON.login    --默认是登录
    end

    --检查输入
    if nil == tcp_ip_list then
        --log.ErrorFormat("ConnectGateway::tcp_ip_list==nil")
        tcp_ip_list = {}
    --return
    end

    if nil == tcp_port_list then
        --log.ErrorFormat("ConnectGateway::tcp_port_list==nil")
        --return
        tcp_port_list = {}
    end

    if nil == udp_ip_list then
        --log.ErrorFormat("ConnectGateway::udp_ip_list==nil")
        --return
        udp_ip_list = {}
    end

    if nil == udp_port_list then
        --log.ErrorFormat("ConnectGateway::udp_port_list==nil")
        --return
        udp_port_list = {}
    end

    local tcpIPCount = table.getn(tcp_ip_list)
    local tcpPortCount = table.getn(tcp_port_list)
    if tcpIPCount ~= tcpPortCount then
        --log.ErrorFormat("ConnectGateway::tcpIPCount ~=tcpPortCount")
        log.LogError("ConnectGateway::tcpIPCount ~=tcpPortCount 网关配置异常", "Net Debug")
        return
    end

    local udpIPCount = table.getn(udp_ip_list)
    local udpPortCount = table.getn(udp_port_list)

    --保存现场
    connect_tcp_ip_list = tcp_ip_list
    connect_tcp_port_list = tcp_port_list
    connect_udp_ip_list = udp_ip_list
    connect_udp_port_list = udp_port_list
    connect_callback = socket_callback
    handshake_callback = gateway_callback
    connect_state = cur_connect_state

    --开始坐标,优先 web
    local setting_server_data = require "setting_server_data"
    cur_connect_type = setting_server_data.GetConnectType()
    --print("udpIPCount>>>>>>>",udpIPCount)
    if udpIPCount>=1 then
        cur_connect_index = math.random(1, udpIPCount)
    end
    cur_reconnect_time = 0

    --循环连接IP和端口
    ChangeNextConnect()
end

function OnConnectGatewayError(errCode, errReason)
    local net_reconnect_module = require "net_reconnect_module"
    net_reconnect_module.OnConnectGatewayError("connect gateway error", errCode, errReason)

        event.Trigger("SERVERERROR_LOG_INFO", tostring(errCode), errReason)
end

--取得tcp ip
function GetTCPIPList()
    return connect_tcp_ip_list
end

--取得 TCP 端口
function GetTCPPortList()
    return connect_tcp_port_list
end

--取得UDP IP 列表
function GetUDPIPList()
    return connect_udp_ip_list
end

--取得UDP 端口列表
function GetUDPPortList()
    return connect_udp_port_list
end

function S2CUCode(msg)
    --print("connect_state:", connect_state)
    local jsonData = msg.serverinfo
    local server_data = require "server_data"
    local sw = server_data.GetGateGetServerListSW()
    
    if sw and not string.IsNullOrEmpty(jsonData) then
        local  net_logic_module = require "net_logic_module"
        net_logic_module.hand_reset_net_ping_onTimer() 
        log.DirectWarning("网关请求服务器数据成功")
        local server_data = require "server_data"
        local success = server_data.SetGateServerList(jsonData)
        if success then
            server_data.DoCheckFinish()
        else
            server_data.LoadLocalServerData()
        end
        return
    end   
    
	curUcode = msg.uCode
    net.ConnectSuccess()
    if nil ~= handshake_callback then
        handshake_callback()
    end
    -- nettest时打开
    --if true then
    --  return
    --end
end

--取得当前连接状态的类型
function GetConnectState()
    return connect_state
end

-- local timeObj = nil
-- 客户端检测是否主动断开连接
-- 简化重连逻辑，由 net_logic_module.net_ping_onTimer 的 ping 超时定时器完成重连请求
function CheckNetWorkDisConnect()
 --    timeObj = base_object()
 --    timeObj:CreateTimeTicker(0, function()
	-- 	local last = os.time()
	-- 	while true do
	-- 		local now = os.time()
	-- 		local pass = now - last
	-- 		local state = game.GetCurState()
	-- 		if state == game.STATE_LOGIN_TYPE then
	-- 			break
	-- 		end
	-- 		if pass >= 10 then
 --                log.LogWarning("Ping 超时，弹出网络异常提示，准备重连", "Net Debug")
	-- 			-- 如果服务器已经通知断开，不需要重连
	-- 			--log.ErrorFormat("客户端心跳检测自动断开连接")
	-- 			util.DelayCall(1, function()
 --                    log.LogWarning("Ping 超时，且判定网络已断开，发送连接lobby失败消息", "Net Debug")

 --                    --未找到此处引用的 errCode 定义
	-- 				event.Trigger(event.CONNECT_FAILED_LOBBY, errCode)
	-- 			end)                    
	-- 			break
	-- 		end
	-- 		coroutine.yield(1)
	-- 	end
	-- 	if timeObj ~= nil then
	-- 		timeObj:Dispose()
	-- 		timeObj = nil
	-- 	end
	-- end)
end

function StopCheckPing()
	--if timeObj ~= nil then
	--	timeObj:Dispose()
	--	timeObj = nil
	--end
end
event.Register(event.KICKOUT_CLIENT, StopCheckPing)

local pingVal = 0
function S2CPing(msg)
    -- print("Ping消息已出发")
    local Ping = gateway_pb.TMSG_GATEWAY_PING_RSP()
    Ping.clientTick = math.floor(time.realtimeSinceStartup * 1000)
    event.Trigger(event.PING_VALUE_CAHNGED, msg.latency)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Gateway, 0, msg_pb.MSG_GATEWAY_PING_RSP, Ping,nil,nil,nil,true)

    -- 客户端检测网络是否主动断开
    --if timeObj ~= nil then
    --    timeObj:Dispose()
    --    timeObj = nil
    --end
    CheckNetWorkDisConnect()
end

--发送网关握手消息
function SendHandShake()
    util.PeekWatch("Login", "发送网关握手消息")
    log.LoginWarning("发送网关握手消息")
    event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_发送网关握手消息")
    local handShake = gateway_pb.TMSG_GATEWAY_HANDSHAKE_NTF()
    handShake.isIntranet = 1
    handShake.udpPort = 5555
    handShake.macAddress = Utility.GetMacAddress()
	-- 如果是重新登录，UCode是0，如果是直接恢复客户端，发送保存到UCode
    handShake.uCode = curUcode or 0
	handShake.msgSyncIndex = msg_syn.GetMsgSyncIndex()
    if net.GetNetUnpackType() ~= net.ENUM_UNPACK_STRATEGY.BADDBADD then
        handShake.unpackStategy = net.GetNetUnpackType()
    end
    --判断一下是否需要网关请求服务器列表
    local server_data = require "server_data"
    local sw = server_data.GetGateGetServerListSW()
    if sw then
        handShake.params = "1"        
    end
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Gateway, 0, msg_pb.MSG_GATEWAY_HANDSHAKE_NTF, handShake, true,nil,nil,true)

    event.Trigger(event.NET_HANDSHAKE_EVENT)
end

-- 发往登录服握手消息
function SendLoginServerHandShake()
    util.PeekWatch("Login", "发送登录服握手消息")
    --log.LoginWarning("发送登录服握手消息")
    event.Trigger(event.NET_HANDSHAKE_LOGINSERVER_EVENT)
    
    local handShake = login_pb.TMSG_LOGIN_HANDSHAKE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Login, 0, msg_pb.MSG_LOGIN_HANDSHAKE_REQ, handShake,nil,nil,nil,true)
end

packet_test_len = 1
packet_recv_len = 1
packet_test_count = 0
test_last_tick = 0
test_timer_handler = nil

function S2CTestpacket(msg)
    if msg.errorCode ~= 0 then
        log.ErrorFormat("packet test error" .. msg.errorCode)
        return
    end

    if msg.len ~= packet_test_len then
        log.ErrorFormat("packet len error" .. msg.len .. "," .. packet_test_len)
        return
    end

    if msg.len ~= string.len(msg.data) then
        log.ErrorFormat("packet len error1" .. msg.len .. "," .. string.len(msg.data))
        return
    end

    if string.find(msg.data, "[^s]") then
        log.ErrorFormat("packet error" .. msg.len .. "," .. msg.data)
        return
    end
    packet_recv_len = msg.len

    local curTick = util.GetCurTickCount()
    local offset = curTick - test_last_tick
    if offset > 100 then
        --print("test tick" .. offset .. msg.len)

        if offset > 1000 then
            log.ErrorFormat("packet overtime" .. offset)
        end
    end
end

function StartTestTimer()
    if test_timer_handler ~= nil then
        return
    end

    test_timer_handler = BaseObject()
    test_timer_handler:SetUpdateTimer(
        function(deltaTime)
            TestSendPack()
        end
    )
end

function TestSendPack()
    if packet_test_len >= 8100 then
        packet_test_len = 1
        packet_recv_len = 1
    end

    if packet_test_len ~= packet_recv_len then
        return
    end

    local msg = gateway_pb.TMSG_GATEWAY_TEST_PACKET_REQ()
    msg.len = packet_test_len
    msg.rsplen = packet_test_len + 1

    msg.data = string.rep("s", packet_test_len)

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Gateway, 0, msg_pb.MSG_GATEWAY_TEST_PACKET_REQ, msg, true,nil,nil,true)

    packet_test_len = packet_test_len + 1

    test_last_tick = util.GetCurTickCount()

    packet_test_count = packet_test_count + 1

    if packet_test_count % 200 == 0 then
    --print("test count" .. packet_test_count)
    end
end

function S2CHandShake(msg)
    print("=====================S2CHandShake======================",msg)
    util.PeekWatch("Login", "收到握手成功消息")
    log.LoginWarning("收到握手成功消息")
    event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_收到握手成功消息")
    --握手成功回调
    if connect_callback then
		if msg:HasField("worldID") then
			connect_callback(last_error_code, msg.worldID, last_error_reason)
            log.DirectWarning("S2CHandShake serverid:",msg.worldID)
            PlayerPrefs.SetInt("serverid",msg.worldID)
            local ui_login_main_mgr = require "ui_login_main_mgr"
            ui_login_main_mgr.AddLoginServerList(msg.worldID)
		else
           event.Trigger("SERVERERROR_LOG_INFO", tostring(last_error_code), last_error_reason)
			connect_callback(last_error_code, nil, last_error_reason)
		end
    end    

    if last_error_code == 0 then
        if sConReason==ENUM_CON_REASON.changeWorld then 
            --换区 暂时不需要做什么
            -- print("S2CHandShake::sConReason>>>>换区")
            --log.LogWarning("S2CHandShake sConReason：换区", "Net Debug")

            -- 2021-12-06 换区之后服务器没有下发玩家数据，bug待服务器查，暂时由客户端主动登录
            login_module.S2CHandShake(msg)
        else
            --登录
            -- print("S2CHandShake::sConReason>>>>登录")
            --log.LogWarning("S2CHandShake sConReason：登录", "Net Debug")
            login_module.S2CHandShake(msg)
        end
    end
    
    local connect_test = require "connect_test"
    connect_test.ResetChangeFlag()
end

-- 开始同步消息序号
function S2CGatewaySynRum(msg)
    util.PeekWatch("Login", "接收同步消息序号,isResume:",msg.isResume)

	if msg.isResume == true then
		-- 恢复客户端
        --服务器维护，挡人功能时，不与游戏服进行握手，直接进行登录，重复握手报服务器错误码46
        if login_module.IsLoginServerMaintenance() then
            --log.LoginWarning("服务器维护中")
            local game = require "game"
            login_module.LoginGameServer(game.gameWorldName)
        else
            -- do nothing
        end
	else
		-- 不恢复客户端，重走登录流程
		--普通登陆状态
		if connect_state == CONNECT_STATE_LOGIN then
			--print("S2CUCode:", msg)
			local game = require "game"
			game.EnterState(game.STATE_CONNECTED)
			SendLoginServerHandShake()
		end

		--返回大厅状态
		if connect_state == CONNECT_STATE_RETUREN_HALL then
			--login_module.AutoLoginServer()
			SendLoginServerHandShake()
		end
	end
	
	msg_syn.SetMsgSyncIndex(msg.msgSyncIndex)
end

-- 客户端服务器当前序号检查
function S2CSynCheckIndex(msg)
	local rspMsg = gateway_pb.TMSG_GATEWAY_SYNC_CHK_RSP()
    rspMsg.msgSyncIndex = msg_syn.GetMsgSyncIndex()
	--print("S2CSynCheckIndex===================", msg.msgSyncIndex, rspMsg.msgSyncIndex)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Gateway, 0, msg_pb.MSG_GATEWAY_SYNC_CHK_RSP, rspMsg, true,nil,nil,true)
end

-- 服务器回复客户端的ping消息
function S2CGatewayPingRSPRSP()
	
end


--服务器回复客户端密钥
function S2CGatewayHandShakeNTF(msg)
    --重置密钥
    print("接收服务器响应",msg.serverid)
    event.Trigger(event.UPDATA_GATEWAY_STEP, msg.serverid)
end


-- 服务器网关同步worldid,只针对webUrl情况
function S2CGatewaySyncWorldid(msg)
    -- msg.dwWorldID 实际的worldid(如果有合服，是合服后的)
    -- msg.dwFromWorldID 服务器校验后的worldid(如果有合服，是合服前的)
    -- 服务器下发的int类型真机上有小数点
    local dwWorldID = util.Float2Int(msg.dwWorldID)
    log.DirectWarning("S2CGatewaySyncWorldid:dwWorldID:", dwWorldID, ", dwFromWorldID:", msg.dwFromWorldID,
     "error code:", msg.errorCode)

    if msg.errorCode ~= 0 and files_version_mgr.IsUseWebUrlLogin() then
		log.DirectWarning("网关校验worldid异常，dwFromWorldID：",msg.dwFromWorldID, ", dwWorldID:", dwWorldID)
        event.Trigger(event.GAME_EVENT_REPORT, "gateway_sync_worldid_error", {fromWorldid = msg.dwFromWorldID, curWorldid = dwWorldID})
		-- net.Disconnect()
		-- local message_box = require "message_box"
		-- function okCall(callbackData, nRet)
		-- 	if message_box.RESULT_YES == nRet then
		-- 		login_module.ReturnLogin()
		-- 	end
		-- end
		-- message_box.Open(lang.Get(100000+msg.errorCode), message_box.STYLE_YES, okCall, 0, Lang.KEY_OK,Lang.KEY_CANCEL,Lang.KEY_SYSTEM_TIPS,message_box.en_msgbox_net_type)
    -- else
    end

    -- 客户端本地直接保存服务器下发的worldid
    if not ReviewingUtil.IsReviewing() then
        local ui_login_main = require "ui_login_main"
        ui_login_main.SaveWorldId(dwWorldID)
    end
end

local MessageTable = {
    {msg_pb.MSG_GATEWAY_PING_REQ,			S2CPing,		    gateway_pb.TMSG_GATEWAY_PING_REQ},
    {msg_pb.MSG_GATEWAY_UCODE_NTF,			S2CUCode,		    gateway_pb.TMSG_GATEWAY_UCODE_NTF},
    {msg_pb.MSG_GATEWAY_TEST_PACKET_RSP,	S2CTestpacket,	    gateway_pb.TMSG_GATEWAY_TEST_PACKET_RSP},
    {msg_pb.MSG_LOGIN_HANDSHAKE_RSP,        S2CHandShake,       login_pb.TMSG_LOGIN_HANDSHAKE_RSP},
	{msg_pb.MSG_GATEWAY_SYN_RUM_NTF,        S2CGatewaySynRum,   gateway_pb.TMSG_GATEWAY_SYN_RUM_NTF},
	{msg_pb.MSG_GATEWAY_SYN_CHK_NTF,        S2CSynCheckIndex,   gateway_pb.TMSG_GATEWAY_SYN_CHK_NTF},
	{msg_pb.MSG_GATEWAY_PING_RSP_RSP,       S2CGatewayPingRSPRSP,   gateway_pb.TMSG_GATEWAY_PING_RSP_RSP},
    {msg_pb.MSG_GATEWAY_HANDSHAKE_STEP2_NTF, S2CGatewayHandShakeNTF, gateway_pb.TMSG_GATEWAY_HANDSHAKE_STEP2_NTF },
    {msg_pb.MSG_GATEWAY_SYNC_WORLDID,       S2CGatewaySyncWorldid, gateway_pb.TMSG_GATEWAY_SYNC_WORLDID},
}

net_route.RegisterMsgHandlers(MessageTable)

function LoginStateClearCache()
    curUcode = 0
end

function OnStateEnter(eventName, curState)
    if curState == game.STATE_LOGIN_TYPE then
		curUcode = 0
    end
end

event.Register(event.STATE_ENTER, OnStateEnter)
