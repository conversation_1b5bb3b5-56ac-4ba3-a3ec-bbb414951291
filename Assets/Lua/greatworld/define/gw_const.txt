--- Created by fgy.
--- DateTime: 2024/6/4 16:10
--- Des:固定通用常量定义

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWConst
module("gw_const")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--沙盘模型显示等级
SandModuleViewLevel = 5
--x\y 各有多少格子
SandGirdCount = 1000
--沙盘单个格子大小
SandGridSize = { x = 120, y = 96 }
--沙盘单个格子大小(新摄像机)
SandGridSizeNew = { x = 100, y = 100 }
--沙盘第一个格子的初始位置
SandGridBeginPos = { x = 0, y = 0 }
--沙盘四周留白大小
SandGridBorder = 6
--沙盘bg默认缩放
SandBgDefaultScale = 10
--沙盘像素比率
SandPixelSize = 0.01
--沙盘读取不到lang得时候默认赋值
SandLangKey = 149
--沙盘加载数量
SandLoadEntityCount = 3
--沙盘加载帧间隔
SandLoadEntityFps = 3
--沙盘一帧删除数量
SandDelEntityCount = 10
--沙盘数据存储数量每帧
SandAddDataCountFps = 30
--存储数据单位上限
SandStorageDataMax = 3000
--数据存储删除距离
SandDataClearDistance = 300
--沙盘显示小地图Level
SandMinimapLevel = 5
--沙盘显示顶层区域Level
SandTopMapLevel = 2
--沙盘跨区显示提示等级
SandCrossAreaTipsLevel = 3
--沙盘沙盘默认播放动画和特效的层级
SandEffectViewLevel = 6
--沙盘sid生成的系数
SandSidModulus = 10000000
--沙盘个等级服务器显示size
SandSrvLevelSize = { { 300, 655 }, { 150, 300 }, { 90, 110 }, { 70, 90 }, { 50, 70 }, { 30, 50 } }
StormSrvLevelSize = { { 9999, 9999 }, { 9999, 9999 }, { 9999, 9999 }, { 9999, 9999 }, { 9999, 9999 }, { 9999, 9999 } }


--沙盘资源存储上限
ESResStorageCount = {
    250,
    250,
    250,
    200,
    150,
    100,
}

---沙盘等级展示配置其他人增加key值
ESViewLevelAddKey = {
    self = 30000,
    leader = 20000,
    ally = 10000,
    const = 1000,
    other = 0,
}

--对应配置表SandMapViewLevel展示类型
ESViewLevelResType = {
    moveLogic = 0,
    modelRes = 1,
    minIcon1 = 2,
    minIcon2 = 3,
    vfx = 4,
    
    level = 11,
    line = 12,
    blood = 13,
    cityBg = 14,
    marchInfo = 15,
    marchHero = 16,
    name = 17,
    heroTop = 18,
    resTip = 19,
    faceTip = 20,
    radarDemonCastle = 21,
    sandBoxTreasureBtn = 22,
    vanishTime = 23,
    allianceBoss = 24, --同盟boss
    -- 城市竞赛
    ncProtectCD = 25,
    ncReward = 26,
    ncProgress = 27,
    --国会
    presidentIcon = 28, --总统头衔
    --酒馆任务
    tavernTask = 29,
    stormBuildScore = 30,--沙漠风暴建筑上面的分数
    stormBuildName = 31,--沙漠风暴建筑下面的名字+中立+倒计时等
    --国会征服者头像
    congressFaceTips = 35,
    
    zombieStormName = 36, --丧尸灾变名字
    zombieStormBuffIcon = 37, --丧尸灾变状态图标（后续应该可以考虑扩展为通用的buff）
    ------------------不要超过60，用61会有问题，bitjit位运算只支持60位-------------
}
---沙盘类型拥有者
ESOwnerType = {
    Invalid = "Invalid", -- 无效类型
    self = "self", -- 自己
    leader = "leader", -- 盟主
    ally = "ally", -- 盟友
    other = "other", -- 其他人
}

EnSandboxBaseBoolBit =
{
    enSandboxBaseBoolBit_ZombieApocalyse = 0X01, --丧尸灾变中毒标记
}

---- 沙盘对象类型 --这里和服务器保持一致，在sandbox.proto的enSandboxEntityType中，强制要求保持一致
---因此客户端真正的详细实体表现类型不要使用（ESEntityType --对应的是entity的type）而是使用ESEntityResViewType
ESEntityType = {
    Invalid = 0, -- 无效类型
    Base = 1, -- 基地
    NeutralCity = 2, -- 中立城池
    FixedMonster = 3, -- 固定怪
    WonderMonster = 4, -- 游荡怪
    Resource = 5, -- 资源点
    March = 6, -- 行军
    --region 雷达实体类型
    RadarDemonCastle = 7, -- 雷达任务-恶魔城堡
    RadarBeastInvasion = 8, -- 雷达任务-野兽入侵
    RadarEnvironmentExplorate = 9, --雷达任务-环境勘探  
    RadarCollection = 10, --雷达任务-资源采集
    RadarChallenge = 11, --雷达任务-莫妮卡计策挑战任务
    RadarTreasure = 12, --雷达任务-雷达宝箱
    WorldBoss = 13, --enSandboxEntity_WorldBoss
    --endregion
    SandBoxTreasure = 14, --沙盘通用宝箱es
    Carriage = 15, -- 城际货车
    CommonMonster = 16, -- 通用怪物    
    AllianceTrain = 17, -- 联盟火车
    --沙漠风暴
    Storm = 18, -- 沙漠风暴
    
    --通用实体（sandmapentity表）
    CommonEntity = 19,     --通用实体
    ZombieStorm = 27,--丧尸灾变据点
    Relative = 100, -- 关联实体
}
---沙盘上各实体展示的资源类型 --该类型直接关联SandMapViewLevel.csv中的id，并将其id和下列的枚举一一对应，并对应着entity.resProps.visionType
---原本应该和ESEntityType完全同步，但1，服务器把很多实体的实现合并成CommonEntity来实现，出现服务器有客户端没有的情况
---2，还有一些只有客户端本地有的，比如假行军，就会出现客户端有，服务器其没有的情况
ESEntityResViewType = {
    Invalid = 0, -- 无效类型
    Base = 1, -- 基地
    NeutralCity = 2, -- 中立城池
    FixedMonster = 3, -- 固定怪
    WonderMonster = 4, -- 游荡怪
    Resource = 5, -- 资源点
    March = 6, -- 行军
    --region 雷达实体类型
    RadarDemonCastle = 7, -- 雷达任务-恶魔城堡
    RadarBeastInvasion = 8, -- 雷达任务-野兽入侵
    RadarEnvironmentExplorate = 9, --雷达任务-环境勘探  
    RadarCollection = 10, --雷达任务-资源采集
    RadarChallenge = 11, --雷达任务-莫妮卡计策挑战任务
    RadarTreasure = 12, --雷达任务-雷达宝箱
    WorldBoss = 13, --enSandboxEntity_WorldBoss
    --endregion
    SandBoxTreasure = 14, --沙盘通用宝箱es
    RadarSpecialMarch = 15, -- 雷达特殊行军
    CommonMonster = 16, -- 通用怪物
    GeneralTrialSingle = 17, -- 将军试炼个人怪物预留
    GeneralTrialLeague = 18, -- 将军试炼联盟
    AllianceBoss = 19, --同盟boss --通用怪物示例
    TavernTask = 20, --酒馆任务
    BigGun=22, --巨炮
    ZombiesAttackBoss = 23, --宝藏亡灵首领
    Carriage = 101, -- 城际货车
    AllianceTrain = 102, -- 城际飞艇-》联盟火车
    Tree = 1001, --树木
    Mountain = 1002, --山脉
    AllianceFriendBase = 10001, --盟友基地
    AllianceFriendMarch = 10006, --盟友行军
    AllianceLeaderBase = 20001, --盟主基地
    AllianceLeaderMarch = 20006, --盟主行军
    SelfBase = 30001, --自己的基地
    SelfMarch = 30006, --自己行军
    Max = 100000, --最大,当前指 同层优先级

    --沙漠风暴--
    --StormBase = 1200,--沙漠风暴基地
    StormBuild = 24,--沙漠风暴通用建筑
    StormOil = 25,--沙漠风暴油田
    StormBox = 26,--沙漠风暴通用宝箱
    
    ZombieStorm = 27,--丧尸灾变据点
    ZombieStormMarch = 28, --丧尸灾变行军
    
    Camp = 29,      --营寨
}

---- 沙盘行军类型
ESMarchType = {
    Invalid = 0, -- 无效的类型
    Collect = 1, -- 采集
    Spy = 2, -- 侦查
    Reinforce = 3, -- 增援
    Troop = 4, -- 行军：单人打玩家
    AtkMinMon = 5, -- 行军：单人打小怪
    AtkBoss = 6, -- 行军：单人打世界BOSS
    AtkCity = 7, -- 行军：单人打中立城池
    AtkCongress = 8, -- 行军：单人打国会
    MassPlay = 21, -- 集结攻打玩家
    MassBoss = 22, -- 集结攻打末日精英
    MassWonderMon = 23, -- 集结攻打游荡怪
    MassLMBoss = 24, -- 集结攻打联盟BOSS	
    MassCity = 25, -- 集结攻打中立城池
    MassCongress = 26, -- 集结攻打国会
    SingleCongress_ZoneBattle = 27, -- 战区对决-单人打国会
    MassCongress_ZoneBattle = 28, -- 战区对决-集结打国会
    SingleBigGun_ZoneBattle= 29, -- 战区对决-单人巨炮战
    MassBigGun_ZoneBattle= 30, -- 战区对决-集结巨炮战
    
    ZombiesAttack = 31,--集结攻打丧尸来袭Boss普通怪
    ZombiesAttackBoss = 32, -- 集结攻打丧尸来袭Boss

    enSandboxLineType_SiegeCamp = 33, -- 单人前往攻城营寨
    WonderMonster = 100, -- 游荡怪

    AllianceTrain = 202, --特殊行军：联盟火车行军
    CarriageMarch = 201, --特殊行军：车队行军
    RadarExploration = 103, --特殊行军：雷达环境勘探
    RadarHelpAlliance = 104, --特殊行军：雷达帮助盟友
    TavernTaskLine = 105, --特殊行军：酒馆任务

    --沙漠风暴--
    enSandboxLineType_DesertAloneAtkPlay = 10,      --// 沙漠风暴单人攻打玩家
    enSandboxLineType_DesertAloneAtkBuild = 11,     --// 沙漠风暴单人攻打建筑
    enSandboxLineType_DesertMassAtkBuild = 12,      --// 沙漠风暴集结攻打建筑
    enSandboxLineType_DesertMassAtkPlay = 13,       --// 沙漠风暴集结攻打玩家
    enSandboxLineType_DesertCollect = 14,           --// 沙漠风暴采集
    
    zombieStormNormalZombie = 301,   --丧尸灾变小怪
    zombieStormPoisonZombie = 302,   --丧尸灾变剧毒丧尸
    zombieStormZombieTruck = 303,    --丧尸灾变丧尸卡车
    zombieStormSpecialTruck = 304,   --丧尸灾变变异卡车
}

-- 队伍状态
ESTeamState = {
    Invalid = 0, -- 无效的值
    Idle = 1, -- 空闲状态
    Going = 2, -- 出发状态
    Backing = 3, -- 返回状态
    Collecting = 4, -- 在外驻扎状态
    Reinforcing = 5, -- 正在增援 后面废弃掉
    Massing = 6, -- 前往集结状态
    MassWait = 7, -- 集结等待状态
    Max = 8   -- 最大值
}

--沙盘删除优先级
SandDelSortType = { ESEntityType.FixedMonster, ESEntityType.Resource, ESEntityType.Base, ESEntityType.NeutralCity }

--- 目前暂定 3个大层级每个大层级包含3个小层级
ESandOrder = {
    bg = "bg",
    entity = "entity",
    move = "move",
    noScale = "noScale",
    top = "top",
    effect = "effect",
}

--- 目前暂定 3个大层级每个大层级包含3个小层级
ESandOrderKey = {
    Bg = "bg1",
    Bg1 = "bg2",
    Bg2 = "bg3",
    Entity = "entity1",
    Entity1 = "entity2",
    Entity2 = "entity3",
    Move = "move1",
    Move1 = "move2",
    Move2 = "move3",
    NoScale1 = "noScale1",
    NoScale2 = "noScale2",
    NoScale3 = "noScale3",
    Top = "top1",
    Top1 = "top2",
    Top2 = "top3",
    Effect1 = "effect1",
    Effect2 = "effect2",
    Effect3 = "effect3",
}
--- 小层级之间的间隔
ESandOrderInterval = 0.06

--- 资源类型具体更具实际调整
EResPool = {
    ---buiding 资源最大限度为20
    baselititem = 100,
}

--- 场景类型
ESceneType = {
    None = 0,
    Home = 1,
    Sand = 2,
    Truck = 3, --货车
    Storm = 4, --沙漠风暴
    Loading = 100, -- 加载中的状态
}
--沙板类型
SandType = {
    [ESceneType.Sand] = 0,
    [ESceneType.Storm] = 1,
}

--队伍数据类型（不能和沙盘类型绑死）
SandTeamType = {
    Sand = 0,
    Storm = 1,
}

--- 沙盘未配置csv资源配置
ESResPath = {
    -- 国会污染之地
    MoveBaseNew = "art/greatworld/sand/scenenew/items/gwsandmovebaseitem.prefab",
    -- 国会污染之地
    ChoseBaseNew = "art/greatworld/sand/scenenew/items/gwsandchosebaseitem.prefab",
    -- 国会污染之地
    CongressZoneBuffBg = "art/greatworld/sand/scenenew/bgs/citynormalbg.prefab",
}

--- 沙盘场景关闭剔除界面
ESEnterCullLua = {
    ui_main_new = true,
    ui_login_main = true,
    ui_sand_box_back_tips = true,
    ui_pop_tips = true,
    ui_full_screen_effect_panel = true,
    ui_nc_reward = true, -- 战争奖励
    ui_tavern_dispatch_tip = true,
    ui_common_item_anim = true,
    ui_sand_box_monsters_approching = true,--旧版怪物来袭
    ui_monsters_approching_scroller = true,--新版怪物来袭
    ui_countdown_to_siege = true,     --攻城倒计时
    ui_desert_storm_activity_tips_panel = true, --沙漠风暴倒计时
    ui_zombie_storm_warning_timer_panel = true, --丧尸灾变倒计时
}

ESOwnerColor = {
    self = { r = 0.2, g = 0.9, b = 0.38, a = 1 },
    leader = { r = 0.2, g = 0.11, b = 0.38, a = 1 },
    ally = { r = 0.11, g = 0.27, b = 0.38, a = 1 },
    other = { r = 1, g = 1, b = 1, a = 1 },
}

ESMapResAddKey = {
    --自己
    self = 0,
    --盟主
    leader = 1,
    --盟友
    union = 2,
    --其他
    other = 3,
    --敌方
    enemy = 4,
    --不同战区
    diffZone = 5,
    --同战区不同联盟
    diffUnion = 6,
}

ESMapResCityAddKey = {
    --自己
    self = 0,
    --己方战区占领
    zoneOccupy = 1,
    --未占领
    none = 2,
    --其他联盟占领
    otherOccupy = 3,
    --敌对占领
    enemyOccupy = 4
}
--不同行军类型对应的资源key
ESMapResMarchTypeKey = {
    ----行军
    --march = 201,
    ----集结
    --gather = 301,
    ----侦查
    --detect = 401,
    [ESMarchType.Collect] = 201, -- 采集
    [ESMarchType.Spy] = 401, -- 侦查
    [ESMarchType.Reinforce] = 201, -- 增援
    [ESMarchType.Troop] = 201, -- 驻防
    [ESMarchType.AtkMinMon] = 201, -- 巡逻
    [ESMarchType.AtkBoss] = 201, -- 行军：单人打世界BOSS
    [ESMarchType.AtkCity] = 201, -- 行军：单人打中立城池
    [ESMarchType.AtkCongress] = 201, -- 行军：单人打国会 
    [ESMarchType.MassPlay] = 301, -- 集结攻打玩家
    [ESMarchType.MassBoss] = 301, -- 集结攻打末日精英
    [ESMarchType.MassWonderMon] = 301, -- 集结攻打游荡怪
    [ESMarchType.MassLMBoss] = 301, -- 集结攻打联盟BOSS	
    [ESMarchType.MassCity] = 301, -- 集结攻打中立城池
    [ESMarchType.MassCongress] = 301, -- 集结攻打国会
    [ESMarchType.ZombiesAttackBoss] = 301, -- 集结攻打丧尸来袭BOSS	
    [ESMarchType.ZombiesAttack] = 201, -- 集结攻打丧尸来袭
    [ESMarchType.SingleCongress_ZoneBattle]=201, -- 战区对决-单人打国会
    [ESMarchType.MassCongress_ZoneBattle]=301, -- 战区对决-集结打国会
    [ESMarchType.SingleBigGun_ZoneBattle]=201, -- 战区对决-单人打巨炮
    [ESMarchType.MassBigGun_ZoneBattle]=301, -- 战区对决-集结打巨炮
    [ESMarchType.RadarExploration] = 10007,
    [ESMarchType.RadarHelpAlliance] = 10007,
    [ESMarchType.TavernTaskLine] = 10007,

    [ESMarchType.enSandboxLineType_DesertAloneAtkPlay] = 201,
    [ESMarchType.enSandboxLineType_DesertAloneAtkBuild] = 201,
    [ESMarchType.enSandboxLineType_DesertCollect] = 201,
    [ESMarchType.enSandboxLineType_DesertMassAtkBuild] = 301,
    [ESMarchType.enSandboxLineType_DesertMassAtkPlay] = 301,
    [ESMarchType.enSandboxLineType_SiegeCamp] = 201, -- 营寨
    
    [ESMarchType.zombieStormNormalZombie] = 80100, --丧尸灾变普通丧尸
    [ESMarchType.zombieStormPoisonZombie] = 80201, --丧尸灾变剧毒丧尸
    [ESMarchType.zombieStormZombieTruck] = 80301,  --丧尸灾变丧尸卡车
    [ESMarchType.zombieStormSpecialTruck] = 80401, --丧尸灾变变异卡车
}

ESMapResMarchKey = {
    --行军
    March = 201,
    --集结
    Gather = 301,
    --侦查
    Detect = 401,
    --雷达行军
    Radar = 10007,
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--------------------------家园主城专用 避免svn冲突 分割线--------------------------
---x\y 各有多少格子
HomeGirdX = 69
HomeGirdY = 65
HomeBgGirdX = 200
HomeBgGirdY = 200

DefaultBuildingCreatePos = { x = 50, y = 50 }
---迷雾偏移
HomeFogOffset = { x = 25, y = 25 }
HomeMapDefaultY = 0
---单个格子大小
HomeGridSize = 1  --默认为1
---第一个格子的初始位置,定义偏移
HomeGridBeginPos = { x = 0, y = 0 }
---默认缩放
HomeBgDefaultScale = 10
---家园默认相机缩放
HomeCameraZoom = 1500
-- 是否城墙手动配置预设
WallPrefabHandleConfig = false
-----------------------客户端独有-------------------------------------
--客户端独有的EHome开头
EHomeOrderKey = {
    Bg1 = "bg1",
    Bg2 = "bg2",
    Bg3 = "bg3",
    Bg4 = "bg4",
    Bg5 = "bg5",
    EditGrid = "editGrid",
    Entity1 = "entity1",
    Entity2 = "entity2",
    Entity3 = "entity3",
    Entity4 = "entity4",
    Entity5 = "entity5",
    Other = "other",
}
--- 目前暂定 2个大层级每个大层级包含4个小层级  --处理y坐标
EHomeOrder = {
    bg = 0,
    editGrid = 0.2,
    entity = -0.4,
    other = -0.6, --other备用
}
--- 小层级之间的间隔
EHomeOrderInterval = 0.1

EHomeSceneState = {
    None = 0,
    Loading = 1,
    Loaded = 2,
    Leaved = 3, --离开的
    Unloaded = 4, --卸载了的
}

--地图上的实体类型
EHomeEntityType = {
    None = 0,
    Scene = 1, --场景实体 --主要用于处理摄像机等
    Map = 2, --地图    --主要用于各种数据管理
    MapEditGrid = 3, --地图上的编辑格子
    Player = 4, --主角
    Area = 5, --区域
    Building = 6, --建筑
    EventEntity = 7, --事件点实体
    ShowSoldier = 8, --表演士兵
    Hero = 9, --编队Hero
    BuildingEdit = 10, --建筑编辑实体
    CutEntity = 11, --剪彩建筑实体
    DecorateEntity = 12, --装饰实体
    VisitingSurvivor = 13, --来访的幸存者
    VisitingVisitor = 14, --访客
    MainLineEntity = 15, -- 主线关卡奖励
}

EHomeEventEntityType = {
    --预留
}
EHomeBuildingSize = {
    One = 1,
    Two = 2,
    Three = 3,
    Four = 4,
    Six = 6,
    Nine = 9,
    Sixteen = 16,
    TwentyFive = 25,
    ThirtySix = 36,
}
HomeBuildingSizeCfg = {
    [EHomeBuildingSize.One] = { x = 1, y = 1, offsetX1 = 0, offsetY1 = 0, offsetX2 = 0, offsetY2 = 0, cutSize = 1 },
    [EHomeBuildingSize.Two] = { x = 2, y = 1, offsetX1 = 1, offsetY1 = 0, offsetX2 = 0, offsetY2 = 0, cutSize = 1 },
    [EHomeBuildingSize.Three] = { x = 3, y = 1, offsetX1 = 1, offsetY1 = 0, offsetX2 = 1, offsetY2 = 0, cutSize = 1 },
    [EHomeBuildingSize.Four] = { x = 2, y = 2, offsetX1 = 1, offsetY1 = 1, offsetX2 = 0, offsetY2 = 0, cutSize = 1.2 },
    [EHomeBuildingSize.Six] = { x = 3, y = 2, offsetX1 = 1, offsetY1 = 1, offsetX2 = 1, offsetY2 = 0, cutSize = 1.2 },
    [EHomeBuildingSize.Nine] = { x = 3, y = 3, offsetX1 = 1, offsetY1 = 1, offsetX2 = 1, offsetY2 = 1, cutSize = 2 },
    [EHomeBuildingSize.Sixteen] = { x = 4, y = 4, offsetX1 = 2, offsetY1 = 2, offsetX2 = 1, offsetY2 = 1, cutSize = 2.5 },
    [EHomeBuildingSize.TwentyFive] = { x = 5, y = 5, offsetX1 = 2, offsetY1 = 2, offsetX2 = 2, offsetY2 = 2, cutSize = 3 },
    [EHomeBuildingSize.ThirtySix] = { x = 6, y = 6, offsetX1 = 3, offsetY1 = 3, offsetX2 = 2, offsetY2 = 2, cutSize = 3.43 },
}

EHomeAreaType = {
    Free = 0, --自由城建的
    CanExpand = 1, --能扩建的
    SpecialFun = 2, --特定功能的
    Decorative = 3, --装饰区 
}

--- 主城场景关闭剔除界面
EHomeEnterCullLua = {
    ui_main_new = true, --也是沙盘的主界面不关，其他读关掉
    ui_puzzle_game_level = true, --小游戏关卡选择
    ui_main_tower = false, --推塔小游戏
    ui_guide_assistant_new = true, --新手引导助手
    ui_casual_game_common = true, --新手引导常规
    ui_pointing_target = true,
    ui_login_main = true,
    ui_sand_box_back_tips = true, --跨服状态返回界面

    ui_ui_alliance_base_fallen = true, --联盟离线破城补偿
    ui_alliance_invite_main = true, --联盟邀请列表

    ui_nc_reward = true, --战争奖励
    ui_common_item_anim = true,
    ui_full_screen_effect_panel = true, -- 全屏特效
    ui_bomberman_enter = true, -- 解救人质入口
    ui_sand_box_monsters_approching = true, --怪物来袭入口
    ui_desert_storm_activity_tips_panel = true, --沙漠风暴顶部计分板
    ui_countdown_to_siege = true, --攻城倒计时
    ui_zombie_storm_warning_timer_panel = true,
}
--备注 理顺一些关系
--grid是地图上一个一个单元格子
--Area 表示区域
--block 是地图上的地块  会对应一个单元格子
--建筑  建筑会有一个坐标  + 占位

---主城建筑操作类型
EHomeBuildingOperateType = {
    Normal = 0,
    Edit = 1,
}
EHomeBuildEntityTagType = {
    HomeBuild = "homeBuild", --家园建筑
    HomeBuildCut = "HomeBuildCut", --剪彩
    HomeBuildEvent = "HomeBuildEvent", --事件
}
--气泡类型 --对应表名 BubbleManager 
EHomeBubbleEntityType = {
    None = 0,
    REPAIR = 1, -- 修复气泡
    ALLIANCE_HELP = 2, -- 联盟帮助气泡(升级、科研)
    Worker = 3, -- 工人委派气泡
    COUNTDOWN = 4, -- 倒计时气泡 --通用倒计时气泡，非升级。
    TIMER = 5, -- 定时气泡
    Resource = 6, -- 资源产出气泡
    SOLDIER_TRAINING = 7, -- 士兵训练气泡 --兵营气泡，没有士兵在训练or晋升
    SOLDIER_HEALING = 8, -- 士兵治疗气泡 --医院气泡，没有士兵在治疗
    MINI_GAME_ENTRY = 9, -- 小游戏入口气泡
    SUMMON_HALL = 10, -- 召唤殿堂气泡
    SHOP = 11, -- 商店气泡
    TALENT_HALL = 12, -- 人才大厅气泡
    ALLIANCE_JOIN = 13, -- 联盟加入气泡
    WALL_ON_FIRE = 14, -- 城墙起火气泡
    RESEARCH = 15, -- 科研气泡 --没有进行科研
    EQUIPMENT_WORKSHOP = 16, -- 装备工坊气泡
    DRONE = 17, -- 无人机气泡
    RADAR = 18, -- 雷达气泡
    HONOR_WALL = 19, -- 荣誉墙气泡
    GardenGetSoldier = 20, --花园领取士兵（完成小游戏后领取奖励）
    Event = 21, --事件气泡
    FireFighting = 22, --灭火气泡
    SecondScientific = 23, --第二科研中心
    OTHER_ALLIANCE_HELP = 24, -- 联盟帮助气泡(其他) 特殊处理 因其他和建筑升级可以同时存在 所以需要单独处理
    ScientificClockDown = 25, --科研倒计时
    GetSoldier = 26, --领取士兵（包括治疗完成和训练完成、晋升完成）
    ScientificFinish = 27, --科研结束领取气泡

    EventGesture = 28, --事件手势
    HostageRescue = 29, --解救人质

    CarriageDepart = 30, -- 货车待发车气泡
    CarriageReward = 31, -- 货车奖励气泡
    HangUpAttack = 32, -- 挂机奖励 攻击
    HangUpReward = 33, -- 挂机奖励 领取

    COLLECT_HALL = 34, -- 收藏馆
    VisitingSurvivor = 35, --来访幸存者气泡
    Tavern = 36, --橡果酒馆
    FirstRecharge = 37, --首次充值气泡
    MonthCard = 38, --月卡气泡

    AllianceTrainLock = 39, --火车锁定
    AllianceTrainAdd = 40, --火车头像和乘客添加
    AllianceTrainGift = 41, --火车礼包

    UnlockSecondScientificBuilding = 42, --解锁第二科研中心
    EventUAV = 43, --事件无人机气泡
    ArenaEnter = 44, --竞技场气泡
    MonstersApproaching = 45, --怪物入侵气泡
    EventBattle = 46, --事件战斗气泡
    XYXEnterLevel = 47, --小游戏进入关卡
    VisitingVisitor = 48, --访客气泡
    MainLine = 49, -- 主线关卡奖励气泡
    AreaRewardShow= 50, -- 区域奖励展示气泡

    TruckTimer = 51,  --- 货车城建倒计时气泡
    XYXEvent = 52, --小游戏事件
    DroneStar = 53,--神兽结晶研究所气泡
    Upgrade = 1001, --建筑升级气泡(升级倒计时) --可共存  需要排序的要写在表中
    Emoji = 1002, --3D模型走线使用的表情气泡
    Dialog = 1003, --3D模型走线使用的对话气泡

}

--气泡显示类型(条件展示)
EBubbleShowType={
    None=0,
    AreaUnlock=1, --区域解锁
}

BubbleStateEnum = {
    Normal = 0, --气泡正常状态
    Hide = 1, --气泡隐藏状态
    WaitingDispose = 2, --气泡等待销毁
    Dispose = 3, --气泡销毁状态
    Editing = 4, --城建编辑状态  进入编辑状态
}
--对应HeroProList表类型。
HeroProType = {
    MainCitiesForCombat = 4, --主城战斗
    MainCitiesForEconomy = 5 --主城经济
}
--主城奖励加成 对应表 ProSourceToLang
MainCityBonus = {
    LevelBonus = 1, -- 等级加成
    StarUpgradeBonus = 2, -- 升星加成
    SkillBonus = 3, -- 技能  
    EquipmentBonus = 4, -- 装备加成
    DroneBonus = 5, -- 无人机加成
    HonorWall = 6, -- 荣誉墙 
    Survivor = 7, -- 幸存者 
    Decoration = 8, -- 装饰物 
    BuildingBonus = 9, -- 建筑加成
    Technology = 10, -- 科技  
    AllianceTechnology = 11, -- 同盟科技
    City = 12, -- 城市  
    OfficialPosition = 13, -- 官职  
    Outfit = 14, -- 装扮
    VIP = 15, -- VIP
    SuperMonthlyCard = 16, -- 超级月卡
}
EventDeadActionTime = 0.5 --事件死亡动作时间
BuildType = {
    Economy = 1, -- 经济
    Military = 2, -- 军事
    Decoration = 3   -- 装饰
}
--摄像机移动秒数 单位毫秒
CameraMoveTime = 350
--最大等级
MaxLevel = 30
--地图 类型
MapCityType = {
    Building = 1, --主城
    Event = 2, --事件
    Decoration = 3, --装饰
    NoviceEvent = 4, --新手事件
}
-- 使用数字枚举
BuildEventType = {
    MainQuest = 1, -- 主线关卡
    Reward = 2, -- 奖励
    MiniGame = 3, -- 小游戏
    StartPoint = 4   -- 起点
}
--死亡移动秒数 单位秒
DeadMoveTime = 1

--生产数值类型对应数组
--约定：[生产物品ID] = {固定值加成,百分比加成} --若为0则表示没有该加成，0这里是用来占位
ProductionTypeList = {
    [35] = { 0, 301 },
    [36] = { 0, 302 },
    [1] = { 0, 303 },
    [3] = { 0, 0 },
    [38] = { 0, 0 },
    [40009] = { 0, 0 },
    [20003] = { 0, 0 }
}

--产出物品资料的物品ID与建筑之间的对应值。key为建筑type，value为产出的物品ID
--在该列表中的建筑均视为生产建筑。
ProductionBuildingTypeList = {
    [2] = 35,
    [3] = 36,
    [4] = 1,
    [5] = 3,
    [25] = 38,
    [26] = 40009,
    [28] = 20003
}

--保护类建筑的加成技能ID。
--约定：[建筑ID] = {固定值加成,百分比加成} --若为0则表示没有该加成，0这里使用的是位来表示
ProtectedBuildingTypeList = {
    [22] = { 314, 304 },
    [23] = { 315, 305 },
    [24] = { 316, 306 },
}

--计时类建筑的加成技能ID。
--约定：[建筑ID] = {{1固定值缩减,1百分比缩减,1最小值,1固定值增加,1百分比增加},{2固定值缩减,2百分比缩减,2最小值,2固定值增加,2百分比增加}} --若为0则表示没有该加成，0这里使用的是位来表示
TimerBuildingTypeList = {
    [10] = { { 0, 0, 0, 506, 0 } },
    [12] = { { 311, 0, 72000, 0, 0 }, { 312, 0, 72000, 0, 0 } }
}

--建筑显示属性的类型枚举
EBuildingParaType = {
    Normal = 1, --普通的数值，正常显示
    Time = 2, --计时类，参数视为时间，会转化为时间格式
    Percentage = 3, --百分比类，参数会除以10000再补一个%
    Production = 4, --生产类，该建筑仅包含一个参数，使用该参数去获取生产id
    Protected = 5, --保护类建筑，该建筑第一个参数不显示，第一个参数的描述lang将作用于第二个参数。
    Ignore = 6, --忽略，该参数将不显示
    GWMapEffect = 7, --该参数需要读取GWMapEffect表，会受到buff加成。
    Technology = 8, --科研中心，参数1将读取研究速度，参数2在表格中不存在，读取免费加速时间。
    Survivor = 9, --直接读取幸存者技能显示。
    Military = 10, --兵营，需要读取419和401两个值。
    Hospital = 11, --医院，参数1读取404和405，另外需要读取406作为下一个属性。
    Alliance = 12, --联盟，参数1读取502加成，另需要读取503作为下一个属性。
    TrainCenter = 13, --校场，此时参数读取408（固定）和409（百分比）
}

--建筑属性与buff的groupId对应关系。按照顺序依次为参数123456
BuildingBuffIdList = {

}

--具有加成buff属性的建筑列表。该建筑的四个参数全部引用GWMapEffect表
BuffBuildingType = {
    1, 32, 33, 34,
}

enCityRewardType = {
    enRewardType_Survivor = 1,
}

--和加速有关的buff列表。
--约定：[加速类型] = {速度提升,资源降低,免费时间} --若为0则表示没有该加成，0的意义为占位
BuildSpeedUpBuffList = {
    soldier = { 402, 403, 0 },
    hospital = { 406, 407, 0 },
    build = { 504, 505, 506 },
    technology = { 507, 508, 509 },
}

--士兵属性枚举
SoldierAttributeEnum = {
    power = "power", --战力
    morale = "morale", --士气
    weights = "weights", --负重
    hp = "hp", --生命
    atk = "atk", --攻击
    def = "def", --防御
}

--与士兵有关的加成group列表。
--约定：[士兵属性] = {固定值加成,百分比加成} --若为0则表示没有该加成，0这里是用来占位
SoldierBuffList = {
    power = { 0, 0 },
    morale = { 416, 0 },
    weights = { 0, 417 },
    hp = { 410, 413 },
    atk = { 411, 414 },
    def = { 412, 415 },
}

SliderType = {
    point = 1,
    item = 2,
    power = 3,
}

ScientificBuildingId = {
    Building_1 = 20000,
    Building_2 = 21000,
}
--------------------------客户端独有End-------------------------------


--------------------------服务器共有----------------------------------
--当前和服务器共有的定义，en开头
--建筑状态
enBuildingState = {
    enBuildingState_Broken = 0; --破损
    enBuildingState_Normal = 1; --正常
    enBuildingState_Repairing = 2; --修复
    enBuildingState_Constructing = 3; --建造（新建建筑）
    enBuildingState_Upgrading = 4; --建筑升级当中
}
--城建用到的标志
enCityFlag = {
    --这里使用的是位来表示 0x0001 
    enCityFlag_BuildingOpenGift = 2; --建筑剪彩状态
    enCityFlag_AreaUnlock = 3; --区域已解锁，解锁迷雾，如有事件需全部完成才可以建设
    enCityFlag_AreaCanBuilding = 4; --区域已完全解锁，可忽略 enCityFlag_AreaUnlock，同时完成的事件可以清理，减少存储数据
    enCityFlag_QueueFullOpen = 5; --该队列已完全解锁
    enCityFlag_MilitaryRecruit = 6; --兵营招募士兵当中
    enCityFlag_MilitaryUpgrade = 7; --兵营升级士兵当中  
    enCityFlag_AreaCompleteAll = 8; --区域所有事件都已完成
}

--家园主城需要用到的属性  --{0,1} 表示属性id = 0，默认值=1
enCityProp = {
    enCityProp_HospitalCureID = { 0, 0 }; --医院治疗ID，服务于联盟求助使用，自增回环(0，0x7FFFFFFF)   
    enCityProp_Flag = { 4, 0 }; --城市标志
    enCityProp_Sid = { 5, 0 }; --玩家自身建筑唯一ID，自增回环(0，0x7FFFFFFF)）
    --预留20个存数据库属性

    --以下内存缓存计算得到，不需要存数据库，可以调整，有可能存数据库的尽可能往前靠
    enCityProp_TotalPower = { 20, 0 }; --总战斗力（所有建筑物）
    enCityProp_TotalFreeAccelerateTime = { 21, 0 }; --总免费加速时间（秒）
    enCityProp_TotalBeHelpTimes = { 22, 0 }; --总受帮助次数
    enCityProp_TotalHospitalCapacity = { 23, 0 }; --总医院容量
    enCityProp_TotalTrainCenterCapacity = { 24, 0 }; --总校场容量
    enCityProp_HeroMaxLevel = { 25, 0 }; --英雄等级上限，城堡影响的属性
    enCityProp_FreeSummonResetTime = { 26, 0 }; --免费抽重置时间
    enCityProp_MovingSpeedUpN = { 27, 0 }; --行军速度提升n% 
    enCityProp_CurDefendValue = { 28, 0 }; --城防值（也可理解为当前血量）
    enCityProp_WallBeginTime = { 29, 0 }; --城墙燃烧、恢复开始时间（结束时间不为0则表示燃烧开始时间）
    enCityProp_WallFireEndTime = { 30, 0 }; --城墙燃烧结束时间
    enCityProp_ResearchSpeedUpRate1 = { 31, 0 }; --科研加速百分比1
    enCityProp_ResearchSpeedUpRate2 = { 32, 0 }; --科研加速百分比2
    enCityProp_ResearchSpeedUpTime1 = { 33, 0 }; --科研加速时间1
    enCityProp_ResearchSpeedUpTime2 = { 34, 0 }; --科研加速时间2
    enCityProp_MovingSpeedUpN2 = { 35, 0 }; --第二编队行军速度提升n%
    enCityProp_MovingSpeedUpN3 = { 36, 0 }; --第三编队行军速度提升n%
    enCityProp_MovingSpeedUpN4 = { 37, 0 }; --第四编队行军速度提升n%
    enCityProp_TotalFoodProtect = { 38, 0 }; --总粮食保护量
    enCityProp_TotalIronProtect = { 39, 0 }; --总铁矿保护量
    enCityProp_TotalGoldProtect = { 40, 0 }; --总金矿保护量
    enCityProp_TotalReduceBuildNeedN = { 41, 0 }; --总建造消耗资源减少n%（幸存者加部分，最终计算需要加上科技部分）
    enCityProp_TotalCureSpeedUpN = { 42, 0 }; --总伤兵治疗速度提升n%（幸存者加部分，最终计算需要加上科技部分）
    enCityProp_ResearchResourceRate1 = { 43, 0 }; --科研资源减少百分比1
    enCityProp_ResearchResourceRate2 = { 44, 0 }; --科研资源减少百分比2
    enCityProp_WorkerFreeSummonCD = { 45, 0 }; --工人免费招募CD
    enCityProp_EquimentMakeSpeedUp = { 46, 0 }; --装备制作加速
    enCityProp_EquimentMakeCoinReduce = { 47, 0 }; --装备制作金币减少
    enCityProp_ResourceLimitTimeUp = { 48, 0 }; --总资源上限时间提升
    enCityProp_ResourceProduceUpN = { 49, 0 }; --总资源生产提升n%
    enCityProp_MaxBuildingPower = { 50, 0 }; --建筑最高战力
    enCityProp_HiddenQueueNum = { 51, 0 }; --增加的隐藏任务的执行队列
    enCityProp_HiddenRewardNum = { 52, 0 }; --隐藏任务的奖励数
    enCityProp_AllianceHelpAdditionTime = { 53, 0 }; --联盟求助额外减少时间（秒）
    enCityProp_AcornPubCanSendTaskCount = { 54, 0 }; --橡果酒馆可派遣任务数量

    enCityProp_TotalConfigPower = { 55, 0 }; --总配置战力
    enCityProp_RadarMissionBox = { 56, 0 }; --雷达任务箱子数量
    enCityProp_RadarMissionBoxMax = { 57, 0 }; --雷达任务箱子最大数量
    enCityProp_RadarMissionBoxId = { 58, 0 }; --雷达任务箱子ID

    enCityProp_Max = { 61, 0 }; --最大值
}

--建筑类型（与配置表对应起来）
enBuildingType = {
    enBuildingType_Default = 0; --默认--新增示例，对应你新增的建筑类型
    enBuildingType_Main = 1; --城堡，大本
    enBuildingType_Farm = 2; --农田
    enBuildingType_Iron = 3; --铁矿
    enBuildingType_Gold = 4; --金矿
    enBuildingType_Exp = 5; --经验（训练基地）
    enBuildingType_Military = 6; --兵营
    enBuildingType_TrainCenter = 7; --校场
    enBuildingType_Hospital = 8; --医院
    enBuildingType_Formation1 = 9; --编队
    enBuildingType_WorkerHouse = 10; --建筑工小屋
    enBuildingType_Garden = 11; --花园（小游戏）
    enBuildingType_Summon = 12; --召唤殿堂
    enBuildingType_Shop = 13; --巨人游商
    enBuildingType_WorkerCenter = 14; --人才大厅
    enBuildingType_Alliance = 15; --同盟中心
    enBuildingType_Wall = 16; --城门
    enBuildingType_Formation2 = 17; --编队2
    enBuildingType_Formation3 = 18; --编队3
    enBuildingType_Formation4 = 19; --编队4
    enBuildingType_Research1 = 20; --第一科研中心
    enBuildingType_Research2 = 21; --第二科研中心
    enBuildingType_FarmWareHouse = 22; --粮食仓库
    enBuildingType_IronWareHouse = 23; --铁矿仓库
    enBuildingType_GoldWareHouse = 24; --金矿仓库
    enBuildingType_SmelterFactory = 25; --冶炼厂
    enBuildingType_Material = 26; --材料坊
    enBuildingType_Equip = 27; --装备工坊
    enBuildingType_DroneFactory = 28; --无人机材料工厂
    enBuildingType_DroneCenter = 29; --无人机中心
    enBuildingType_Reconnaissance = 30; --侦察机
    enBuildingType_Radar = 31; --雷达车
    enBuildingType_TankCenter = 32; --坦克中心
    enBuildingType_AircraftCenter = 33; --飞机中心
    enBuildingType_MissileCenter = 34; --导弹中心
    enBuildingType_RoadA = 35; --道路（纵向）
    enBuildingType_RoadB = 36; --道路（横向）
    enBuildingType_Wall_Rail = 37; --城墙围栏
    enBuildingType_CarriageCenter = 38; --城际货运中心
    enBuildingType_HangUp = 39; --挂机建筑
    enBuildingType_AllianceTrainCenter = 40; --城际飞艇中心->联盟火车
    enBuildingType_CollectHall = 42; --装饰品收藏馆
    enBuildingType_AcornPub = 43; --橡果酒馆
    enBuildingType_FristRecharge = 44; --首充建筑
    enBuildingType_Arena = 45; --竞技场
    enBuildingType_DroneStar = 46;--结晶研究所
    enBuildingType_Max = 1116; --最大值
}

--生产数值类型
enProductionProType = {
    enProdPro_Food_Output_Rate = 301; -- 粮食产出速度提升n%
    enProdPro_Iron_Output_Rate = 302; -- 铁矿产出速度提升n%
    enProdPro_Gold_Output_Rate = 303; -- 金币产出速度提升n%
    enProdPro_Food_Safe_Amount = 314; -- 粮食保护量n
    enProdPro_Iron_Safe_Amount = 315; -- 铁保护量n
    enProdPro_Gold_Safe_Amount = 316; -- 金保护量n
    enProdPro_Food_Safe_Increase_Rate = 304; -- 粮食保护量提升n%
    enProdPro_Iron_Safe_Increase_Rate = 305; -- 铁矿保护量提升n%
    enProdPro_Gold_Safe_Increase_Rate = 306; -- 金币保护量提升n%
    enProdPro_AFK_Time = 307; -- 挂机累积时间提升
    enProdPro_AFK_Output_Rate = 308; -- 挂机资源产出提升%
    enProdPro_Equip_Make_Speed_Rate = 309; -- 装备工厂制造加速%
    enProdPro_Equip_Cost_Reduce_Rate = 310; -- 装备制造减少金币消耗%
    enProdPro_Free_Adv_Recruit_CD = 311; -- 免费高级招募CD缩短n
    enProdPro_Free_Surv_Recruit_CD = 312; -- 幸存者免费招募CD缩短n
    enProdPro_Extra_Barracks = 313; -- 获得额外兵营
    enProdPro_Food_Safe_Amount = 314; -- 粮食保护量n
    enProdPro_Iron_Safe_Amount = 315; -- 铁保护量n
    enProdPro_Gold_Safe_Amount = 316; -- 金保护量n
    enProdPro_Extra_Farm = 317; -- 获得城建额外农田建筑
    enProdPro_Extra_Iron = 318; -- 获得城建额外铁矿建筑
    enProdPro_Extra_Gold = 319; -- 获得城建额外金矿建筑
    enProdPro_Barrack_Limit_Rate = 401; -- 兵营训练士兵上限提升n%
    enProdPro_Training_Speed_Rate = 402; -- 训练士兵速度提升n%
    enProdPro_Training_Cost_Reduce_Rate = 403; -- 训练士兵的资源消耗降低n%
    enProdPro_Hospital_Capacity = 404; -- 医院容量
    enProdPro_Hospital_Cap_Incr_Rate = 405; -- 医院容量提升n%
    enProdPro_Treatment_Speed_Rate = 406; -- 伤兵治疗速度提升n%
    enProdPro_Treatment_Cost_Rate = 407; -- 治疗消耗资源降低n%
    enProdPro_Parade_Ground = 408; -- 校场容量
    enProdPro_Parade_Cap_Incr_Rate = 409; -- 校场容量提升n%
    enProdPro_Unit_HP_Bonus = 410; -- 士兵生命值提升
    enProdPro_Unit_Att_Bonus = 411; -- 士兵攻击力提升
    enProdPro_Unit_Def_Bonus = 412; -- 士兵防御力提升
    enProdPro_Unit_HP_Incr_Rate = 413; -- 士兵生命值提升n%
    enProdPro_Unit_Att_Incr_Rate = 414; -- 士兵攻击力提升n%
    enProdPro_Unit_Def_Incr_Rate = 415; -- 士兵防御力提升n%
    enProdPro_Unit_Morale = 416; -- 提升士兵士气n
    enProdPro_Unit_Load_Incr_Rate = 417; -- 士兵的负重提升n%
    enProdPro_Unlock_T10_Soldiers = 418; -- 解锁训练10级士兵
    enProdPro_Max_Soldiers_Incr_Num = 419; --最大训练士兵数+n
    enProdPro_Auto_Dispatch = 501; -- 离线成员自动派遣队伍参加集结
    enProdPro_Help_Count = 502; -- 受帮助次数+n
    enProdPro_Help_Time_Reduce = 503; -- 每次帮助额外减少时间n
    enProdPro_Const_Speed_Rate = 504; -- 建造加速+n%
    enProdPro_Const_Cost_Reduce_Rate = 505; -- 建造消耗的资源减少+n%
    enProdPro_Free_Const_Speed = 506; -- 建造免费加速时间+n秒
    enProdPro_Tech_Speed_Rate = 507; -- 科技加速n%
    enProdPro_Tech_Cost_Reduce_Rate = 508; -- 科技消耗的资源减少n%
    enProdPro_Free_Tech_Speed = 509; -- 科技免费加速时间n秒
    enProdPro_March_Speed_Rate = 510; -- 行军速度提升n%
    enProdPro_Zombie_March_Rate = 511; -- 【对丧尸行军】速度提升n%
    enProdPro_Resource_March_Rate = 512; -- 【对采集点行军】速度提升n%
    enProdPro_Enemy_March_Rate = 513; -- 【进攻其他指挥官】行军速度提升n%
    enProdPro_City_March_Rate = 514; -- 【进攻城市时】行军速度提升n%
    enProdPro_Defense_March_Rate = 515; -- 【进行驻守时】行军速度提升n%
    enProdPro_Gather_Speed_Rate = 516; -- 采集速度+n%
    enProdPro_Food_Gather_Rate = 517; -- 食物采集速度提升n%
    enProdPro_Iron_Gather_Rate = 518; -- 铁矿采集速度提升n%
    enProdPro_Gold_Gather_Rate = 519; -- 金币采集速度提升n%
    enProdPro_Load_Bonus_Rate = 520; -- 负重属性提升%
    enProdPro_Can_Not_Add_Shield = 521; -- 不可添加保护罩
    enProdPro_Can_Not_Be_Attacked = 522; -- 不可被攻击
    enProdPro_Can_Not_Be_Spied = 523; -- 不可被侦察
    enProdPro_Can_Not_Be_Gathered = 524; -- 不可被集结

    enProdPro_Accelerate_Dash_Count = 601; -- 关卡(前进模式)加速冲刺次数
    enProdPro_Auto_Dispatch_Mission = 602; -- 隐秘任务自动派遣
    enProdPro_Photo_Sticker_Unlock = 603; -- 大头贴功能解锁
    enProdPro_Shake_Collect_Resources = 604; -- 摇一摇收取资源(24级送)
    enProdPro_Batch_Collect_Resources = 605; -- 批量收取资源(20级送)
    enProdPro_Show_VIP_Level_In_Chat = 606; -- 聊天时显示VIP等级
    enProdPro_Super_Mode_Stealth_Squad = 607; -- 隐密机动队超级模式
    enProdPro_Fast_Challenge_Expedition = 608; -- 荣耀远征快速挑战
    enProdPro_Increase_Truck_Carry_Rate = 701; -- 【拉车】提升货车携带铁粮金的数量n%
    enProdPro_Accelerate_Truck_Speed_Rate = 702; -- 【拉车】加快货车的行进速度n%
    enProdPro_Extra_Loot_On_Attack = 703; -- 【拉车】进攻胜利时进攻胜利时有概率额外抢夺一个货物
    enProdPro_Extra_Loot_On_Defend = 704; -- 【拉车】防守失败时防守失败时有概率夺回一个货物
    enProdPro_Daily_Truck_Increase = 705; -- 【拉车】每日货车数量增加
    enProdPro_Max_Attack_Failure_Limit = 706; -- 【拉车】同一位指挥官攻击您的货车时同一位指挥官攻击您的货车时允许失败的次数上限
    enProdPro_Trade_Contract_Reindeer = 707; -- 【拉车】使用贸易合同刷新使用贸易合同刷新有几率刷出驯鹿货车
    enProdPro_Radar_Task_Points_Rate = 708; -- 【同盟对决】雷达任务获得积分增加n%
    enProdPro_Accelerate_Points_Rate = 709; -- 【同盟对决】使用加速获得积分增加n%
    enProdPro_Hero_Recruit_Points_Rate = 710; -- 【同盟对决】招募英雄获得积分增加n%
    enProdPro_Unlock_Rewards_Tier_4_to_6 = 711; -- 【同盟对决】解锁4-6档奖励
    enProdPro_All_Points_Increase_Rate = 712; -- 【同盟对决】所有获得积分增加n%
    enProdPro_Building_Power_Points_Rate = 713; -- 【同盟对决】建筑战力获得的积分增加n%
    enProdPro_Tech_Power_Points_Rate = 714; -- 【同盟对决】科技战力获得的积分增加n%
    enProdPro_Train_Soldier_Points_Rate = 715; -- 【同盟对决】训练士兵获得的积分增加n%
    enProdPro_Kill_Enemy_Points_Rate = 716; -- 【同盟对决】杀敌所获得的积分增加n%
    enProdPro_Unlock_Rewards_Tier_7_to_9 = 717; -- 【同盟对决】解锁7-9档奖励
    enProdPro_Increase_Dispatch_Queue = 718; -- 隐秘机动队派遣队列数+n
    enProdPro_Increase_Supply_Box_Output = 719; -- 隐秘任务产出神秘补给箱数量+n
    enProdPro_Radar_Mission_Treasure_Box = 720; -- 每次雷达任务获取宝箱
}

--无人机属性
enDroneProp = {
    DRONE_PROP_UNKOWN = 0; --未知属性
    DRONE_PROP_LV = 1; --无人机等级
    DRONE_PROP_REMAINEXP = 2; --剩余经验
    DRONE_PROP_UPGRADESTAGE = 3; --突破阶段
    DRONE_PROP_POWER = 4; --战力
    DRONE_PROP_HP = 5; --生命
    DRONE_PROP_ATTACK = 6; --攻击
    DRONE_PROP_DEFENCE = 7; --防御
    DRONE_PROP_HEROHP = 8; --英雄生命
    DRONE_PROP_HEROATTACK = 9; --英雄攻击
    DRONE_PROP_HERODEFENCE = 10; --英雄防御
    DRONE_PROP_HEROSKILLUP = 11; --英雄技能伤害提升
    DRONE_PROP_HPRATE = 12; --生命转换率
    DRONE_PROP_ATTACKRATE = 13; --攻击转换率
    DRONE_PROP_DEFENCERATE = 14; --防御转换率
    DRONE_PROP_SKILLID = 15; --技能id
    DRONE_PROP_BUFFID = 16; --buffId
    DRONE_PROP_ADDHP = 17; --英雄实际添加的生命
    DRONE_PROP_ADDATTACK = 18; --英雄实际添加的攻击
    DRONE_PROP_ADDDEFENCE = 19; --英雄实际添加的防御
    DRONE_PROP_ADDSKILL = 20; --英雄实际添加的技能伤害
    DRONE_PROP_MAXID = 21; --最大值
}

enSandboxType =
{
    enSandboxType_World = 0;      -- 和世界绑定的沙盘，每个游戏世界对应一个沙盘
    enSandboxType_Desert = 1;     -- 沙漠风暴
    enSandboxType_Max = 2;      -- 枚举最大值
}

--国会相关枚举
--常量
enCongress_Const = {
    enCongress_Const_TimeCfgDefault = 1, --上任时间档次默认下标, enCongress_Const_TimeCfgIndex
    enCongress_Const_ResetForwardSecond = 105, --国会重置时提前N秒重置, GWMapConstant.csv::iIndex,
    enCongress_Const_TimeCfgIndex = 106, --上任时间档次配置, "档次1秒数#档次2秒数#......", GWMapConstant.csv::iIndex,
    enCongress_Const_AppointCD = 107, --任命CD, 秒, GWMapConstant.csv::iIndex,
    enCongress_Const_SearchActiveDays = 108, --搜索玩家活跃天数, GWMapConstant.csv::iIndex,
    enCongress_Const_SearchCD = 109, --搜索玩家CD, 秒, GWMapConstant.csv::iIndex,
    enCongress_Const_Manifesto = 110, --战区宣言字数限制, GWMapConstant.csv::iIndex,
    enCongress_Const_SearchListLimit = 111, --搜索玩家列表上限, GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailCost = 112, --总统邮件发放消耗道具(道具id#数量), GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailCD = 113, --总统邮件发放CD, GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailActiveDays = 114, --总统邮件发送对象活跃天数, GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailLimit = 115, --总统邮件发送对象数量上限, GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailTitleLimit = 116, --总统邮件标题字数上限, GWMapConstant.csv::iIndex,
    enCongress_Const_PresMailContentLimit = 117, --总统邮件文本字数上限, GWMapConstant.csv::iIndex,
    enCongress_Const_ApplyForTimeOut = 118, --申请队列超时时长, 秒, GWMapConstant.csv::iIndex,
    enCongress_Const_OfficialRecordLimit = 120, --官职记录数量上限, GWMapConstant.csv::iIndex,
}

--职位类型, CongressOfficialPosition.csv::PositionType
enCongress_PositionType = {
    enCongress_PositionType_None = 0, --无
    enCongress_PositionType_Pres = 1, --总统
    enCongress_PositionType_Vice = 2, --副总统
    enCongress_PositionType_Tactic = 3, --战略部长
    enCongress_PositionType_Def = 4, --国防部长
    enCongress_PositionType_Build = 5, --建设部长
    enCongress_PositionType_Science = 6, --科学部长
    enCongress_PositionType_Politics = 7, --内政部长
    enCongress_PositionType_Conqueror = 8, --征服者
    enCongress_PositionType_Conqueror_MilitaryCommander = 9, --军事指挥官
    enCongress_PositionType_Conqueror_GovernmentCommander = 10, --政务统帅
}

enCongress_IconName =
{
    [enCongress_PositionType.enCongress_PositionType_Pres] = "sp_icon_bighg",
    [enCongress_PositionType.enCongress_PositionType_Vice] = "ghgl_icon_zw_sx",
    [enCongress_PositionType.enCongress_PositionType_Tactic] = "ghgl_icon_zw_ys",
    [enCongress_PositionType.enCongress_PositionType_Def] = "ghgl_icon_zw_yl",
    [enCongress_PositionType.enCongress_PositionType_Build] = "ghgl_icon_zw_js",
    [enCongress_PositionType.enCongress_PositionType_Science] = "ghgl_icon_zw_kj",
    [enCongress_PositionType.enCongress_PositionType_Politics] = "ghgl_icon_zw_nz",
}

--职位权限; CongressOfficialPosition.csv::PositionAuthority
enCongress_PositionAuthority = {
    enCongress_PositionAuthority_Appoint = 1, --任命, 罢免, 设置上任时间, 踢出排队,
    enCongress_PositionAuthority_Check = 2, --审核申请
    enCongress_PositionAuthority_Award = 3, --嘉奖
}

--职位管理, CongressOfficialPosition.csv::ManualAppointment
enCongress_Manage = {
    enCongress_Manage_False = 0, --不可
    enCongress_Manage_True = 1, --职位可被任命/申请/罢免
}

--嘉奖类型, CongressAwards.csv::AwardsType
enCongress_AwardType = {
    enCongress_AwardType_Pres = 1, --总统
    enCongress_AwardType_General = 2, --将军
    enCongress_AwardType_Officer = 3, --军官
    enCongress_AwardType_Soldier = 4, --士兵
    enCongress_AwardType_Pres_Conqueror = 5, --征服者总统
    enCongress_AwardType_General_Conqueror = 6, --征服者将军
    enCongress_AwardType_Officer_Conqueror = 7, --征服者军官
    enCongress_AwardType_Soldier_Conqueror = 8, --征服者士兵
}

--查询枚举
enSearchCallbackID = {
    enSearchAwardPlayer = 1, --嘉奖列表
    enSearchOfficialPlayer = 2, --官职任命
}

--region 国会嘉奖服务器类型（策划不加表字段，让代码写死，按原流程代码添加...）
congressAwardServerDataMap ={
    self={
        sType="self",
        cfgData={1,2,3,4},
    },
    conqueror={ 
        sType="conqueror",
        cfgData={5,6,7,8}, 
    },
}

--endregion

--region 丧尸灾变
--活动状态
EnZombieApocalypseState =
{
    enZombieApocalypseState_None = 0,-- 无效值
    enZombieApocalypseState_Preview = 1,-- 预告期
    enZombieApocalypseState_Open = 2,-- 开启
    enZombieApocalypseState_Running = 3,-- 运行中
    enZombieApocalypseState_Cool = 4,-- 冷却期
    enZombieApocalypseState_Close = 5,-- 关闭
    enZombieApocalypseState_Max = 6,-- 最大值
}

--据点状态
EnZombieApocalypsePointState =
{
    enZombieApocalypsePointState_None = 0,-- 无效值
    enZombieApocalypsePointState_Prepare = 1,-- 准备阶段
    enZombieApocalypsePointState_PreAttack = 2,-- 即将进攻状态
    enZombieApocalypsePointState_Attack = 3,-- 进攻状态
    enZombieApocalypsePointState_Max = 4,-- 最大值
}

--卡片类型聊天枚举
EnZombieApocalypseCardType =
{
    enZombieApocalypseCardType_None = 0,-- 无效值
    enZombieApocalypseCardType_Prepare = 1,-- 准备期开始提醒
    enZombieApocalypseCardType_MutantTruck = 2,-- 变异卡车
    enZombieApocalypseCardType_Pass = 3,-- 通关
    enZombieApocalypseCardType_PassFail = 4,--通关失败
    enZombieApocalypseCardType_Max = 5,-- 最大值
}

--列表类型聊天枚举
EnZombieApocalypseListType =
{
    enZombieApocalypseListType_None = 0,-- 无效值
    enZombieApocalypseListType_AssistDetoxification = 1,-- 协助解毒
    enZombieApocalypseListType_AssistDefence = 2,-- 协助防守
    enZombieApocalypseListType_Max = 3,-- 最大值
}

--详情类型聊天枚举
EnZombieApocalypseDetailType =
{
    enZombieApocalypseDetailType_None = 0,-- 无效值
    enZombieApocalypseDetailType_DetoxifyReward = 1,-- 解毒奖励详情
    enZombieApocalypseDetailType_MutantTruck = 2,-- 变异卡车
    enZombieApocalypseDetailType_Max = 3,-- 最大值
}
--endregion
--------------------------服务器共有End ----------------------------------

--------------------------家园主城专用 避免svn冲突 分割线End-----------------------
--建筑类型对应编队队列id
enQueueIndex = {
    [enBuildingType.enBuildingType_Formation1] = 1,
    [enBuildingType.enBuildingType_Formation2] = 2,
    [enBuildingType.enBuildingType_Formation3] = 3,
    [enBuildingType.enBuildingType_Formation4] = 4,
}

--技能标签，客户端显示用
HeroSkillType = {
    Magic = 1, --魔法伤害
    Physics = 2, --物理伤害
    Gain = 3, --增益
}
HeroSkillTypeParam = {
    [HeroSkillType.Magic] = {
        lang = 630027,
        icon = "common_icon_moFang",
    },
    [HeroSkillType.Physics] = {
        lang = 630025,
        icon = "common_icon_wuLi",
    },
    [HeroSkillType.Gain] = {
        lang = 630033,
        icon = "common_icon_zengYi",
    }
}
--（参数类型：1固定值，2万分比）
SkillDesType = {
    Value = 1, --固定值
    Per = 2, --百分比
}

SkillUnLockType = {
    Null = 0,
    Level = 1, --等级不组
    Star = 2, --星级不足
    LevelAndStar = 3, --等级和星际都不足
}

HeroUIType = {
    Null = 0,
    Level = 1,
    Skill = 2,
    Star = 3,
}

--修复条件类型 1 建筑id#建筑等级  条件类型2 关卡id  条件类型3 小游戏关卡id  条件类型4 礼包id
FixConditionType = {
    None = 0, -- 如果没有任何条件或未知条件
    BuildingIDAndLevel = 1, -- 建筑id#建筑等级
    StageID = 2, -- 关卡id
    MiniGameStageID = 3, -- 小游戏关卡id
    GiftPackID = 4  -- 礼包id
}

HomeModelState = {
    Normal = 0,
    Hide = 1,
    WaitingDispose = 2, --等待销毁
    Dispose = 3, --销毁状态
}


--家园实体状态类型
EHomeEntityStateType = {
    Normal = 1,
    WaitingDispose = 2,
    Dispose = 3,
}
--气泡状态的底图状态
BubbleStatePath = {
    Normal = "chengJian_img_bubble_white", --正常状态
    Finished = "chengJian_img_bubble_yellow", --检测变黄
    Error = "xinshou_qipao_rd", --检测变红
}
--家园特效类型
HomeEffectType = {
    None = 0,
    EventBoxOpen = 1, --事件宝箱打开
    EventCombatGrid = 2, --事件战斗格子
    EventRewardGrid = 3, --事件奖励格子
    EventCardGrid = 4, --事件卡牌奖励格子
    EventCombat = 5, --事件战斗
    EventPlayerGrid = 6, --事件主角格子
    EventExpansionDone = 7, --事件区域解锁
    BuildingEffect = 8, --建筑中特效
    BuildOpenEffect = 9, --建筑完成特效
    CutOpenEffect = 10, --剪彩完成特效
    EventFightSuccessEffect = 11, --事件战斗成功
    NoviceModelShowEffect = 12, --新手模型显示
    NoviceMainFreeEffect = 13, --新手主城释放
    BuildFireEffect = 14, --建筑点燃特效
    NoviceMainFreeEffect2 = 15, --新手主城释放2
    EventCombatGridLight = 16, --事件战斗格子光柱
    EventRewardGridLight = 17, --事件奖励格子光柱
    EventPassLight = 18, --事件通关光柱
    BubbleEventBattle = 19, --气泡事件战斗
}

ATTR_TYPE = {
    HP = 1, -- 英雄生命固定值+n
    Atk = 2, -- 英雄攻击固定值+n
    Def = 3, -- 英雄防御固定值+n
    EnergyMax = 4, -- 英雄能量上限
    Speed = 5, -- 英雄速度
    CritRate = 6, -- 暴击+n%
    CritResist = 7, -- 暴击抵抗+n%
    CritDamage = 8, -- 暴击伤害+n%
    CritDamageResist = 9, -- 暴击伤害抵抗+n%
    GenericDamageIncrease = 10, -- 通用伤害增加+n%
    GenericDamageReduce = 11, -- 通用受到伤害降低+n%
    DamageIncreaseAgainstCounters = 12, -- 克制时造成伤害增加+n%
    DamageReductionAgainstCounters = 13, -- 克制时受到的伤害降低+n%
    DamageIncreaseAgainstMonsters = 14, -- 对怪物增伤+n%
    DamageReductionFromMonsters = 15, -- 受到怪物伤害降低+n%
    PhysicalDamageIncrease = 16, -- 物理增伤+n%
    PhysicalDamageReduction = 17, -- 受到物理伤害降低+n%
    EnergyDamageIncrease = 18, -- 能量增伤+n%
    EnergyDamageReduction = 19, -- 受到能量伤害降低+n%
    ForestHeroDamageIncrease = 20, -- 【森林英雄】造成的伤害提升+n%
    HumanHeroDamageIncrease = 21, -- 【人族英雄】造成的伤害提升+n%
    NightHeroDamageIncrease = 22, -- 【暗夜英雄】造成的伤害提升+n%
    GenericTroopCap = 23, -- 通用带兵量
    ForestHeroTroopCap = 24, -- 【森林英雄】带兵量+n
    HumanHeroTroopCap = 25, -- 【人族英雄】带兵量+n
    NightHeroTroopCap = 26, -- 【暗夜英雄】带兵量+n
    SkillDamageIncrease = 27, -- 技能伤害提升+n%
    EnergyRecoveryPerRound = 28, -- 英雄每回合能量回复
    DroneHP = 29, -- 无人机血量
    DroneAtk = 30, -- 无人机攻击
    DroneDef = 31, -- 无人机防御
    AbandonedLife = 32, -- 【废弃】生命
    AbandonedAtk = 33, -- 【废弃】攻击
    AbandonedArmor = 34, -- 【废弃】护甲
    AbandonedSpeed = 35, -- 【废弃】速度
    AbandonedCritDamageReduction = 36, -- 【废弃】暴击伤害减免
    InitialEnergy = 41, -- 初始能量
    AbandonedFactionCounter = 44, -- 【废弃】阵营克制
    AbandonedCritResist = 45, -- 【废弃】暴击抵抗
    AbandonedCritDamageResist = 46, -- 【废弃】暴伤抵抗
    AbandonedExtraFixedHP = 49, -- 【废弃】额外固定生命
    AbandonedExtraFixedAtk = 50, -- 【废弃】额外固定攻击
    AbandonedRedSubAttributeBreakthrough = 53, -- 【废弃】红色副属性破势百分比
    AbandonedRedSubAttributeBlock = 54, -- 【废弃】红色副属性招架百分比
    ExtraHP = 101, -- 英雄生命额外固定值+n
    ExtraAtk = 102, -- 英雄攻击额外固定值+n
    ExtraDef = 103, -- 英雄防御额外固定值+n
    ExtraForestHeroHP = 104, -- 【森林英雄】生命额外固定值+n
    ExtraForestHeroAtk = 105, -- 【森林英雄】攻击额外固定值+n
    ExtraForestHeroDef = 106, -- 【森林英雄】防御额外固定值+n
    ExtraHumanHeroHP = 107, -- 【人族英雄】生命额外固定值+n
    ExtraHumanHeroAtk = 108, -- 【人族英雄】攻击额外固定值+n
    ExtraHumanHeroDef = 109, -- 【人族英雄】防御额外固定值+n
    ExtraNightHeroHP = 110, -- 【暗夜英雄】生命额外固定值+n
    ExtraNightHeroAtk = 111, -- 【暗夜英雄】攻击额外固定值+n
    ExtraNightHeroDef = 112, -- 【暗夜英雄】防御额外固定值+n
    HPIncrease = 201, -- 英雄生命+n%
    AtkIncrease = 202, -- 英雄攻击+n%
    DefIncrease = 203, -- 英雄防御+n%
    ForestHeroHPIncrease = 204, -- 【森林英雄】生命+n%
    ForestHeroAtkIncrease = 205, -- 【森林英雄】攻击+n%
    ForestHeroDefIncrease = 206, -- 【森林英雄】防御+n%
    HumanHeroHPIncrease = 207, -- 【人族英雄】生命+n%
    HumanHeroAtkIncrease = 208, -- 【人族英雄】攻击+n%
    HumanHeroDefIncrease = 209, -- 【人族英雄】防御+n%
    NightHeroHPIncrease = 210, -- 【暗夜英雄】生命+n%
    NightHeroAtkIncrease = 211, -- 【暗夜英雄】攻击+n%
    NightHeroDefIncrease = 212, -- 【暗夜英雄】防御+n%
    GatheringHPIncrease = 213, -- 【参与集结时】生命提升n%
    GatheringAtkIncrease = 214, -- 【参与集结时】攻击力提升n%
    GatheringDefIncrease = 215, -- 【参与集结时】防御力提升n%
    GarrisonHPIncrease = 216, -- 【参与驻守时】生命提升n%
    GarrisonAtkIncrease = 217, -- 【参与驻守时】攻击力提升n%
    GarrisonDefIncrease = 218, -- 【参与驻守时】防御力提升n%
    AttackBaseHPIncrease = 219, -- 【攻击敌方基地时】生命值提升n%
    AttackBaseAtkIncrease = 220, -- 【攻击敌方基地时】攻击力提升n%
    AttackBaseDefIncrease = 221, -- 【攻击敌方基地时】防御力提升n%
    DefenseCityHPIncrease = 222, -- 【在城内防守时】生命值提升n%
    DefenseCityAtkIncrease = 223, -- 【在城内防守时】攻击力提升n%
    DefenseCityDefIncrease = 224, -- 【在城内防守时】防御力提升n%
    PullCartAttackIncrease = 225, -- 【拉车进攻时】提升生攻防n%
    PullCartDefenseIncrease = 226, -- 【拉车防御时】提升生攻防n%
    FoodOutputSpeedIncrease = 301, -- 粮食产出速度提升n%
    IronOreOutputSpeedIncrease = 302, -- 铁矿产出速度提升n%
    GoldOutputSpeedIncrease = 303, -- 金币产出速度提升n%
    FoodProtectionAmountIncrease = 304, -- 粮食保护量提升n%
    IronOreProtectionAmountIncrease = 305, -- 铁矿保护量提升n%
    GoldProtectionAmountIncrease = 306, -- 金币保护量提升n%
    IdleAccumulationTimeIncrease = 307, -- 挂机累积时间提升n
    IdleResourceOutputIncrease = 308, -- 挂机资源产出提升n%
    EquipmentFactoryManufactureSpeedUp = 309, -- 装备工厂制造加速n%
    EquipmentManufactureGoldCostReduction = 310, -- 装备制造减少金币消耗n%
    FreeHighLevelRecruitCDReduction = 311, -- 免费高级招募CD缩短n
    SurvivorFreeRecruitCDReduction = 312, -- 幸存者免费招募CD缩短n
    ExtraCampFarmlandIronGoldIncrease = 313, -- 获得额外兵营/农田/铁矿/金矿
    FoodProtectionAmount = 314, -- 粮食保护量n
    IronProtectionAmount = 315, -- 铁保护量n
    GoldProtectionAmount = 316, -- 金保护量n
    BarracksTroopCapIncrease = 401, -- 兵营训练士兵上限提升n%
    TrainingSoldierSpeedIncrease = 402, -- 训练士兵速度提升n%
    TrainingSoldierResourceConsumptionReduction = 403, -- 训练士兵的资源消耗降低n%
    HospitalCapacity = 404, -- 医院容量n
    HospitalCapacityIncrease = 405, -- 医院容量提升n%
    WoundedTreatmentSpeedIncrease = 406, -- 伤兵治疗速度提升n%
    TreatmentResourceConsumptionReduction = 407, -- 治疗消耗资源降低n%
    FieldCapacity = 408, -- 校场容量n
    FieldCapacityIncrease = 409, -- 校场容量提升n%
    SoldierHPIncrease = 410, -- 士兵生命值提升n
    SoldierAtkIncrease = 411, -- 士兵攻击力提升n
    SoldierDefIncrease = 412, -- 士兵防御力提升n
    SoldierHPIncreasePercent = 413, -- 士兵生命值提升n%
    SoldierAtkIncreasePercent = 414, -- 士兵攻击力提升n%
    SoldierDefIncreasePercent = 415, -- 士兵防御力提升n%
    SoldierMoraleIncrease = 416, -- 提升士兵士气n
    SoldierLoadIncrease = 417, -- 士兵的负重提升n
    UnlockTraining10LevelSoldiers = 418, -- 解锁训练10级士兵
    MaxTrainingSoldierCount = 419, -- 最大训练士兵数n
    AutoDispatchOfflineMembersToGathering = 501, -- 离线成员自动派遣队伍参加集结
    HelpCount = 502, -- 受帮助次数n
    ExtraTimeReductionPerHelp = 503, -- 每次帮助额外减少时间n
    BuildingSpeedUp = 504, -- 建造加速n%
    ResourceCostReductionForBuilding = 505, -- 建造消耗的资源减少n%
    FreeConstructionSpeedUpTime = 506, -- 建造免费加速时间n
    TechnologySpeedUp = 507, -- 科技加速n%
    ResourceCostReductionForTechnology = 508, -- 科技消耗的资源减少n%
    FreeTechnologySpeedUpTime = 509, -- 科技免费加速时间n
    MarchSpeedIncrease = 510, -- 行军速度提升n%
    ZombieMarchSpeedIncrease = 511, -- 【对丧尸行军】速度提升n%
    ResourceGatheringMarchSpeedIncrease = 512, -- 【对采集点行军】速度提升n%
    AttackCommanderMarchSpeedIncrease = 513, -- 【进攻其他指挥官】行军速度提升n%
    AttackCityMarchSpeedIncrease = 514, -- 【进攻城市时】行军速度提升n%
    GarrisonMarchSpeedIncrease = 515, -- 【进行驻守时】行军速度提升n%
    GatheringSpeed = 516, -- 采集速度n
    FoodGatheringSpeedIncrease = 517, -- 食物采集速度提升n%
    IronGatheringSpeedIncrease = 518, -- 铁矿采集速度提升n%
    GoldGatheringSpeedIncrease = 519, -- 金币采集速度提升n%
    LoadAttributeIncrease = 520, -- 负重属性提升n%
    SummaryHeroHP = 10001, -- 【汇总计算】英雄生命
    SummaryHeroAtk = 10002, -- 【汇总计算】英雄攻击
    SummaryHeroDef = 10003, -- 【汇总计算】英雄防御
}
--英雄属性模块
HERO_ATTR_MODULE = {
    All = 0, --所有的
    Level = 1, --等级
    Equip = 2, --装备
    Star = 3, --军衔
    Build = 4, --建筑
    UAV = 5, --无人机
    Honor = 6, --荣誉
    Official = 7, --官职
}
--英雄属性更行，抛事件用
EVENT_HERO_ATTR_MODULE_UPDATE = "EVENT_HERO_ATTR_MODULE_UPDATE"
--- Created by fgy.
--- DateTime: 2024/6/4 16:10
--- Des:固定通用常量定义

--拖拽类型
enDragType = {
    Normal = 1,
    _5v5 = 2,
}

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
--地图单位表头像气泡显示类型
EHomeCityBubbleIconType = {
    None = 0,
    Common = 1,
    Survivor = 2,
}

--家园额外建筑类型加成类型
EHomeExtraBuildingType = {
    [enBuildingType.enBuildingType_Farm] = enProductionProType.enProdPro_Extra_Farm,
    [enBuildingType.enBuildingType_Iron] = enProductionProType.enProdPro_Extra_Iron,
    [enBuildingType.enBuildingType_Gold] = enProductionProType.enProdPro_Extra_Gold,
    [enBuildingType.enBuildingType_Military] = enProductionProType.enProdPro_Extra_Barracks,
}
---@public 家园医院状态
EHomeHospitalState = {
    None = 0,
    --有伤兵
    HasSoldier = 1,
    --治疗中
    Healing = 2,
    --治疗中可以被帮助
    HealingCanHelp = 3,
    --治疗完成
    HealingFinish = 4,
}

---@field 沙盘实体对话框类型
SandEntityDialogType = {
    None = 0,
    HelpAlly = 1, --帮助盟友
    DemonCastle = 2, --恶魔城堡
}

---气泡移动到世界空间后的默认缩放
ScreenToWorldScale = 0.0165

--服务器更新队伍数据类型
-- 0仅阵容 1队伍所有数据
TEAM_DATA_TYPE = {
    Formation = 0,
    ALL = 1
}
--队伍行队类型
TEAM_LINE_TYPE = {
    Normal = 1,
    --侦察
    Scouting = 2,
}

--增援界面模型展示的相机距离
ReinforceCameraDistance = {
    [ESEntityResViewType.NeutralCity] = 7,
    [ESEntityResViewType.BigGun] = 3.5,
    [ESEntityResViewType.Camp] = 2,
    [ESEntityResViewType.StormBuild] = 6,
}