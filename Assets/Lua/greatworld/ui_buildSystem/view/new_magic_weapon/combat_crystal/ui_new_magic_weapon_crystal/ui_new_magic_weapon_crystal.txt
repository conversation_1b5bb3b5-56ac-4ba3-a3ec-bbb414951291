local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local typeof = typeof
local UIUtil = CS.Common_Util.UIUtil
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_new_magic_weapon_crystal_binding"
local crystal_item = require "crystal_item"
local gw_hero_mgr = require "gw_hero_mgr"
local ui_new_select_model_node = require "ui_new_select_model_node"
local new_weapon_combat_crystal_define = require "new_weapon_combat_crystal_define"
local log = require "log"
local ui_select_hero_model = require "ui_select_hero_model"
local FeatureType = new_weapon_combat_crystal_define.FeatureType
local SchemeIDEnum = new_weapon_combat_crystal_define.SchemeIDEnum
--region View Life
module("ui_new_magic_weapon_crystal")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.crystalItem = {}
    self.heroModels = {}
    self.typeTogList = {[SchemeIDEnum.Scheme1] = self.tog_1,[SchemeIDEnum.Scheme2] = self.tog_2,[SchemeIDEnum.Scheme3] = self.tog_3,[SchemeIDEnum.Scheme4] = self.tog_4 }
    self.crystalRoots = {[FeatureType.Start] = self.rtf_crystalRoot_1,[FeatureType.Attack]=self.rtf_crystalRoot_2,[FeatureType.Interrupt] = self.rtf_crystalRoot_3,[FeatureType.Defense] = self.rtf_crystalRoot_4}
    self.VData = {}
    self:SetActive(self.rImg_team,false)
    ui_new_select_model_node.InitBattleSelect(function (_rt)
        if self:IsValid() then
            self.modelNode = ui_new_select_model_node.GetHeroSelectModel()
            self.modelNode.gameObject:SetActive(true)
            self:SetActive(self.rImg_team,true)
            ui_new_select_model_node.GetSelectHeroBg().gameObject:SetActive(false)
            self.rImg_team.texture = _rt
            if self.pageData then
                self:RenderTeam(self.pageData.heroList,self.pageData.schemeData.teamIndex)
            end
        end

    end)
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:UpdateToggeUI(data) 
    for k,v in ipairs(self.typeTogList) do
        local scrollRectItem = v.gameObject:GetComponent(typeof(ScrollRectItem))
        if data[k] then
            self:SetActive(scrollRectItem:Get("lock"), not data[k].schemeData)
            scrollRectItem:Get("Text").text = tostring(data[k].planId)
            scrollRectItem:Get("Text_1").text = tostring(data[k].planId)
        end
    end
end 

function UIView:UpdatePageUI(data) 
    self.schemeData = data.schemeData
    local schemeData = data.schemeData
    self.pageData = data
    if schemeData then
            --四个结晶部件
            for place,starSid in ipairs(schemeData.starPlace) do
                self:RenderCrystalItem(self.crystalRoots[place], place, starSid,data.emptyCrystalClickEvent)
            end
            self:UpdateTeamUI(schemeData.teamIndex)
            --编队渲染
            self:RenderTeam(data.heroList,schemeData.teamIndex)
    else
        self.txt_lockTip1.text = string.format2(lang.Get(607126),tostring(data.unlockStage))
        self.txt_tip2.text = string.format2(lang.Get(607127),tostring(data.unlockLevel),tostring(data.unlockStage))
    end
    self:SetActive(self.rtf_teamRoot,schemeData and schemeData.teamIndex ~= 0)
    self:SetActive(self.rtf_noneTeam,schemeData and schemeData.teamIndex == 0)
    self:SetActive(self.rtf_crystalPage,schemeData ~= nil )
    self:SetActive(self.rtf_lockPage,not schemeData)
    self.tmp_power.text = tostring(data.power)
end

function UIView:UpdateTeamUI(teamIndex)
    local hasTeam = teamIndex and teamIndex~=0
    if hasTeam then
        local gw_power_mgr = require "gw_power_mgr"
        self.txt_teamDesc.text = lang.Get(601474)..teamIndex
        self.txt_teamPower.text = util.NumberWithUnit2(gw_power_mgr.GetTeamAllPower(teamIndex))
    end
    self:SetActive(self.rtf_teamRoot,hasTeam)
    self:SetActive(self.txt_teamDesc,hasTeam)
    self:SetActive(self.txt_teamPower,hasTeam)
end

function UIView:RenderTeam(heroList,teamIndex)
    if not heroList or util.IsObjNull(self.modelNode) then
        return
    end
    self.teamBg = ui_new_select_model_node.GetTeamBG()
    for i = 1, 4 do
        if self.teamBg[i] then
            UIUtil.SetActive(self.teamBg[i].gameObject, i == teamIndex)
        end
    end
    self.heroNode = ui_new_select_model_node.GetHeroNode()
    if not self.heroNode then
        return
    end

    for i = 0,4 do
        if heroList[i+1] then
            self.heroModels[i] = self:LoadHeroModelInfo(i,heroList[i+1],self.heroNode[i].transform)
        else
            if self.heroModels[i] then
                self.heroModels[i]:Dispose();
                self.heroModels[i] = nil
            end
        end
    end
    
end

function UIView:LoadHeroModelInfo(slot, heroData, parentTrs, callback)
    if not self.heroModels then
        return
    end
    if heroData.heroID == 125 then
        UIUtil.SetLocalScale(parentTrs,1,1,1)
    else
        UIUtil.SetLocalScale(parentTrs,-1,1,1)
    end
    local heroModel = self.heroModels[slot]
    --log.Error("Update")
    local id = heroData and heroData.heroID or 1
    local starLv = heroData.heroStar or 5
    local cfg = gw_hero_mgr.GetCfgByModelIdAndStarLv(id, starLv,heroData.skinID)
    if cfg == nil then
        log.Error("Update HeroModel Error:", id, starLv)
        return
    end
    local res = cfg.modelPath
    local model = heroModel and heroModel:Init(res, parentTrs, cfg.showChildModel) or ui_select_hero_model.CHeroModel():Init(res, parentTrs, cfg.showChildModel)
    model:SetInfo(res, function(go)
        if callback and window and window:IsValid() then
            local card = UIUtil.GetComponent(go.transform, typeof(Card))
            card:SetDissolve(0)
            callback(go)
        end
    end)
    return model
end


function UIView:RefreshTypeToggleList(toggleType)
    for k, v in pairs(self.typeTogList) do
        if toggleType == k then
            v.isOn = true
        else
            v.isOn = false
        end
    end
end

function UIView:RenderCrystalItem(crystalRoot,place,starSid,clickEvent) 
    local scroll_rect_item = crystalRoot:GetComponent(typeof(ScrollRectItem))
    local crystalRoot = scroll_rect_item:Get("crystalRoot")
    local emptyRoot = scroll_rect_item:Get("emptyRoot")
    self:SetActive(emptyRoot,not (starSid and starSid ~= 0))
    if starSid ~= 0 then 
        self.VData[place] = self.VData[place] or crystal_item.CCrystalItem():Init(crystalRoot,nil,2.1)
        self.VData[place]:SetCrystalInfo(starSid,nil,nil,nil,function()
            --打开升星简略弹窗
            local ui_window_mgr = require "ui_window_mgr"
            ui_window_mgr:ShowModule("ui_new_magic_weapon_crystal_upgrade_simple",nil,nil,{sid = starSid,openType = 2 })
        end)
        self.VData[place]:ShowFeatureTypeFlag(true)
        self.VData[place]:ShowStarRoot(true)
    else
        if self.VData[place] then
            self.VData[place]:Dispose()
            self.VData[place] = nil
        end
        local featureType = scroll_rect_item:Get("featureType")
        featureType:Switch(place-1)
    end

    scroll_rect_item.InvokeFunc = function(funcname)
        --注意  这里的事件是存在多种的clickItemEvent 
        if funcname == "addEvent" then
            local data = {
                place = place,
                oldStarSid = starSid or 0
            }
            clickEvent(data)
        end
    end
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.heroModels then
        for i, v in pairs(self.heroModels) do
            v:Dispose()
            v = nil
        end
    end
    if not util.IsObjNull(ui_new_select_model_node.GetHeroSelectModel()) then
        ui_new_select_model_node.GetHeroSelectModel().gameObject:SetActive(false)
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, true, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil, true, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
