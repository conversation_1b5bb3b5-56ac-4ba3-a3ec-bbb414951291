//#define UNITY_IOS
//#undef UNITY_EDITOR
//#define UNITY_ANDROID
//#undef UNITY_ANDROID
//#undef UNITY_IOS

#define ENABLE_LOG_FILE

using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif
using System.IO;
using System.Text;
using System.Threading;
using System.Collections;
using System.Collections.Generic;
using MiniJSON;
using ICSharpCode.SharpZipLib.Zip;
using War.Base;
using War.Script;
using System.Text.RegularExpressions;
using System;
using System.Runtime.InteropServices;
using WWW = UnityEngine.WWW;
//using WWW = War.Base.WWW;
using XLua;
using War.Common;
using UnityEngine.Networking;
using Object = System.Object;
using System.Reflection;
using LunarConsolePlugin;
using War.AOT;

[ReflectionUse]
public class AssetsUpdator : MonoBehaviour
{
#if UNITY_IOS
    [DllImport("__Internal")]
    private static extern void GotoAppStore(string url);
#endif

    public enum ErrCode
    {
        ELoadStreamAssetsVersion, // 加载本地StreamAsset里面的版本信息失败
        ERequestRemoteAssetVersion, // 请求远程版本信息失败
        ERequestPatchList, // 请求patch列表失败
        EDownloadPatch, // 下载patch超时或超过重试次数
        EReinstall, // 需要重装, 不能热更
        EUpdateServerInfo, // 更新服务器信息错误
        EShowAppStoreUI, //跳转应用商店界面
    }

    public enum Status
    {
        None,
        LoadingLocalVersionInfo,
        RequestingRemoteVersionInfo,
        RequestingPatchInfo,
        DownloadingPatch,
        WaitingForExtraction,
        UpdateServerInfo,
    }

#if UNITY_EDITOR
    const string kSimulateAssetBundleUpdateMenu = "AssetBundles/Simulate Assets Update";

    [MenuItem(kSimulateAssetBundleUpdateMenu)]
    public static bool ToggleSimulateAssetBundleUpdate()
    {
        SimulateAssetBundlesUpdateInEditor = !SimulateAssetBundlesUpdateInEditor;
        Menu.SetChecked(kSimulateAssetBundleUpdateMenu, SimulateAssetBundlesUpdateInEditor);
        return true;
    }

    protected static int ms_SimulateAssetBundlesUpdate = -1;
    const string kSimulateAssetBundles = "SimulateAssetBundlesUpdate";

    public static bool SimulateAssetBundlesUpdateInEditor
    {
        get
        {
            if (ms_SimulateAssetBundlesUpdate == -1)
                ms_SimulateAssetBundlesUpdate = EditorPrefs.GetBool(kSimulateAssetBundles, false) ? 1 : 0;

            return ms_SimulateAssetBundlesUpdate != 0;
        }
        set
        {
            int newValue = value ? 1 : 0;
            if (newValue != ms_SimulateAssetBundlesUpdate)
            {
                ms_SimulateAssetBundlesUpdate = newValue;
                EditorPrefs.SetBool(kSimulateAssetBundles, value);
            }
        }
    }


    public static bool SimulateAssetBundleInEditor
    {
        get
        {
            PropertyInfo property = typeof(AssetBundleManager).GetProperty("SimulateAssetBundleInEditor",
                BindingFlags.Public | BindingFlags.Static);
            if (property != null)
            {
                bool simulate = (bool)property.GetValue(null, null);
                return simulate;
            }

            return false;
        }
    }
#endif

    //public static string UPDATE_CONFIG = "update.json";
    protected const int TRUNK_SIZE = 2048;
    protected const int DOWNLOAD_RETRY_TIMES = 3; // 下载失败重试次数
    protected const float DOWNLOAD_RETRY_INTERVAL = 3; // 重试间隔

    private static bool ms_IsReady = false;

    public static bool IsReady
    {
        get { return ms_IsReady; }
    }

    //设置完是否审核的回调，给lua用
    public static Action onSetReviewFinish;

    public static Action onSetReviewFinishCsharp;

    //是否设置完审核变量
    public static bool isSetReview;

    private int iReportFilesFinish;

    class ZipData
    {
        public byte[] Data;
        public System.Action callBack;
    }

    protected Thread m_ThreadUnpack;
    protected Queue m_QueueToUnpack = new Queue();


    private static Status currStatus;

    public static Status CurrStatus
    {
        get { return currStatus; }
        private set
        {
            currStatus = value;
            if (OnStatusChanged != null)
            {
                OnStatusChanged(value);
            }
        }
    }

    public delegate void UpdateProgress(int current, int total);

    public static UpdateProgress OnProgress;

    public static System.Action<string> StartDownloadApk;
    public static UpdateProgress OnDownload; // 下载apk进度

    public static System.Action<bool> IsUpdateDll;

    public delegate void ErrorCallback(ErrCode code, string message = null);

    public static ErrorCallback OnError;

    public delegate void UpdateCompleteCallback();

    public static UpdateCompleteCallback OnCompleted;

    public delegate void StatusChangedCallback(Status curr);

    public static StatusChangedCallback OnStatusChanged;

    public static Dictionary<string, object> UpdateConfig;
    public static Dictionary<string, object> ApkUpdateConfig;
    public static Dictionary<string, string> UrlConfig;
    public static Dictionary<string, object> ExtParams;

    public static Dictionary<string, object> local_config = new Dictionary<string, object>();

    public static Action<string> OnUpdateDriverNodeUrl;
    public static Action OnInitDynamicUrl;

    public static bool s_IsReviewing = false;
    public static bool s_last_IsReviewing = false;

    private string ChannelStr = "0";

    static bool first = true;

    //是否在 LUA 中触发 apk 强更，否则使用旧逻辑，在 c# 中强更
    public static bool enableLuaApk = false;
    public bool shouldInstallApk = false;

    //是否同意隐私协议
    public static bool isAgreePri = false;

    public static string PACKAGE_JSON = "package.json";
    private static bool isGarbleUpdateJson = false;
    private static bool isGarblePackageJson = false;

    private static string hashRemoteParseJson = ""; //没有被剔除资源信息的完整hashRemoteJson

    //是否使用包内update.json
    private static bool isUseLocalUpdateJson = false;
    private static bool usedLocalUpdateJson = false; //是否使用了本地update.json
    private Dictionary<string, object> operationConfig = new Dictionary<string, object>();

    // 数数新增部分事件上报的开关
    public static bool isOpenReportEvent = true;

    private static Dictionary<String, Font> fontWithinSceneDict = null;

    void Awake()
    {
        System.Net.ServicePointManager.DefaultConnectionLimit = 5;
        //Instance = this;
        War.Base.AssetBundleManager.trackEvent += TrackEventHandler;
        War.Base.AssetLoader.trackEvent += TrackEventHandler;
        operationConfig = new Dictionary<string, Object>();

        TrackEvent("assetsUpdator_Awake", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson);

        var _ABPathKeyIns = ABPathKey.Instance;
        if (_ABPathKeyIns != null)
        {
            TrackEvent("assetsUpdator_ABPathKeyIns_start", new Dictionary<string, object>()
            {
            }, JsonModule.NewtonsoftJson, isOpenReportEvent);
            string key = _ABPathKeyIns.Get("update.json");
            Debug.Log($"AssetsUpdator update.json to {key}");
            if (!string.IsNullOrEmpty(key))
            {
                AssetBundleManager.UPDATE_CONFIG = key;
                isGarbleUpdateJson = true;
                Debug.Log($"AssetsUpdator update.json GarbleAb key {key}");
            }
            else
            {
                isGarbleUpdateJson = false;
                Debug.Log("AssetsUpdator GarbleAb not find update.json");
            }

            string packageKey = _ABPathKeyIns.Get(PACKAGE_JSON);
            if (string.IsNullOrEmpty(packageKey) == false)
            {
                PACKAGE_JSON = packageKey;
                isGarblePackageJson = true;
                Debug.Log($"AssetsUpdator PACKAGE_JSON chang GarbleAb key{key}");
            }
            else
            {
                isGarblePackageJson = false;
                Debug.Log("AssetsUpdator GarbleAb not find package.json");
            }

            TrackEvent("assetsUpdator_ABPathKeyIns_end", new Dictionary<string, object>()
            {
            }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        }
        else
        {
            Debug.Log("AssetsUpdator  not use GarbleAb");
        }

        // 防止包体内不包含 Adjust，此处主动进行一次 AssetLoadCfg.LoadChannelCfg 初始化
        //同步位面海外修改
        bool isEnableGoogle = GameConfig.Instance().TryGetBool("ENABLE_GOOGLE", false);
        if (isEnableGoogle)
        {
            AssetLoadCfg.LoadChannelCfg.LoadConfig();
        }

        //协程检测是否同意隐私协议
        StartCoroutine(CheckAgreePri());

        //请求ISOCode
        StartCoroutine(ReqISOCode());
    }

    /// <summary>
    /// 根据字符串获取布尔值，默认值为true,只有外网才生效
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static bool GetDefaultTrueByStr(Object key)
    {
        if (key == null)
        {
            return false;
        }

        bool isDebug = GameConfig.Instance().TryGetBool("ENABLE_Q1_DEBUG_MODE");
        if (!isDebug)
        {
            return key.ToString() != "false";
        }

        return key.ToString() == "true";
    }

    public static Font GetSceneFont(String fontName)
    {
        if (String.IsNullOrEmpty(fontName))
        {
            return null;
        }

        if (null == fontWithinSceneDict)
        {
            return null;
        }

        if (!fontWithinSceneDict.ContainsKey(fontName))
        {
            return null;
        }

        return fontWithinSceneDict[fontName];
    }

    public void FindAllFontInScene()
    {
        GameObject goFontsRegistered = GameObject.Find("FontsRegistered");
        if (null == goFontsRegistered)
        {
            Debug.LogWarning(
                $"[LAUNCH]There is no gameObject with name \"FontsRegistered\", check the scene and try again.");
            return;
        }

        UnityEngine.UI.Text[] comTexts = goFontsRegistered.GetComponentsInChildren<UnityEngine.UI.Text>(true);
        if (null == comTexts || 0 >= comTexts.Length)
        {
            Debug.LogWarning(
                $"[LAUNCH]There is no texts within the FontsRegistered gameObject, check the gameObject and try again. {(null != comTexts ? comTexts.Length : -1)}");
            return;
        }

        if (null == fontWithinSceneDict)
        {
            fontWithinSceneDict = new Dictionary<String, Font>();
        }

        for (Int32 i = 0, imax = comTexts.Length; i < imax; i++)
        {
            if (null == comTexts[i] || null == comTexts[i].font)
            {
                Debug.Log($"[LAUNCH]Font is null {(null != comTexts[i] ? comTexts[i].name : null)}");
                continue;
            }

            if (fontWithinSceneDict.ContainsKey(comTexts[i].font.name))
            {
                Debug.Log(
                    $"[LAUNCH]Refresh font {comTexts[i].font.name}-{(fontWithinSceneDict[comTexts[i].font.name] == comTexts[i].font)}");
                fontWithinSceneDict[comTexts[i].font.name] = comTexts[i].font;
            }
            else
            {
                fontWithinSceneDict.Add(comTexts[i].font.name, comTexts[i].font);
                Debug.Log($"[LAUNCH]Add font {comTexts[i].font.name}");
            }
        }
    }

    //设置reskey
    public static void SetHookResKey()
    {
        if (string.IsNullOrEmpty(Config.HookResKey) == false)
        {
            ApkUpdateConfig["res_key"] = Config.HookResKey;
        }
    }

    public static string ISOReqStr = "";
    string ISOUrl = "https://api-ea.q1.com/ip/api/ips/selfcountry";

    IEnumerator ReqISOCode()
    {
        TrackEvent("assetsUpdator_ReqISOCode_start", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        UnityWebRequest www = UnityWebRequest.Get(ISOUrl);
        www.timeout = 3;
        var reqTime = Time.realtimeSinceStartup;
        Debug.Log("ReqISOCode  Time.realtimeSinceStartup:" + reqTime);
        yield return www.Send();
        if (www.error == null)
        {
            ISOReqStr = www.downloadHandler.text;
            Debug.Log("ReqISOCode ISOReqStr:" + ISOReqStr + "  Time.realtimeSinceStartup:" + Time.realtimeSinceStartup +
                      " total:" + (Time.realtimeSinceStartup - reqTime));
            TrackEvent("assetsUpdator_ReqISOCode_end", new Dictionary<string, object>()
            {
                { "step", "1" },
            }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        }
        else
        {
            Debug.Log("ReqISOCode Error :" + www.error);
            TrackEvent("assetsUpdator_ReqISOCode_end", new Dictionary<string, object>()
            {
                { "step", "2" },
            }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        }
    }


    IEnumerator CheckAgreePri()
    {
        TrackEvent("assetsUpdator_CheckAgreePri_start", new Dictionary<string, object>()
        {
            { "step", "1" },
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        while (true)
        {
            //海外没有是否同意隐私协议，默认都是为true
            if (Q1.Q1SDK.Instance == null)
            {
                //Debug.LogError("editor 平台直接初始化");
                TrackEvent("assetsUpdator_CheckAgreePri_start", new Dictionary<string, object>()
                {
                    { "step", "2" },
                }, JsonModule.NewtonsoftJson, isOpenReportEvent);
                Init();
                yield break;
            }
            // else if(Q1.Q1SDK.Instance.GetIsAgreePri())
            // {
            //     //Debug.LogError("移动平台 点击同意！！");
            //     isAgreePri = true;
            //     Init();
            //     yield break;
            // }
            else
            {
                //Debug.LogError("移动平台 未同意协议！！");
                TrackEvent("assetsUpdator_CheckAgreePri_start", new Dictionary<string, object>()
                {
                    { "step", "3" },
                }, JsonModule.NewtonsoftJson, isOpenReportEvent);
                Init();
                yield break;
            }

            yield return null;
        }
    }

    void LogCallback(string info)
    {
        War.Base.LogDebug.Instance.HandleLog(info, "", LogType.Log);
    }

    public void InitLogCat(Dictionary<string, object> gameConfig)
    {
        //if (gameConfig.TryGetValue("ENABLE_LOGCAT_CATCH", out var ENABLE_LOGCAT_CATCH))
        {
            //"".Print("ENABLE_LOGCAT_CATCH", ENABLE_LOGCAT_CATCH.ToString().ToLower());
            //if (ENABLE_LOGCAT_CATCH.ToString().ToLower() == "true")
            {
                if (com.an.logcat.LogcatManager.Instance.isEnable())
                {
                    Debug.LogError("isEnable:" + com.an.logcat.LogcatManager.Instance.isEnable());
                    Application.logMessageReceivedThreaded -= War.Base.LogDebug.Instance.HandleLog;
                    com.an.logcat.LogcatManager.Instance.logMessageReceivedThreaded -= LogCallback;
                    com.an.logcat.LogcatManager.Instance.logMessageReceivedThreaded += LogCallback;
                    com.an.logcat.LogcatManager.Instance.Start();
                    var id = System.Diagnostics.Process.GetCurrentProcess().Id;
                    "".Print("ProcessID", id);
                    com.an.logcat.LogcatManager.Instance.AddPattern(id.ToString());
                }
            }
        }
    }

    void Init()
    {
        TrackEvent("assetsUpdator_init_start", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);

#if ENABLE_LOG_FILE
        Application.logMessageReceivedThreaded -= War.Base.LogDebug.Instance.HandleLog;
        Application.logMessageReceivedThreaded += War.Base.LogDebug.Instance.HandleLog;
        LogDebug.Instance.EnableLogFile = true;
        LogDebug.Instance.MAX_CACHED_COUNT = 3000;
#endif
        TrackEvent("start_load_StreamingAssets_Bg", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);


        FindAllFontInScene();

        StartCoroutine(loadStreamingAssetsBg("updatehero1.png", "StartupCanvas/Bg/Model/UILoginBg/imgLoginBg"));
#if UNITY_IOS
        //目前安卓平板没有用updateheroWidth.png，只有IOS ipad会用宽屏updateheroWidth.png
        StartCoroutine(loadStreamingAssetsBg("updateheroWidth.png","StartupCanvas/Bg/Model/UILoginBg/imgLoginBgTablet"));
#endif
        //var gameConfig = GetGameConfig();
        //TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        ChannelStr = GameConfig.Instance().TryGetString("CHANNEL_ID");
        "".Print("ChannelStr", ChannelStr);
        bool objLuaApk;
        enableLuaApk = GameConfig.Instance().TryGetBool("ENABLE_LUA_APK");

        //InitLogCat(gameConfig);

        Log("enbale lua apk:" + enableLuaApk);

        bool httpsocket2ReWWW = false;
        // object objHttpsocket2ReWWW;
        // if (gameConfig.TryGetValue("ENABLE_HTTPSOCKET_2_REWWW", out objHttpsocket2ReWWW))
        // {
        httpsocket2ReWWW = GameConfig.Instance().TryGetBool("ENABLE_HTTPSOCKET_2_REWWW");
        // }
        AssetBundleManager.s_IsEnableHttpsocket2Rewww = httpsocket2ReWWW;
        Log("enbale httpsocket2ReWWW:" + httpsocket2ReWWW);
        ;

        //Caching.ClearCache();
        ms_IsReady = false;
        //UnityEngine.Random.InitState(System.DateTime.Now.Millisecond);

        QualitySettings.vSyncCount = 0;
        Application.targetFrameRate = 300;

        //AssetBundleManager.DNS_IND = isCn() ? 0 : 1;

        // Log(textAsset.text);
        Log(Application.version);

        if (first == false)
            return;

        //Log("Unity Profiler Attach Begin");
        TrackEvent("Unity_Profiler_Attach_Begin", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        first = false;
        War.Script.Utility.OnBeginSample += (str) =>
        {
            UnityEngine.Profiling.Profiler.BeginSample(str);
            // UWAEngine.PushSample(str);
        };
        War.Script.Utility.OnEndSample += () =>
        {
            // UWAEngine.PopSample();
            UnityEngine.Profiling.Profiler.EndSample();
        };
        TrackEvent("Unity_Profiler_Attach_End", new Dictionary<string, object>()
        {
        }, JsonModule.NewtonsoftJson, isOpenReportEvent);
        //Log("Unity Profiler Attach End");

        //执行后续初始化，原Start()
        StartCoroutine(InitStart());
    }

    IEnumerator CheckUpdateFileTxt()
    {
        while (true)
        {
            AssetsSync.Instance.UpdateFileTxt(hashPersistent);
            yield return null;
        }
    }


    float startTime;
    float loopTimes;

    IEnumerator InitStart()
    {
        Debug.Log("AssetsUpdator Start");
        //War.Base.AssetBundleManager.trackEvent += TrackEventHandler;
        //War.Base.AssetLoader.trackEvent += TrackEventHandler;

        startTime = Time.realtimeSinceStartup;
        loopTimes = 0;
        Debug.LogErrorFormat(" InitStart = {0}", "开始了");
        if (Q1.Q1SDK.Instance != null)
        {
            Debug.LogError("Q1.Q1SDK.Instance");
            System.Text.StringBuilder str = new System.Text.StringBuilder();
            //str.Append("{\"since_start_time\":");
            //str.Append((Time.time).ToString());
            //str.Append(",\"system_memory_size\":");
            //str.Append(SystemInfo.systemMemorySize);
            //str.Append("}");
            //Q1.Q1SDK.Instance.TrackEvent("asset_updator_start", str.ToString());
            //var gameConfig = GetGameConfig();

            var last_role_id = PlayerPrefs.GetFloat("last_role_id", -1);
            TrackEvent("asset_updator_start", new Dictionary<string, object>()
            {
                { "system_memory_size", SystemInfo.systemMemorySize },
                { "last_role_id", (double)last_role_id },
                { "last_svn_version", GameConfig.Instance().TryGetString("BRANCH_VERSION") },
            }, JsonModule.LitJson);

            StartEvent("updateBegin",
                "ver=" + System.DateTime.Now.ToString("yyyy-MM-dd.HH-mm-ss-ff",
                    System.Globalization.DateTimeFormatInfo.InvariantInfo));

            Q1.Q1SDK.Instance.ChannelSDKReport("sdk_page_game_start", "");
            str.Remove(0, str.Length);
            str.Append("{\"system_memory_size\":");
            str.Append(SystemInfo.systemMemorySize);
            str.Append("}");
            Q1.Q1SDK.Instance.SetSuperProperties(str.ToString());

            Debug.LogErrorFormat(" InitStart = {0}", "加载动态url域名配置");
        }

#if UNITY_EDITOR

        "".Print("SimulateAssetBundleInEditor", SimulateAssetBundleInEditor);
        if (SimulateAssetBundleInEditor)
        {
            yield return null;
            yield return OnUpdateCompleted();
        }
        else
#endif

        {
            StartCoroutine(CheckUpdateFileTxt());

            while (UpdateConfig == null)
            {
                yield return Load();
                loopTimes += 1;
                if (UpdateConfig == null)
                {
                    yield return new WaitForSeconds(2);
                    AssetBundleManager.NEXT_DNS();
                }
            }

            yield return UpdateRes();
            Log("Starting update");
        }


        //if (Q1.Q1SDK.Instance != null)
        //{
        //    System.Text.StringBuilder str = new System.Text.StringBuilder();
        //    str.Append("{\"since_start_time\":");
        //    str.Append((Time.time).ToString());
        //    str.Append(",\"consume\":");
        //    str.Append((Time.time - startTime).ToString());
        //    str.Append(",\"loop_times\":");
        //    str.Append(loopTimes.ToString());
        //    str.Append("}");
        //    Q1.Q1SDK.Instance.TrackEvent("asset_updator_end", str.ToString());

        //}
        StopAllCoroutines();
    }

    public void SetCopyStreamingasset()
    {
        //默认情况下domain的配置只有update会写到assetsUpdateInfo中，其他需要自己添加
        bool p_copy_streamingasset_out = GetConfigByDomainOrLocal("p_copy_streamingasset_out").ToString() == "true";
        "".Print("p_copy_streamingasset_out", p_copy_streamingasset_out);
        Config.SetConfig("p_copy_streamingasset_out", p_copy_streamingasset_out.ToString());
    }

    public int GetRwVer()
    {
        int ver = 0;
        //var strNumber = GetConfigByDomainOrLocal("vision");
        try
        {
            if (assetsUpdateInfo.ContainsKey("vision"))
            {
                ver = Convert.ToInt32(assetsUpdateInfo["vision"].ToString());
            }
            else
                Debug.Log($"UpdateConfig cannot find key vision");
        }
        catch (FormatException e)
        {
            Debug.Log($"UpdateConfig vision form exception:{e.ToString()}");
        }

        Debug.Log($"UpdateConfig key vision:{ver}");
        return ver;
    }

    public static void StartEvent(string action, string msg)
    {
        if (Q1.Q1SDK.Instance != null)
        {
            Q1.Q1SDK.Instance.StartEvent(action, msg);
        }
    }

    // private static Dictionary<string, object> GetGameConfig()
    // {
    //     TextAsset textAsset = Resources.Load<TextAsset>("game_config");
    //     var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
    //     return gameConfig;
    // }

    public static void TrackEvent(string ev, Dictionary<string, object> dic = null,
        JsonModule jsonModule = JsonModule.NewtonsoftJson, bool isReportThinkingEvent = true)
    {
        /*if (dic != null)
        {
            dic["since_start_time"] = Time.realtimeSinceStartup;
        }
        if (Q1.Q1SDK.Instance!=null)
        {
            Q1.Q1SDK.Instance.TrackEvent(ev, properties);
        }*/
        //string properties = AssetBundleManager.ToJson(dic);
        //Debug.LogWarning("TrackEvent:" + ev + properties);
        if (isReportThinkingEvent)
        {
            AssetBundleManager.TrackEvent(ev, dic, jsonModule);
        }
    }

    // 2021/3/6 修改为 public 访问权限
    public Dictionary<string, object> assetsUpdateInfo = null;
    long assetsVersion;
    static bool isSkipDownloadUpdateJson = false;

    static SystemLanguage[] CN_LANGS = new SystemLanguage[]
    {
        SystemLanguage.Chinese,
        SystemLanguage.ChineseSimplified,
        SystemLanguage.ChineseTraditional,
    };

    bool isCn()
    {
        var dl = Application.systemLanguage;
        //dl = SystemLanguage.Afrikaans;
        foreach (var l in CN_LANGS)
        {
            if (dl == l)
            {
                return true;
            }
        }

        return false;
    }

    //当换资源路径的时候，需要清理旧的资源，避免使用旧资源的老版本进游戏
    public void DeleteAllOldRes()
    {
        int clearOldResVer = 0;
        int cacheClearOldResVer = PlayerPrefs.GetInt("clear_old_res_ver", 0);
        string strClearOldResVer = GetConfigByDomainOrLocal("p_clear_old_res_ver").ToString();
        int.TryParse(strClearOldResVer, out clearOldResVer);
        if (clearOldResVer > 0 && clearOldResVer > cacheClearOldResVer)
        {
            BgDownloadFileMgr.InitCachingFileMgr();
            string rootPath = BgDownloadFileMgr.rootPath;
            var files = Directory.GetFiles(rootPath, "hashRemoteVirtualFile*.txt", SearchOption.TopDirectoryOnly);
            foreach (var file in files)
            {
                File.Delete(file);
                Debug.LogWarning("Delete old hashRemoteVirtualFile:" + file);
            }

            PlayerPrefs.SetInt("MaxHashRemoteVirtualVersion", 0);
            PlayerPrefs.SetInt("PreloadVirtualVersion", 0);
            PlayerPrefs.SetInt("clear_old_res_ver", clearOldResVer);
        }
    }

    string GetStreamingPath(string assetPath)
    {
        
#if !UNITY_ANDROID
        assetPath = "file://" + assetPath;
#endif
        return assetPath;
    }

    bool LoadStreamingByAndroidAssetLoader(string assetPath, ref byte[] content)
    {
         bool isSucceed = false;
#if UNITY_ANDROID 
        var m_StreamingAssetsPath = BaseLoader.GetStreamAssetRelativePath(); 
        var m_StreamingPathLength = m_StreamingAssetsPath.Length + 1;
        assetPath = assetPath.Substring(m_StreamingPathLength);
        try
            {
                using (var player = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
                using (var activity = player.GetStatic<AndroidJavaObject>("currentActivity"))
                {
                    var _manager = activity.Call<AndroidJavaObject>("getAssets");
                    AndroidAssetLoader.SetupAssetManager(_manager.GetRawObject(), Application.dataPath);
                }
            int assetSize = 0;
            assetSize = AndroidAssetLoader.GetStreamAssetLength(assetPath);
            content = new byte[assetSize];
            AndroidAssetLoader.GetStreamAssetContent(assetPath, content, assetSize);
            isSucceed = true;
            }
            catch(Exception ex)
            {
              if(ex!=null)
                {
                    Debug.LogError("LoadStreamingByAndroidAssetLoader Error::" + ex.ToString());
                }
                
            }
#endif

        return isSucceed;
    }



    bool LoadStreamingByWWW( WWW assetsUpdateWWW, string assetPath,bool isGarble, ref string localJson)
    {
        var subPath = assetPath.Substring(assetPath.LastIndexOf('/')+1);
        byte[] bytes = assetsUpdateWWW.bytes;
            if (assetsUpdateWWW.error != null || PlayerPrefs.GetInt("test_load_streaming_error", 0) == 1)
            {
                LogError("Load:" + assetsUpdateWWW.url + ",error:" + assetsUpdateWWW.error);
                RaiseError(ErrCode.ELoadStreamAssetsVersion, assetsUpdateWWW.error);
                var isSucceed =
                    LoadStreamingByAndroidAssetLoader(
                        assetPath, ref bytes);
                if (!isSucceed)
                {
                  
                    TrackEvent("LoadStreamingByAndroidAssetLoader", new Dictionary<string, object>
                    {
                        { "assetPath", assetPath }, 
                        {"isGarble", isGarble },
                        { "isSucceed", false }, 
                        { "error", assetsUpdateWWW.error }
                    });
                    return true;
                }
                var json = Encoding.ASCII.GetString(bytes);
                TrackEvent("LoadStreamingByAndroidAssetLoader", new Dictionary<string, object>
                {
                    { "assetPath", assetPath }, 
                    { "isSucceed", true },
                    {"isGarble", isGarble },
                    { "error", assetsUpdateWWW.error },
                    { "json", json }
                });
                
            }

            if (isGarble)
            {
                bytes = Aes.Decryptor(bytes, AssetBundleManager.UpdateConfigEncryptKey,
                    AlgorithmUtils.HashUtf8MD5(subPath).Substring(8, 16));
                localJson = Encoding.Unicode.GetString(bytes);
                Debug.Log("AssetsUpdator.Load update.json decryptor done. json:" + localJson);
            }
            else
            {
                localJson = Encoding.ASCII.GetString(bytes);
            }

            var updateAssert = (Dictionary<string, object>)Json.Deserialize(localJson);

            try
            {
                foreach (var keyValuePair in updateAssert)
                {
                    assetsUpdateInfo[keyValuePair.Key] = keyValuePair.Value;
                }

                TrackEvent("load_inclusion_json_done", new Dictionary<string, object>()
                {
                    {"assetPath", assetPath},
                }, JsonModule.NewtonsoftJson, isOpenReportEvent);
            }
            catch (Exception e)
            {
                LogError(e);
            }

            return false;
    }




    IEnumerator Load()
    {
        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");
        Log("new AssetsUpdater : basePersistentUrl" + basePersistentUrl + "basePersistentPath:" +
            basePersistentPath);
        //m_ThreadUnpack = new Thread(new ParameterizedThreadStart(StartUnpackToPersistent));
        //m_ThreadUnpack.IsBackground = true;
        //m_ThreadUnpack.Start(basePersistentPath);
        Log("Updator:StartCheckLoad");

        assetsUpdateInfo = new Dictionary<string, object>();
        
        string localPackageJson = null;
        string localUpdatejson = null;
        var streamPackagePath   = BaseLoader.GetStreamingAssetsPath() + PACKAGE_JSON;
        using (var assetsPackageWWW = new WWW(GetStreamingPath(streamPackagePath) ))
        {
            //Log("load package");
            yield return assetsPackageWWW;
            var needBreak = LoadStreamingByWWW(assetsPackageWWW, streamPackagePath, isGarblePackageJson, ref localPackageJson);
        }

        #region 包体内部的update.json

        var streamUpdatePath = BaseLoader.GetStreamingAssetsPath() + AssetBundleManager.UPDATE_CONFIG;
        using (var assetsUpdateWWW =
               new WWW(GetStreamingPath(streamUpdatePath)))
        {
            CurrStatus = Status.LoadingLocalVersionInfo;
            yield return assetsUpdateWWW;
            var needBreak = LoadStreamingByWWW(assetsUpdateWWW, streamUpdatePath, isGarbleUpdateJson, ref localUpdatejson);
            if (needBreak)
            {
                yield break;
            }

            TrackEvent("getExtParam_start", new Dictionary<string, object>()
            {
            });

            #region 处理extparams

            bool isGetExtParams = false;
            int tryGetExtTime = 0;
            while (!isGetExtParams && tryGetExtTime < 30)
            {
                if (Q1.Q1SDK.Instance != null)
                {
                    Q1.Q1SDK.Instance.GetExtParams((str) =>
                    {
                        try
                        {
                            // Log("ExtParams:" + str);
                            if (!string.IsNullOrEmpty(str))
                            {
                                if (str == "not support")
                                {
                                    // 不支持获取额外参数
                                    ExtParams = new Dictionary<string, object>();
                                    isGetExtParams = true;
                                    return;
                                }

                                OnUpdateDriverNodeUrl?.Invoke(str);
                                var dic = (Dictionary<string, object>)MiniJSON.Json.Deserialize(str);
                                if (dic != null)
                                {
                                    object extConfigStr;
                                    dic.TryGetValue("extConfig", out extConfigStr);
                                    foreach (var data in dic)
                                    {
                                        var key = data.Key.ToString();
                                        var value = data.Value == null ? "" : data.Value.ToString();
                                        if (ExtParams == null)
                                        {
                                            ExtParams = new Dictionary<string, object>();
                                        }

                                        ExtParams[key] = value;
                                    }

                                    ExtParams["jsonStr"] = str;
                                    //var extData = (List<object>)MiniJSON.Json.Deserialize(extConfigStr.ToString());
                                    var extData = extConfigStr as List<object>;
                                    if (extData != null && extData.Count > 0)
                                    {
                                        foreach (Dictionary<string, object> data in extData)
                                        {
                                            object key;
                                            data.TryGetValue("Key", out key);
                                            object value;
                                            data.TryGetValue("Value", out value);
                                            ExtParams[key.ToString()] = value;
                                            if (key.ToString() == "operationConfig")
                                            {
                                                "".print("AssetsUpdator ExtParams operationConfig");
                                                if (operationConfig.Count > 0)
                                                    operationConfig.Clear();
                                                operationConfig = AssetBundleManager.ToObj<Dictionary<string, object>>(value.ToString());

                                                "".print("AssetsUpdator ExtParams operationConfig end");
                                            }
                                            if (key.ToString() == "update")
                                            {
                                                //仅替换update json内容
                                                var valueList = (Dictionary<string, object>)MiniJSON.Json.Deserialize(value.ToString());
                                                if (valueList != null && valueList.Count > 0)
                                                {
                                                    List<string> dontReplaceKeys = new List<string>();
                                                    #region 替换jekins上指定update.json
                                                    object use_specific_updatejson;
                                                    assetsUpdateInfo.TryGetValue("use_specific_updatejson", out use_specific_updatejson);

                                                    if (use_specific_updatejson != null && (bool)use_specific_updatejson == true)
                                                    {
                                                        // 替换指定update.json
                                                        object specific_updatejson;
                                                        assetsUpdateInfo.TryGetValue("specific_updatejson", out specific_updatejson);
                                                        if (specific_updatejson != null)
                                                        {
                                                            var cacheArray = specific_updatejson.ToString().Split(',');
                                                            for (int i = 0; i < cacheArray.Length; i++)
                                                            {
                                                                if (!string.IsNullOrEmpty(cacheArray[i]))
                                                                    dontReplaceKeys.Add(cacheArray[i]);
                                                            }
                                                        }
                                                    }

                                                    #endregion

                                                    bool isReplaceKey = true;
                                                    if (dontReplaceKeys != null && dontReplaceKeys.Count > 0)
                                                        isReplaceKey = false;
                                                    foreach (var obj in valueList)
                                                    {
                                                        if (assetsUpdateInfo != null)
                                                        {
                                                            bool isSkip = false;
                                                            if (!isReplaceKey)
                                                                isSkip = (dontReplaceKeys.Contains(obj.Key));
                                                            if (!isSkip)
                                                            {
                                                                if (assetsUpdateInfo.ContainsKey(obj.Key))
                                                                {
                                                                    assetsUpdateInfo[obj.Key] = obj.Value;
                                                                }
                                                                else
                                                                    assetsUpdateInfo[obj.Key] = obj.Value;
                                                            }
                                                        }
                                                    }

                                                    isGetExtParams = true;
                                                }
                                            }
                                        }

                                        if (!isGetExtParams)
                                            tryGetExtTime = tryGetExtTime + 1;
                                    }
                                    else
                                        tryGetExtTime = tryGetExtTime + 1;
                                }
                            }
                            else
                                tryGetExtTime = tryGetExtTime + 1;
                        }
                        catch (Exception e)
                        {
                            Debug.LogErrorFormat(" 扩展参数异常 AssetsUpdator ExtParams Exception： {0} ", e);
                            TrackEvent("getExtParam_Exception", new Dictionary<string, object>()
                            {
                                { "tryGetExtTime", tryGetExtTime },
                                { "exception", e.ToString() },
                            });
                        }
                    });
                }

                if (ExtParams == null || Q1.Q1SDK.Instance == null)
                {
                    tryGetExtTime = tryGetExtTime + 1;
                    yield return new WaitForSeconds(0.1f);
                }
            }

            #endregion

            TrackEvent("getExtParam_end", new Dictionary<string, object>()
            {
                { "tryGetExtTime", tryGetExtTime },
                { "isGetExtParams", isGetExtParams }
            });
            ApkUpdateConfig = assetsUpdateInfo;
            Config.AddConfigs(ApkUpdateConfig); // 将配置信息读入Config
            SetCopyStreamingasset(); //设置拷贝开关 

            int streamingVer = GetFileVer(assetsUpdateInfo["files_url"].ToString());
            Log("new AssetsUpdater : streamingVer:" + streamingVer);
            local_config["GetStreamingAssetsPath_filesVer"] = streamingVer;
            object bWholePackage = null;
            assetsUpdateInfo.TryGetValue("wholepackage", out bWholePackage);

            local_config["wholepackage"] = bWholePackage ?? "false";

            string strWholePackage = bWholePackage != null ? bWholePackage.ToString() : "false";
            int localFilesVer = (int)local_config["GetStreamingAssetsPath_filesVer"];
            OldResVersionManager.SetWholepackage(strWholePackage == "true", localFilesVer);
        }

        Log("Updator:streaming_update_json");

        #endregion

        string serverConfigUrl = GetUrlByPlatform((string)assetsUpdateInfo["update_url"]);
        object tmp = null;
        List<object> serverConfigUrls = null;
        assetsUpdateInfo.TryGetValue("update_urls", out tmp);
        serverConfigUrls = (List<object>)tmp;
        assetsVersion = (long)assetsUpdateInfo["version"];

        #region 可写目录内update.json [会跟着版本更新]

        var localUpdateInfo = ReadLocalUpdateInfo(basePersistentPath);
        if (localUpdateInfo != null)
        {
            //Log("Loading local asset version...");
            long localVersion = (long)localUpdateInfo["version"];
            Log("new AssetsUpdater : localVersion:" + localVersion);
            // copy full pack app assets to persistent path
            if (localVersion > assetsVersion)
            {
                serverConfigUrl = GetUrlByPlatform((string)localUpdateInfo["update_url"]);
                assetsUpdateInfo.TryGetValue("update_urls", out tmp);
                serverConfigUrls = (List<object>)tmp;
                assetsVersion = localVersion;
            }
        }

        Log("local update.json:" + Json.Serialize(localUpdateInfo != null ? localUpdateInfo : assetsUpdateInfo));
        Log("Updator:basePersistentPath_update_json");
        SaveLaunchUpdateUrl(localUpdateInfo != null ? localUpdateInfo : assetsUpdateInfo);

        #endregion

        //if(serverConfigUrls!=null)
        //{
        //    AssetBundleManager.DNS_LIST = serverConfigUrls;
        //    try
        //    {
        //        serverConfigUrl = (string)serverConfigUrls[AssetBundleManager.DNS_IND];
        //    }
        //    catch (Exception e)
        //    {
        //        LogError("serverConfigUrls error:"+e.ToString());
        //    }
        //}
        try
        {
            if (!Directory.Exists(basePersistentPath))
            {
                Directory.CreateDirectory(basePersistentPath);
            }
        }
        catch (Exception e)
        {
            LogHelpBase.LogError(e.ToString());
        }

        // 新增跳过开关
        isSkipDownloadUpdateJson = GetConfigByDomainOrLocal("skip_download_update_json").ToString() == "true";
        float skipTimeout = GetTimeOutConfigByDomain(4f, "updateSkipTimeout");

        TrackEvent("update_config_start", new Dictionary<string, object>()
        {
        });
        if (isSkipDownloadUpdateJson && localUpdateInfo != null)
        {
            // 跳过下载分支
            ParseUpdateJson(Json.Serialize(localUpdateInfo), localUpdateInfo);
            TrackEvent("update_config_skip", new Dictionary<string, object>()
            {
                { "assetsVersion", assetsVersion },
            });
            Log($"update_config_skip:{isSkipDownloadUpdateJson}");
            
            ApkUpdateConfig.TryGetValue("res_key", out var res_key);
            var last_role_id = PlayerPrefs.GetFloat("last_role_id", -1);
            TrackEvent("update_config_finish", new Dictionary<string, object>()
            {
                { "url", "skip download update json" },
                { "res_key", res_key },
                { "last_role_id", last_role_id },
            });
        }
        else
        {
            // read update config from server by serverConfigUrl

            var listUpdateJsonUrl = new List<string>();
            if (serverConfigUrls != null)
            {
                foreach (var ob in serverConfigUrls)
                {
                    listUpdateJsonUrl.Add(ob.ToString());
                }
            }
            else
            {
                listUpdateJsonUrl.Add(serverConfigUrl);
                //listUpdateJsonUrl.Add(serverConfigUrl);
            }

            var urls = listUpdateJsonUrl.ToArray();
            ApkUpdateConfig.TryGetValue("res_key", out var res_key);
            bool encryptUpdateAndServerInfo = Config.IsTrue("p_encryptUpdateAndServerInfo");
            string updateJsonKey = "update.json";

            System.Action<object> cb = (o) =>
            {
                var rh = o as RockHttp;
                var c = rh.Cache;

                var ccr = c[updateJsonKey];
                if (ccr == null)
                {
                    "".PrintError("RockHttp 获取update.json数据异常");
                }

                string json = null;
                if (encryptUpdateAndServerInfo) // 对update.json和server_info.json进行了加密，需要解密
                {
                    var bytes = AlgorithmUtils.XorEncrypt(ccr.data);
                    json = Encoding.UTF8.GetString(bytes);
                }
                else
                {
                    json = ccr.text;
                }

                "".print("json", json);
                //保存update的相关信息,开启开关的时候
                if (isSkipDownloadUpdateJson)
                {
                    //读取domain剔除key的配置
                    object filter_operation = GetConfigByDomainOrLocal("filter_key_update_json");
                    string strfilter = filter_operation?.ToString() ?? string.Empty;
                    "".print("strfilter: " + strfilter);
                    if (!string.IsNullOrEmpty(strfilter))
                    {
                        try
                        {
                            HashSet<string> not_filter_list = new HashSet<string>()
                            {
                                "files_url",
                                "version",
                                "server_info_url",
                                "resource_url",
                                "update_url",
                                "update_urls"
                            };
                            strfilter = strfilter.ToLower();
                            HashSet<string> filter_set = new HashSet<string>(strfilter.Split(','));
                            //如果配置了剔除key则保存update.json时过滤这些key-value
                            Dictionary<string, object> update_dic = (Dictionary<string, object>)MiniJSON.Json.Deserialize(json);
                            List<string> keys = new List<string>(update_dic.Keys);
                            int len = keys.Count;
                            for (int i = 0; i < len; i++)
                            {
                                string key = keys[i];
                                if (!not_filter_list.Contains(key))
                                {
                                    string key_lower = key.ToLower();
                                    if (filter_set.Contains(key_lower))
                                    {
                                        update_dic.Remove(key);
                                    }
                                }
                            }
                            string new_json = MiniJSON.Json.Serialize(update_dic);
                            "".print("strfilter1111111111: " + new_json);
                            SaveLocalUpdateInfo(new_json);
                        }
                        catch (System.Exception e)
                        {
                            LogErrorFormat("update.json filter failed! error: [{0}]", e.Message);
                        }
                    }
                    else
                    {
                        SaveLocalUpdateInfo(json);
                    }
                }

                try
                {
                    Dictionary<string, object> serverUpdateInfo =
                        (Dictionary<string, object>)MiniJSON.Json.Deserialize(json);
                    U3D.Threading.Dispatcher.instance.ToMainThread(() => { ParseUpdateJson(json, serverUpdateInfo); });
                }
                catch (System.Exception e)
                {
                    LogErrorFormat("update.json Read failed! error: [{0}]", e.Message);
                }

                U3D.Threading.Dispatcher.instance.ToMainThread(() =>
                {
                    var last_role_id = PlayerPrefs.GetFloat("last_role_id", -1);
                    TrackEvent("update_config_finish", new Dictionary<string, object>()
                    {
                        { "url", ccr.url },
                        { "res_key", res_key },
                        { "last_role_id", last_role_id },
                    });
                });
            };

            var sh = new RockHttp()
            {
                //这里timeout是请求WWW或者http时的超时时间，不是RockHttp重试结束时间，在未获取到任意一个链接数据前，会一直重试，
                //除非当前RockHttp的finish被主动设置为true,才会在请求回调时停止重试
                timeout = 20,
                callback = cb
            };

            if (encryptUpdateAndServerInfo)
            {
                for (int i = 0; i < urls.Length; ++i)
                {
                    urls[i] = urls[i].Replace(".json", ".data"); // 加密文件后缀修改
                }
            }

            sh.SetUrls(updateJsonKey, urls);
            sh.Start();

            var t = Time.realtimeSinceStartup;
            isUseLocalUpdateJson = GetDefaultTrueByStr(GetConfigByDomainOrLocal("use_local_update_json_new"));
            "".Print("isUseLocalUpdateJson", isUseLocalUpdateJson);

            while (UpdateConfig == null)
            {
                if (Time.realtimeSinceStartup - t > skipTimeout && isUseLocalUpdateJson)
                {
                    Log("download_updateJson_timeout");
                    //结束当前RockHttp
                    sh.finish = true;
                    usedLocalUpdateJson = true;
                    //使用本地update.json
                    Dictionary<string, object> localJsonDic =
                        localUpdateInfo != null ? localUpdateInfo : assetsUpdateInfo;
                    ParseUpdateJson(localUpdatejson, localJsonDic);
                    var last_role_id = PlayerPrefs.GetFloat("last_role_id", -1);
                    TrackEvent("update_config_finish", new Dictionary<string, object>()
                    {
                        { "url", "use local update.json" },
                        { "res_key", res_key },
                        { "last_role_id", last_role_id },
                        { "json", localUpdatejson },
                    });
                    break;
                }
                yield return null;
            }
            SetGlobalLuaExceptionReport();
        }
        
    }

    public void SetOldResVersion()
    {
        Log("SetOldResVersion");
        float skipTimeout = GetTimeOutConfigByDomain(4f, "updateSkipTimeout");
        bool isOpenOldResVersion = GetDefaultTrueByStr(GetConfigByDomainOrLocal("is_open_old_res_version"));
        bool isSaveHashRemoteVirtual = GetDefaultTrueByStr(GetConfigByDomainOrLocal("is_save_hash_remote_virtual")) ;
        //审核服默认使用老版本
        if (s_IsReviewing)
        {
            isOpenOldResVersion = true;   
        }
        if (isOpenOldResVersion)
        {
            DeleteAllOldRes();
        }
        Log("SetOldResVersion s_IsReviewing =" + s_IsReviewing + "isOpenOldResVersion=" + isOpenOldResVersion);
        OldResVersionManager.SetIsOpenOldResVersion(isOpenOldResVersion);
        OldResVersionManager.SetIsSaveHashRemoteVirtual(isSaveHashRemoteVirtual);
        
        //老版本开关 打点上报
        TrackEvent("old_res_version__toggle", new Dictionary<string, object>() {
            { "is_open_old_res_version", isOpenOldResVersion },
            { "is_save_hash_remote_virtual", isSaveHashRemoteVirtual },
            { "skipTimeout", skipTimeout },
        });
    }

    public static void SaveLocalUpdateInfo(string json)
    {
        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");
        //此功能依赖这个开关
        if (isSkipDownloadUpdateJson)
        {
            Log($"save update.json in PersistentPath{isSkipDownloadUpdateJson}");
            var localUpdateConfigPath = basePersistentPath + AssetBundleManager.UPDATE_CONFIG;
            if (!Directory.Exists(basePersistentPath))
            {
                Directory.CreateDirectory(basePersistentPath);
            }
            // 保存到可写目录
            try
            {
                if (isGarbleUpdateJson)
                {
                    var bytes = Encoding.Unicode.GetBytes(json);
                    File.WriteAllBytes(localUpdateConfigPath, Aes.Encryptor(bytes, AssetBundleManager.UpdateConfigEncryptKey, AlgorithmUtils.HashUtf8MD5(AssetBundleManager.UPDATE_CONFIG).Substring(8, 16)));
                }
                else
                {
                    File.WriteAllText(localUpdateConfigPath, json);
                }
            }
            catch (Exception e)
            {
                LogError($"Save update.json failed: {e}");
            }
        }
    }

    /// <summary>
    /// 获取key的配置，优先获取Domain，获取不到时使用本地配置
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public Object GetConfigByDomainOrLocal(string key)
    {
        Object resuletValue = null;
        string resuleType = "none";
        if (operationConfig.TryGetValue(key, out Object value))
        {
            resuleType = "domain";
            resuletValue = value; //domain配置中开关
        }
        else
        {
            "".Print($"domain operationConfig中{key}未配置或者获取异常");
            //domain中开关未配置，使用包内package.json中开关
            if (assetsUpdateInfo.TryGetValue(key, out Object localvalue))
            {
                resuleType = "inPackage";
                resuletValue = localvalue; //包内配置中开关
            }
        }

        string resuletStr = resuletValue != null ? resuletValue.ToString() : ""; //.ToLower();
        //本地还是domain的开关
        TrackEvent("domain_or_package_switch", new Dictionary<string, object>()
        {
            { "key", key },
            { key, resuletStr },
            { "type", resuleType },
        });
        //domain中bool值为小写，package内的bool为大写，需要统一转为小写，统一判断逻辑
        return resuletStr.ToLower();
    }

    /// <summary>
    /// 获取key的配置
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public float GetTimeOutConfigByDomain(float val, string key)
    {
        float resuletFloat = val;
        try
        {
            if (operationConfig.TryGetValue(key, out Object value))
            {
                resuletFloat = float.Parse(value.ToString()); //domain配置
            }
        }
        catch (Exception e)
        {
            LogErrorFormat("SkipTimeout to float error: [{0}]", e.Message);
        }

        return resuletFloat;
    }

    void ParseUpdateJson(string json, Dictionary<string, object> jsonDic)
    {
        Dictionary<string, object> serverUpdateInfo = jsonDic;

        //Log("start read update_json");

        //Log("read update_json finish");
        if (serverUpdateInfo == null)
        {
            return;
        }

        object isUseFile2Zip = null;
        serverUpdateInfo.TryGetValue("use_file2_zip", out isUseFile2Zip);
        if (serverUpdateInfo.TryGetValue("files_url", out var files_url))
        {
            serverUpdateInfo["files_url"] = files_url.ToString().Replace("files.txt", "files2.bytes");
        }

        if (serverUpdateInfo.TryGetValue("review_files_url", out var review_files_url))
        {
            serverUpdateInfo["review_files_url"] = review_files_url.ToString().Replace("files.txt", "files2.bytes");
        }

        "".Print("files_url", files_url, "review_files_url", review_files_url);

        UpdateConfig = serverUpdateInfo;

        AssetBundleManager.serverUpdateJson = json;

        //GetSupportMiniGameCSV();
        //"files_url": "https://down-wmzz.q1.com/marvelous/marvelous_update/huawei/Android/resource/38/files.txt",
        var files_url_str = serverUpdateInfo["files_url"].ToString();
        local_config["remote_filesVer"] = GetFileVer(files_url_str);
        Debug.Log("URL:" + GetFileVer(files_url_str));

        object a = null;
        serverUpdateInfo.TryGetValue("use_inner_files", out a);
        local_config["use_inner_files"] = a ?? "false";

        "".Print("files_ver", files_url_str);

        SaveLaunchUpdateUrl(serverUpdateInfo);
    }

    private static void SaveLaunchUpdateUrl(Dictionary<string, object> serverUpdateInfo)
    {
        if (Launch.LaunchMgr.Ins != null)
        {
            if (serverUpdateInfo.TryGetValue("launch_update_url", out var launch_update_url))
            {
                PlayerPrefs.SetString("launch_update_url", launch_update_url.ToString());
                "".Print("launch_update_url", launch_update_url);
            }
        }
    }

    public static int GetFileVer(string files_url)
    {
        var mark = "/resource/";
        var startind = files_url.IndexOf(mark) + mark.Length;
        var endind = files_url.IndexOf("/files");
        var ver = files_url.Substring(startind, endind - startind);
        var files_ver = -1;
        int.TryParse(ver, out files_ver);

        "".Print("GetFileVer", files_url, files_ver);
        return files_ver;
    }

    public static int URL_LENGTH
    {
        set { AssetBundleManager.URL_LENGTH = value; }
        get { return AssetBundleManager.URL_LENGTH; }
    }

    private static int GetTimeout(int i)
    {
        //int timeout = (1 + i / URL_LENGTH) * Mathf.FloorToInt(Mathf.Sqrt(i + 1)) *2 - 1;
        //Debug.LogWarning(string.Format("GetTimeout:{0} URL_LENGTH:{1} timeout:{2}", i,URL_LENGTH, timeout));
        //return timeout;
        return AssetBundleManager.GetTimeout(i);
    }

    IEnumerator UpdateRes()
    {
        Log("UpdateRes");
        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");

        var serverUpdateInfo = UpdateConfig;

        #region 判断审核版本。如果是不需要更新

        // 正在审核中的版本
        long remoteReviewingVersion = -1;
        if (serverUpdateInfo.ContainsKey("reviewing_version"))
        {
            remoteReviewingVersion = (long)serverUpdateInfo["reviewing_version"];
            Log("remoteReviewingVersion:" + remoteReviewingVersion);
        }

        long localReviewingVersion = -1;
        if (assetsUpdateInfo.ContainsKey("reviewing_version"))
        {
            localReviewingVersion = (long)assetsUpdateInfo["reviewing_version"];
            Log("localReviewingVersion:" + localReviewingVersion);
        }

        if (localReviewingVersion == GetRwVer() || (!usedLocalUpdateJson &&
                                                    localReviewingVersion == remoteReviewingVersion &&
                                                    remoteReviewingVersion != -1))
        {
            Log("***REVIEWING APP***");
            s_IsReviewing = true;
            AssetBundleManager.s_IsReviewing = true;
            isSetReview = true;
            onSetReviewFinish?.Invoke();
            onSetReviewFinishCsharp?.Invoke();
            bool success = false;
            // update server info
            if (serverUpdateInfo.ContainsKey("reviewing_server_info_url"))
            {
                string serverInfoUrl = (string)serverUpdateInfo["reviewing_server_info_url"];
                bool encryptUpdateAndServerInfo = Config.IsTrue("p_encryptUpdateAndServerInfo");
                if (encryptUpdateAndServerInfo)
                    serverInfoUrl = serverInfoUrl.Replace(".json", ".data"); // 加密时替换文件名

                yield return UpdateServerInfo(serverInfoUrl);
                success = ServerInfo.Instance.IsValid();
            }

            if (!success)
            {
                RaiseError(ErrCode.EUpdateServerInfo, "Failed to update server info");
                yield break;
            }

            Log("update remote version res complete");

            #region 边玩边下资源

            yield return PrepareHashCheck((string)serverUpdateInfo["files_url"]);
            Log("Updator:PrepareHashCheck");

            #endregion

            yield return OnUpdateCompleted();
            yield break;
        }
        else
        {
            s_IsReviewing = false;
            AssetBundleManager.s_IsReviewing = false;
            isSetReview = true;
            onSetReviewFinish?.Invoke();
            onSetReviewFinishCsharp?.Invoke();
        }

        #endregion

        #region 边玩边下资源

        yield return PrepareHashCheck((string)serverUpdateInfo["files_url"]);
        Log("Updator:PrepareHashCheck");

        #endregion

        // update server info
        if (serverUpdateInfo.ContainsKey("server_info_url"))
        {
            //string serverInfoUrl = (string)serverUpdateInfo["server_info_url"];
            //if (U3D.Threading.Dispatcher.instance)
            //{
            //    U3D.Threading.Dispatcher.instance.StartCoroutine(WaitUpdateServerInfo(serverInfoUrl));
            //}
            //else
            //{
            //    yield return WaitUpdateServerInfo(serverInfoUrl);
            //}
        }

        #region 强制更新apk 版本升高

        yield return UpdateApk(serverUpdateInfo, basePersistentPath);
        if (shouldInstallApk)
        {
            yield break;
        }

        #endregion

        #region 更新dll和update.json

        /*long serverVersion = (long)serverUpdateInfo["version"];
        if (serverVersion > assetsVersion)
        {
            yield return StartCoroutine(HandlePatchsFromServer((string)serverUpdateInfo["patch_url"], assetsVersion, serverVersion));
        }
        else
        {
            yield return OnUpdateCompleted();
        }*/
        //LogError("OnUpdateComplete");

        yield return OnUpdateCompleted();

        #endregion
    }

    /// <summary>
    /// 处理是否需要强制更新应用
    /// </summary>
    /// <returns></returns>
    IEnumerator UpdateApk(Dictionary<string, object> serverUpdateInfo, string basePersistentPath)
    {
        shouldInstallApk = false;

        // 资源版本号不会升高
        object url;
        if (serverUpdateInfo.TryGetValue("downdload_url", out url))
        {
            var match = Regex.Match((string)url, @"[^/\\]+?(\d+[\d.]+)\.(apk|ipa)$");
            if (match.Success)
            {
                if (War.Script.Utility.VersionIsHigher(match.Groups[1].Value, Application.version))
                {
#if !UNITY_EDITOR
#if UNITY_ANDROID
                    if (!enableLuaApk)
                    {
                        // 原来的dll是老版本
                        if (Directory.Exists(Path.Combine(basePersistentPath, "dlls")))
                            Directory.Delete(Path.Combine(basePersistentPath, "dlls"), true);

                        if (StartDownloadApk != null) StartDownloadApk.Invoke(match.Groups[1].Value);

                        string channel_url = (string)url;
                        var complete_url = channel_url.Replace("$CHANNEL_ID", ChannelStr);

                        // 分块下载
                        object md5Obj = null;
                        if (serverUpdateInfo.TryGetValue("md5", out md5Obj))
                        {
                            SortedDictionary<string, string> md5 = new SortedDictionary<string, string>();
                            foreach (KeyValuePair<string, object> kv in (Dictionary<string, object>)md5Obj)
                                md5.Add(kv.Key, (string)kv.Value);

                            var partSize = 6;
                            var totalM = md5.Count * partSize;
                            var apkMatch = Regex.Match(complete_url, @"([^/\\]+\.apk)$");
                            var index = 0;

                            // TODO: 可以判断这个apk是不是就是要安装的apk
                    #region 删除以前apk
                            var files = Directory.GetFiles(basePersistentPath, "*.apk", SearchOption.TopDirectoryOnly);
                            foreach (var filePath in files)
                            {
                                Log("delete exist apk " + filePath);
                                File.Delete(filePath);
                            }
                    #endregion

                            foreach (KeyValuePair<string, string> kv in md5)
                            {
                                // 如此实现断点续传功能
                                files = Directory.GetFiles(basePersistentPath, "*.apk" + kv.Key, SearchOption.TopDirectoryOnly);
                                if (files.Length > 0)
                                {
                                    Log(kv.Key + " exist!!!");
                                    index++;
                                    continue;
                                }
                                var inner = index;
                                // TODO 计算大小。都是6M
                                yield return DownloadApk_md5(complete_url, (val, total) => { if (OnDownload != null) OnDownload.Invoke((inner * partSize + val * partSize / total) * 100 / totalM, 100); }, basePersistentPath + apkMatch.Value, kv.Key, kv.Value, index, md5.Count == (index + 1));
                                index++;
                            }


                            // 下载完合并文件
                            using (FileStream streamWriter = new FileStream(basePersistentPath + apkMatch.Value, FileMode.Create, FileAccess.Write))
                            {
                                byte[] bytes = null;

                                // 全下载好了。拼接文件
                                foreach (KeyValuePair<string, string> kv in md5)
                                {
                                    using (FileStream reader = new FileStream(basePersistentPath + apkMatch.Value + kv.Key, FileMode.Open, FileAccess.Read))
                                    {
                                        if (bytes == null)
                                            bytes = new byte[reader.Length];

                                        reader.Read(bytes, 0, (int)reader.Length);
                                        streamWriter.Write(bytes, 0, (int)reader.Length);
                                    }
                                }
                            }

                    #region 删除以前apk[a-z][a-z]
                            files = Directory.GetFiles(basePersistentPath, "*.apk??", SearchOption.TopDirectoryOnly);
                            foreach (var filePath in files)
                            {
                                Log("delete exist apk-part " + filePath);
                                File.Delete(filePath);
                            }
                    #endregion

                            // 文件合并好了
                            cachefullfileName = basePersistentPath + apkMatch.Value;
                            ShowGoToInstall(true);
                        }
                        else
                        {
                            //TODO 当前版本号在"must_reinstall_versions"中才重装。不用也可以
                            yield return DownloadApk(complete_url, (val, total) => { if (OnDownload != null) OnDownload.Invoke(val, total); });
                        }
                        shouldInstallApk = true;
                    }
#elif UNITY_IOS
                    RaiseError(ErrCode.EShowAppStoreUI, "version:" + match.Groups[1].Value + ",local version:" + Application.version);
                    shouldInstallApk = true;
                    while(shouldInstallApk)
                    {
                        yield return null;
                    }
#else
                    shouldInstallApk = false;
#endif
#else
                    shouldInstallApk = false;
#endif
                    yield break;
                }
            }
        }


        Log("Updator:downdload_url");
    }

    //强更
    public void SkipUpdateJumpAppStore()
    {
        shouldInstallApk = false;
    }

    //强更
    public void ForceUpdateJumpAppStore()
    {
#if !UNITY_EDITOR
#if UNITY_ANDROID
        LogWarning("Force update android");
#elif UNITY_IOS
        // 跳转App Store mt是meta-type.8是Mobile Software Applications
        // mt 代表 meta-type，有效值如下：
        // 1 Music
        // 2 Podcasts
        // 3 Audiobooks
        // 4 TV Shows
        // 5 Music Videos
        // 6 Movies
        // 7 iPod Games
        // 8 Mobile Software Applications
        // 9 Ringtones
        // 10 iTunes U
        // 11 E-Books
        // 12 Desktop Apps
        const string APPID = "1478697636";
        GotoAppStore(string.Format("itms-apps://itunes.apple.com/app/id{0}?mt=8", APPID));
#endif //end if platform
#endif //end if !UNITY_EDITOR
    }

    protected string GetUrlByPlatform(string url, long version = 0)
    {
        string newUrl = url.Replace("$platform", BaseLoader.GetPlatformFolderForAssetBundles());
        return newUrl.Replace("$version", version.ToString());
    }

    /// <summary>
    /// 检查streamingassets 内abname的hash是否与服务器上的hash一致,是则更新persistent下的hash记录(删除persistent下的资源,标记可以读取本地资源),本地资源的加载优先级是persistent>streamassets
    /// </summary>
    /// <param name="remote"></param>
    /// <param name="persistent"></param>
    /// <param name="stream"></param>
    void CombineHashCheck(hashCheck remote, hashCheck persistent, hashCheck stream)
    {
        if (stream == null || remote == null)
        {
            //yield break;
            return;
        }

        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");

        AssetsSync.Instance.m_BaseDownloadingPath = basePersistentPath;

        List<string> ab2Remove = new List<string>();
        foreach (var kp in remote.list)
        {
            var k = kp.Key;
            if (stream.list.ContainsKey(k) && stream.CheckSame(k, remote.list[k]))
            {
                if (persistent.list.ContainsKey(k) && stream.CheckSame(k, persistent.list[k]))
                {
                    ab2Remove.Add(k);
                }

                //persistent.list[k] = stream.list[k];
                persistent.Update(k, stream.list[k]);
            }
            else if (persistent.list.ContainsKey(k))
            {
                string fullfileName = Path.Combine(basePersistentPath, k);
                if (!File.Exists(fullfileName))
                {
                    //Log("remove key:" + k);
                    persistent.list.Remove(k);
                }
            }
        }

        //yield return null;
        foreach (var ab in ab2Remove)
        {
            string fullfileName = Path.Combine(basePersistentPath, ab);
            if (File.Exists(fullfileName))
            {
                File.Delete(fullfileName);
            }
        }

        AssetsSync.Instance.UpdateFileTxt(persistent);
    }

    hashCheck hashRemote = null;
    hashCheck hashStreamingAssets = null;
    hashCheck hashPersistent = null;

    IEnumerator PrepareHashCheck(string fileUrl)
    {
        hashRemote = null;
        hashStreamingAssets = null;
        hashPersistent = null;
        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");

        AssetsSync.Instance.m_BaseDownloadingPath = basePersistentPath;

        iReportFilesFinish = 0;

        bool isOpenOldResVersion = GetDefaultTrueByStr(GetConfigByDomainOrLocal("is_open_old_res_version"));
        float skipFilesTimeout;
        if (isOpenOldResVersion || s_IsReviewing)
        {
            skipFilesTimeout = GetTimeOutConfigByDomain(0, "filesSkipTimeout");
        }
        else
        {
            skipFilesTimeout = GetTimeOutConfigByDomain(6f, "filesSkipTimeout");   
        }

        totalZip = 0;
        unpackedZip = 0;
        //Log("fileUrl:" + fileUrl);
        var currentPatchUrl = "";

        currentPatchUrl = GetStreamingPath(BaseLoader.GetStreamingAssetsPath() + "files.txt");
        Log("GetStreamingAssetsPath:" + currentPatchUrl);


        var gbkEncoding = Encoding.GetEncoding("utf-8");
        War.Script.Utility.SetSharpZipDefaultCodePage(gbkEncoding.CodePage);

        ApkUpdateConfig.TryGetValue("res_key", out var res_key);
        ApkUpdateConfig.TryGetValue("ch_res_key", out var ch_res_key);
        ApkUpdateConfig.TryGetValue("collection_res_key", out var collection_res_key);
        var keyInKey2mark = collection_res_key?.ToString();

        if (!string.IsNullOrEmpty(keyInKey2mark))
        {
            if (PlayerPrefs.GetString("mgame_reskey", "") != "")
            {
                if (PlayerPrefs.GetInt(PlayerPrefs.GetString("mgame_reskey", ""), 0) == 1)
                {
                    PlayerPrefs.SetInt(PlayerPrefs.GetString("mgame_reskey", ""), 0);
                    Config.HookResKey = "";
                }
            }
        }

        using (var patchInfoWWW = new WWW(currentPatchUrl))
        {
            yield return patchInfoWWW;
            if (patchInfoWWW.error != null)
            {
                LogFormat("Download fileUrl list {0} failed!", currentPatchUrl);
            }
            else
            {
                hashStreamingAssets = War.Base.hashCheck.Parse(patchInfoWWW.text);

                Log("GetStreamingAssetsPath finish:" + currentPatchUrl);
                //Log("hashStreamingAssets:" + AssetsSync.ToJson(hashStreamingAssets));
            }
        }

        Log("Updator:hashStreamingAssets");

        //审核服hashRemote直接使用hashStreamingAssets赋值，跳过file2.txt的获取
        //审核服 现在使用OldResVersionManager
        /*if (s_IsReviewing)
        {
            hashRemote = hashRemote ?? hashStreamingAssets;
        }*/

        SetOldResVersion();
        var last_role_id = PlayerPrefs.GetFloat("last_role_id", -1);

        string filesVer = "filesVer_new";

        TrackEvent("update_file_init", new Dictionary<string, object>()
        {
            { "usedLocalUpdateJson", usedLocalUpdateJson },
            { "skipFilesTimeout", skipFilesTimeout },
            { "isOpenOldResVersion", isOpenOldResVersion },
        });
        currentPatchUrl = fileUrl;
        if (string.IsNullOrEmpty(fileUrl) == false && !s_IsReviewing)
        {
            Log("PreFileTxt");

            if (usedLocalUpdateJson == false)
            {
                var savedfilesVer = PlayerPrefs.GetString(filesVer, "");
                System.Text.StringBuilder str = new System.Text.StringBuilder();
                str.Append(basePersistentPath);
                str.Append("localfiles.txt");
                var localCacheRemoteUrl = str.ToString();
                var bytesUrl = localCacheRemoteUrl.Replace("localfiles.txt", "files2.bytes");
                if (!string.IsNullOrEmpty(savedfilesVer) && currentPatchUrl == savedfilesVer)
                {
                    Log("Updator:hashRemoteCache0");
                    var remoteContent = ReadLocalFile(localCacheRemoteUrl);
                    if (!string.IsNullOrEmpty(keyInKey2mark))
                    {
                        res_key = keyInKey2mark;
                        hashRemoteParseJson = remoteContent;
                        Log("hashRemoteParseJson_1");
                    }
                    hashRemote = War.Base.hashCheck.Parse(remoteContent, res_key?.ToString(), ch_res_key?.ToString()); // hashRemote添加ch_res_key配置
                    iReportFilesFinish = 2;

                    Log("Updator:hashRemoteCache1");
                }

                if (hashRemote == null)
                {
                    Log("Updator:DownloadFiles2Txt");
                    // 下载处理files2
                    DownloadFiles2Txt(currentPatchUrl, filesVer);
                }

                var t = Time.realtimeSinceStartup;
                while (hashRemote == null)
                {
                    if (local_config["use_inner_files"].ToString() == "true")
                    {
                        if (Time.realtimeSinceStartup - t > skipFilesTimeout)
                        {
                            TrackEvent("download_files2_timeout", new Dictionary<string, object>()
                            {
                            });
                            Log("download_files2_timeout");
                            break;
                        }
                    }

                    yield return null;
                }
            }
        }

        Log($"Updator:hashRemote == hashStreamingAssets:{hashStreamingAssets == hashRemote}");

        System.Text.StringBuilder _str = new System.Text.StringBuilder();
        _str.Append(basePersistentPath);
        _str.Append("files.txt");
        currentPatchUrl = _str.ToString();
        Log("basePersistentPath:" + currentPatchUrl);

        var persistentContent = ReadLocalFile(currentPatchUrl);
        if (string.IsNullOrEmpty(persistentContent) == false)
        {
            hashPersistent = War.Base.hashCheck.Parse(persistentContent);
            Log("hashPersistent:finish" /*+ AssetsSync.ToJson(hashPersistent)*/);
        }
        else
        {
            LogFormat("Download basePersistentPath filetxt {0} failed!", currentPatchUrl);
        }

        hashPersistent = hashPersistent == null ? new hashCheck() { list = new Dictionary<string, string[]>() } : hashPersistent;
        hashPersistent.isDirty = false;
        Log("Updator:hashPersistent");

        string filesUrl = UpdateConfig["files_url"].ToString();
        OldResVersionManager.SetRemoteFilesUrl(filesUrl, usedLocalUpdateJson);

        AssetBundleManager.hashStreamingAssets = hashStreamingAssets;
        AssetBundleManager.hashPersistent = hashPersistent;
        AssetBundleManager.InitHashCachingCheck();

        AssetBundleManager.res_key = string.IsNullOrEmpty(keyInKey2mark) == false ? keyInKey2mark : res_key?.ToString();
        AssetBundleManager.ch_res_key = ch_res_key?.ToString();

        if (hashRemote != null)
        {
            //获取到最新远程资源列表，判断差异资源大小，如果超过设定阈值，则开启老版本模式
            AssetBundleManager.hashRemote = hashRemote;
            AssetBundleManager.mCacheList = new List<hashCheck>()
            {
                AssetBundleManager.hashCaching,
                AssetBundleManager.hashPersistent,
                AssetBundleManager.hashStreamingAssets,
                AssetBundleManager.hashRemote,
            };

            string[] difflist = AssetBundleManager.GetDiffList();
            if (difflist.Length > 0)
            {
                int difflistTotalSize = 0;
                foreach (string item in difflist)
                {
                    difflistTotalSize += hashRemote.GetSize(item);
                }

                OldResVersionManager.diffListTotalSize = difflistTotalSize;
                LogFormat("UseOldResVersion downLoad-res-size:{0}   OPEN_CONDITIONS__UPDATE_RES_SIZE:{1}",  difflistTotalSize, OldResVersionManager.OPEN_CONDITIONS__UPDATE_RES_SIZE);
                //, OldResVersionManager.OPEN_CONDITIONS__UPDATE_RES_SIZE, AssetsSync.ToJson(difflist)
                //老包是否拉取热更新量的限制 单位(字节)
                object configObj = GetConfigByDomainOrLocal("oldres_version_update_size");
                string configStr = configObj?.ToString() ?? string.Empty;
                // 注意int类型的大小限制  2G
                int defaultThreshold = 0;
                int limitSize = defaultThreshold;
                "".Print("oldres_version_update_size", configObj, configStr);
                if (int.TryParse(configStr.Trim(), out int configThreshold))
                {
                    limitSize = configThreshold;
                    Debug.Log($"use configThreshold{configThreshold}");
                }
                else
                {
                    Debug.Log($"use configThreshold:{configObj}:{configStr}");
                }

                if (difflistTotalSize > limitSize)
                {
                    yield return OldResVersionManager.UseOldResVersionByCoroutine("exceed_update_res_size");
                    if (OldResVersionManager.IS_OLD_RES_VERSION)
                    {
                        //有使用老版本
                        hashRemote = AssetBundleManager.hashRemote;
                    }
                }
            }
        }
        else if (hashRemote == null)
        {
            //使用老资源会赋值给AssetBundleManager.hashRemote
            yield return War.Base.OldResVersionManager.UseOldResVersionByCoroutine("download_files2_timeout");

            if (usedLocalUpdateJson == true)
                iReportFilesFinish = 3;
            else
                iReportFilesFinish = 4;

            //前面老资源和下载都没获取到会使用包内files2获取，触发最后保底
            if (AssetBundleManager.hashRemote == null)
            {
                Log("Updator:GetStreamingAssetsFiles2");
                yield return AssetBundleManager.InitHashStreamingCheckFiles2((hashStreamingAssetsFiles2) =>
                {
                    AssetBundleManager.hashRemote = hashCheck.Parse(hashStreamingAssetsFiles2, res_key?.ToString(),
                        ch_res_key?.ToString());
                });
                iReportFilesFinish = 5;
            }

            //这里主要是对小游戏合集需要的未裁剪的完整资源列表做补充
            if (AssetBundleManager.hashRemote != null)
            {
                string json = AssetBundleManager.hashRemote.ToJson();
                if (!string.IsNullOrEmpty(keyInKey2mark))
                {
                    res_key = keyInKey2mark;
                    hashRemoteParseJson = json;
                    Log("hashRemoteParseJson_4");
                }
                hashRemote = hashCheck.Parse(json, res_key?.ToString(), ch_res_key?.ToString());
            }

            TrackEvent("SkipRemoteHash", new Dictionary<string, object>() {
                //{ "url",currentPatchUrl },

            });

            Log("Updator:SkipRemoteHash");
        }

        "".Print("hashcr", hashRemote == hashStreamingAssets);
        AssetBundleManager.hashRemote = hashRemote;

        // hook res_key
        if (string.IsNullOrEmpty(Config.HookResKey) == false)
        {
            ApkUpdateConfig["res_key"] = Config.HookResKey;
        }
        if(AssetBundleManager.hashRemote!=null && iReportFilesFinish!=1)
        {
            TrackEvent("update_file_finish", new Dictionary<string, object>() {
                { "finish_id",iReportFilesFinish },
            });
        }
    }

    void DownloadFiles2Txt(string currentPatchUrl, string filesVer)
    {
        string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
        string basePersistentPath = basePersistentUrl.Replace("file://", "");
        ApkUpdateConfig.TryGetValue("res_key", out var res_key);
        ApkUpdateConfig.TryGetValue("ch_res_key", out var ch_res_key);
        ApkUpdateConfig.TryGetValue("collection_res_key", out var collection_res_key);
        System.Text.StringBuilder str = new System.Text.StringBuilder();
        str.Append(basePersistentPath);
        str.Append("localfiles.txt");
        var localCacheRemoteUrl = str.ToString();
        var bytesUrl = localCacheRemoteUrl.Replace("localfiles.txt", "files2.bytes");
        if (hashRemote == null)
        {
            TrackEvent("update_file_start", new Dictionary<string, object>()
            {
                { "start_id", "2" },
            });
            int failTime = 0;
            var flist2backup = new List<string>();

            System.Action<object> fcb2 = (o) =>
            {
                // file2 bytes 解压失败用
                var rh = o as RockHttp;
                var c = rh.Cache;
                var finish = false;
                foreach (var kk in c.Keys)
                {
                    var ccr = c[kk];
                    var json = ccr.text;
                    var keyInKey2mark = collection_res_key?.ToString();
                    if (!string.IsNullOrEmpty(keyInKey2mark))
                    {
                        res_key = keyInKey2mark;
                        hashRemoteParseJson = json;
                        Log("hashRemoteParseJson_2");
                    }

                    hashRemote = War.Base.hashCheck.Parse(json, res_key?.ToString());
                    if (hashRemote == null)
                    {
                        LogError("hashRemote parse error");
                    }
                    else
                    {
                        File.WriteAllText(localCacheRemoteUrl, json);
                        PlayerPrefs.SetString(filesVer, currentPatchUrl);
                        iReportFilesFinish = 1;
                        TrackEvent("update_file_finish", new Dictionary<string, object>()
                        {
                            { "finish_id", iReportFilesFinish },
                            { "url", ccr.url },
                        });
                        finish = true;

                        break;
                    }
                }

                rh.finish = finish;
            };
            var tryUnpackAndDownloadTime = 5;
            System.Action<object> fcb = (o) =>
            {
                var rh = o as RockHttp;
                var c = rh.Cache;

                var finish = false;
                // 目前只有有一个key，回调只有一个生效
                foreach (var kk in c.Keys)
                {
                    var ccr = c[kk];
                    var json = ccr.text;
                    var data = ccr.data;
                    try
                    {
                        if (failTime > tryUnpackAndDownloadTime)
                        {
                            var fsh1 = new RockHttp()
                            {
                                timeout = 20,
                                callback = fcb2
                            };
                            fsh1.SetUrls("files", flist2backup.ToArray());
                            fsh1.Start();
                            finish = true;
                            break;
                        }
                        else
                        {
                            File.WriteAllBytes(bytesUrl, data);
                            var zipInputStream = new ZipInputStream(File.OpenRead(bytesUrl.ToString()));
                            var result = War.Script.Utility.DecompressFile(zipInputStream, bytesUrl.ToString(),
                                basePersistentPath);
                            if (result == true)
                            {
                                zipInputStream.Close();
                                zipInputStream.Dispose();
                                var file2Path = bytesUrl.ToString().Replace("files2.bytes", "files2.txt");
                                var stream = new FileStream(file2Path, FileMode.Open, FileAccess.Read, FileShare.None);
                                StreamReader sr = new StreamReader(stream, System.Text.Encoding.Default);
                                var file2Text = sr.ReadToEnd();
                                sr.Close();

                                File.WriteAllText(localCacheRemoteUrl, file2Text);
                                PlayerPrefs.SetString(filesVer, currentPatchUrl);
                                var keyInKey2mark = collection_res_key?.ToString();
                                if (!string.IsNullOrEmpty(keyInKey2mark))
                                {
                                    res_key = keyInKey2mark;
                                    hashRemoteParseJson = file2Text;
                                    Log("hashRemoteParseJson_3");
                                }

                                hashRemote = War.Base.hashCheck.Parse(file2Text, res_key?.ToString(),
                                    ch_res_key?.ToString()); // hashRemote添加ch_res_key配置
                            }
                            else
                            {
                                LogErrorFormat(" 解压失败,路径= {0}", bytesUrl);
                                failTime++;
                            }

                            if (hashRemote == null)
                            {
                                LogError("hashRemote parse error");
                                failTime++;
                            }
                            else
                            {
                                iReportFilesFinish = 1;
                                TrackEvent("update_file_finish", new Dictionary<string, object>()
                                {
                                    { "finish_id", iReportFilesFinish },
                                    { "url", ccr.url },
                                });
                                finish = true;
                                break;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        LogErrorFormat(" 解压报错 {0}", e);
                        failTime++;
                        if (File.Exists(bytesUrl))
                            File.Delete(bytesUrl);
                    }
                }

                rh.finish = finish;
            };


            Log("www-" + currentPatchUrl);
            var serverUpdateInfo = UpdateConfig;

            var tmpObj = (object)null;
            serverUpdateInfo.TryGetValue("update_urls", out tmpObj);
            var serverConfigUrls = (List<object>)tmpObj;
            var list = new List<string>();
            if (serverConfigUrls != null)
            {
                foreach (var ob in serverConfigUrls)
                {
                    list.Add(new System.Uri(ob.ToString()).Host);
                }
            }
            else
            {
                serverUpdateInfo.TryGetValue("update_url", out tmpObj);
                if (tmpObj != null)
                {
                    list.Add(new System.Uri(tmpObj.ToString()).Host);
                }
            }

            serverUpdateInfo.TryGetValue("files_url", out tmpObj);
            if (tmpObj == null)
            {
                LogError("files_url can not found");
            }

            var files_url = tmpObj.ToString();
            var files_domain = new System.Uri(files_url).Host;


            // update.json 控制是否使用files2.bytes的方式
            Action<Object> cb;
            cb = fcb;

            var fsh = new RockHttp()
            {
                timeout = 20,
                callback = cb
            };
            var flist = new List<string>();
            foreach (var host in list)
            {
                var domain = files_url.Replace(files_domain, host);
                flist.Add(domain);
                if (domain.Contains("files2.bytes") == true)
                    domain = domain.Replace("files2.bytes", "files2.txt");
                flist2backup.Add(domain);
            }


            var flistbackup = new List<string>();
            flistbackup = flist;

            fsh.SetUrls("files", flistbackup.ToArray());
            fsh.Start();
        }
    }

    /// <summary>
    /// 获取hashRemote被剔除小游戏资源信息前的字符串（War.Base.hashCheck.Parse中的第一个参数）
    /// </summary>
    /// <returns></returns>
    public static War.Base.hashCheck GetHashRemoteParseJson()
    {
        return War.Base.hashCheck.Parse(hashRemoteParseJson);
    }

    int totalZip = 0;
    int unpackedZip = 0;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="patchUrl"></param>
    /// <param name="minVersion"></param>
    /// <param name="maxVersion"></param>
    /// <param name="fileUrl"></param>
    /// <returns></returns>
    protected IEnumerator HandlePatchsFromServer(string patchUrl, long minVersion, long maxVersion)
    {
        // 需要它有序
        SortedDictionary<long, string> versionPatchJson = new SortedDictionary<long, string>();
        Dictionary<string, object> totalSet = null; //合集

        if (IsUpdateDll != null)
            IsUpdateDll.Invoke(true);

        var allPatchSize = 0;
        for (long i = maxVersion; i > minVersion; --i)
        {
            var currentPatchUrl = GetUrlByPlatform(patchUrl, i);
            using (var patchInfoWWW = WWWFromServer(currentPatchUrl))
            {
                CurrStatus = Status.RequestingPatchInfo;
                yield return patchInfoWWW;
                if (patchInfoWWW == null || patchInfoWWW.error != null)
                {
                    RaiseError(ErrCode.ERequestPatchList,
                        currentPatchUrl + (patchInfoWWW != null ? patchInfoWWW.error : ""));
                    continue;
                }

                var patchInfo = (Dictionary<string, object>)Json.Deserialize(patchInfoWWW.text);
                var dic = (Dictionary<string, object>)patchInfo["list"];
                object total_size = patchInfo["total_size"];
                if (totalSet == null)
                {
                    totalSet = dic;
                    versionPatchJson.Add(i, patchInfoWWW.text);
                    allPatchSize += total_size != null ? Convert.ToInt32(total_size) : 0;
                }
                else
                {
                    var preCount = totalSet.Count;

                    #region merge dictionary

                    foreach (KeyValuePair<string, object> kv in dic)
                    {
                        totalSet[kv.Key] = kv.Value;
                    }

                    #endregion

                    if (totalSet.Count > preCount)
                    {
                        versionPatchJson.Add(i, patchInfoWWW.text);
                        allPatchSize += total_size != null ? Convert.ToInt32(total_size) : 0;
                    }
                }
            }
        }

        object patchSize = null;
        // 只更新需要的版本。太老的可以跳过。下面这个保证有序
        foreach (KeyValuePair<long, string> kv in versionPatchJson)
        {
            hashCheck hashCheck = null;
            List<object> patchList = null;
            var patchInfo = (Dictionary<string, object>)Json.Deserialize(kv.Value);
            patchList = (List<object>)patchInfo["patch_list"];
            patchSize = patchInfo["total_size"];

            hashCheck = War.Base.hashCheck.Parse(kv.Value);
            Log(AssetsSync.ToJson(hashCheck));

            foreach (string url in patchList)
            {
                if (StartDownloadApk != null)
                    StartDownloadApk.Invoke(Regex.Match(url, @"(\d+/[^/]*)\.zip$").Groups[1].Value);
                CurrStatus = Status.DownloadingPatch;

                int retryTimes = DOWNLOAD_RETRY_TIMES;
                WWW patchZip = null;

                string realUrl = GetUrlByPlatform(url, kv.Key);
                do // try to download patch until it's done
                {
                    if (patchZip != null)
                    {
                        patchZip.Dispose();
                        --retryTimes;
                        if (retryTimes < 0)
                        {
                            string msg = string.Format("Retry download path from [{0}] failed", realUrl);
                            RaiseError(ErrCode.EDownloadPatch, msg);
                            yield break; // 多次尝试仍失败
                        }

                        LogFormat("Retry downloading patch from [{0}]", realUrl);
                        yield return new WaitForSeconds(DOWNLOAD_RETRY_INTERVAL);
                    }
                    else
                    {
                        LogFormat("Downloading patch from [{0}]", realUrl);
                    }

                    if (OnProgress != null)
                    {
                        if (patchSize != null)
                            OnProgress(0, Convert.ToInt32(patchSize));
                        else
                            OnProgress(0, 100);
                    }

                    //CurrStatus = Status.DownloadingPatch;
                    for (patchZip = WWWFromServer(realUrl); !patchZip.isDone && patchZip.error == null;)
                    {
                        yield return new WaitForSeconds(0.1f);
                        if (OnProgress != null && patchZip.progress > 0f)
                        {
                            if (patchSize != null)
                                OnProgress(patchZip.bytesDownloaded, Convert.ToInt32(patchSize));
                            else
                                OnProgress((int)(patchZip.progress * 100f), 100);
                        }
                    }
                } while (!patchZip.isDone || patchZip.error != null);

                LogFormat("Download url[{0}] success!", patchZip.url);

                ++totalZip;
                var hashC = hashCheck;
                // 在线程中解压
                UnpackResToPersistentDataPath(patchZip.bytes, () =>
                {
                    foreach (var item in hashC.list)
                    {
                        hashPersistent.Update(item.Key, item.Value);
                    }

                    //AssetsSync.Instance.UpdateFileTxt(hashPersistent);
                    ++unpackedZip;
                });
                patchZip.Dispose();
            }
            // TODO等上个处理完再开始，不然可能存在次序错乱问题。如果多个patch.zip。包含update.json的成功写入，别的没处理成功就悲剧了

            yield return new WaitUntil(() => { return unpackedZip >= totalZip; });
            //LogFormat("Deal Loading Patch:{0}", currentPatchUrl);
        }

        if (OnProgress != null)
        {
            if (patchSize != null)
                OnProgress(Convert.ToInt32(patchSize), Convert.ToInt32(patchSize));
            else
                OnProgress(100, 100);
        }

        CurrStatus = Status.WaitingForExtraction;
        yield return new WaitUntil(() => { return unpackedZip >= totalZip; });
        Log("Updator:PatchZipFinish");

        Log(totalSet.Keys.ToString());
        // 如果更新的是dll强制重启应用。这样才能使用新的dll
#if UNITY_ANDROID && !UNITY_EDITOR
        //if (/*Regex.IsMatch(sb.ToString(), @"dlls/[\w.-]+\.dll")*/dllUpdate){
            var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            var activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            activity.Call("toastMessage", I18NManager.GetText("reboot_app"));

            activity.Call("reboot");
            activity.Dispose();
            unityPlayer.Dispose();
        //}
#endif
        //LogError("OnUpdateComplete");

        yield return OnUpdateCompleted();
    }

    /// <summary>
    /// 异步回调下载资源
    /// 目前 lua 使用此接口来替换 lua 中不易使用的 DownloadApk_md5 接口
    /// </summary>
    static public void DownloadApk_Md5_Async(MonoBehaviour coroutineBehaviour, string url,
        UpdateProgress downloadProgress, Action<int, int> completeCallback, string saveName, string suffix,
        string md5Value, int partIndex, bool isTail)
    {
        coroutineBehaviour.StartCoroutine(DownloadApk_Md5ByCoroutine(url, downloadProgress, completeCallback, saveName,
            suffix, md5Value, partIndex, isTail));
    }

    static public IEnumerator DownloadApk_Md5ByCoroutine(string url, UpdateProgress downloadProgress,
        Action<int, int> completeCallback, string saveName, string suffix, string md5Value, int partIndex, bool isTail)
    {
        yield return DownloadApk_md5(url, downloadProgress, saveName, suffix, md5Value, partIndex, isTail);
        if (completeCallback != null)
        {
            var savePath = saveName + suffix;
            if (File.Exists(savePath))
            {
                completeCallback.Invoke(0, partIndex);
            }
            else
            {
                // 下载失败，未生成下载文件
                completeCallback.Invoke(1, partIndex);
            }
        }
    }

    /// <summary>
    /// 异步回调下载资源
    /// 目前 lua 使用此接口来替换 lua 中不易使用的 DownloadApk 接口
    /// </summary>
    static public void DownloadApk_Async(MonoBehaviour coroutineBehaviour, string url, UpdateProgress downloadProgress,
        Action<int, int> completeCallback, string saveName)
    {
        coroutineBehaviour.StartCoroutine(DownloadApk_ByCoroutine(url, downloadProgress, completeCallback, saveName));
    }

    static public IEnumerator DownloadApk_ByCoroutine(string url, UpdateProgress downloadProgress,
        Action<int, int> completeCallback, string saveName)
    {
        yield return DownloadApk(url, downloadProgress, saveName);
        if (completeCallback != null)
        {
            var savePath = saveName;
            if (File.Exists(savePath))
            {
                completeCallback.Invoke(0, 0);
            }
            else
            {
                // 下载失败，未生成下载文件
                completeCallback.Invoke(1, 0);
            }
        }
    }

    static public byte[] AppendFile(FileStream streamWriter, string appendFilePath, byte[] bytes)
    {
        using (FileStream reader = new FileStream(appendFilePath, FileMode.Open, FileAccess.Read))
        {
            if (bytes == null || bytes.Length < reader.Length)
            {
                bytes = new byte[reader.Length];
            }

            reader.Read(bytes, 0, (int)reader.Length);
            streamWriter.Write(bytes, 0, (int)reader.Length);
        }

        return bytes;
    }

    static private int _6M = 1024 * 1024 * 6;
    private int downloadTryTimes = 0;
    private const int MaxTry = 20;

    static protected IEnumerator DownloadApk_md5(string url, UpdateProgress downloadProgress, string saveName,
        string suffix, string md5Value, int partIndex, bool isTail)
    {
        UnityEngine.WWW downloader = null;
        string range = string.Format("bytes={0:D}-{1:D}", partIndex * _6M, (partIndex + 1) * _6M - 1);
        if (isTail)
            Regex.Replace(range, "-[0-9]+", "-");

        var dic = new Dictionary<string, string>
        {
            { "Range", range }
        };
        for (downloader = /*WWWFromServer(url)*/new UnityEngine.WWW(url /* + suffix*/, null, dic);
             !downloader.isDone && downloader.error == null;)
        {
            yield return new WaitForSeconds(0.1f);
            if (downloadProgress != null && downloader.progress > 0f)
            {
                downloadProgress((int)(downloader.progress * 100f), 100);
            }
        }

        bool suc = true;
        // 切换网络之类的
        if (downloader.error != null /*&& ++downloadTryTimes <= MaxTry*/)
        {
            yield return new WaitForSeconds(2f);
            LogError(url + downloader.error);
            suc = false;
        }

        // 校验md5码
        string downloadaMd5 = War.Common.AlgorithmUtils.HashMD5(downloader.bytes);
        if (downloadaMd5 != md5Value)
        {
            yield return new WaitForSeconds(1.6f);
            Log(War.Common.AlgorithmUtils.HashMD5(downloader.bytes) + " != " + md5Value);
            suc = false;
        }

        if (suc)
        {
            // 保存到本地
            using (FileStream streamWriter = new FileStream(saveName + suffix, FileMode.Create, FileAccess.Write))
            {
                streamWriter.Write(downloader.bytes, 0, downloader.bytesDownloaded);
            }

            downloader.Dispose();
        }
        else
        {
            downloader.Dispose();
            yield return DownloadApk_md5(url, downloadProgress, saveName, suffix, md5Value, partIndex, isTail);
        }
    }

    static public IEnumerator DownloadApk(string url, UpdateProgress downloadProgress, string saveName)
    {
        WWW downloader = null;
        for (downloader = new WWW(url); !downloader.isDone && downloader.error == null;)
        {
            yield return new WaitForSeconds(0.1f);
            if (downloadProgress != null && downloader.progress > 0f)
            {
                downloadProgress((int)(downloader.progress * 100f), 100);
            }
        }

        // 切换网络之类的
        if (downloader.error != null /*&& ++downloadTryTimes <= MaxTry*/)
        {
            yield return new WaitForSeconds(2f);
            LogError(url + downloader.error);
        }

        if (downloader.error == null)
        {
            string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
            string basePersistentPath = basePersistentUrl.Replace("file://", "");

            #region 删除以前apk

            var files = Directory.GetFiles(basePersistentPath, "*.apk", SearchOption.TopDirectoryOnly);
            foreach (var filePath in files)
            {
                Log("delete exist apk " + filePath);
                File.Delete(filePath);
            }

            #endregion

            string fullfileName = saveName;
            Log("Save Apk: " + fullfileName);

            using (FileStream streamWriter = new FileStream(fullfileName, FileMode.Create, FileAccess.Write))
            {
                streamWriter.Write(downloader.bytes, 0, downloader.bytesDownloaded);
            }

            downloader.Dispose();
        }
        else
        {
            downloader.Dispose();
        }
    }

    protected IEnumerator DownloadApk(string url, UpdateProgress downloadProgress)
    {
        WWW downloader = null;
        for (downloader = /*WWWFromServer(url)*/new WWW(url); !downloader.isDone && downloader.error == null;)
        {
            yield return new WaitForSeconds(0.1f);
            if (downloadProgress != null && downloader.progress > 0f)
            {
                downloadProgress((int)(downloader.progress * 100f), 100);
            }
        }

        // 切换网络之类的
        if (downloader.error != null && ++downloadTryTimes <= MaxTry)
        {
            yield return new WaitForSeconds(3f);
            LogError(url + downloader.error);
            //downloader.Dispose();
            yield return DownloadApk(url, downloadProgress);
        }

        if (downloader.error == null)
        {
            // 捕获文件名
            var match = Regex.Match(url, @"([^/\\]+\.apk)$");
            if (!match.Success)
                yield break;

            string basePersistentUrl = BaseLoader.GetBasePersistentUrl();
            string basePersistentPath = basePersistentUrl.Replace("file://", "");

            #region 删除以前apk

            var files = Directory.GetFiles(basePersistentPath, "*.apk", SearchOption.TopDirectoryOnly);
            foreach (var filePath in files)
            {
                Log("delete exist apk " + filePath);
                File.Delete(filePath);
            }

            #endregion

            string fullfileName = Path.Combine(basePersistentPath, /*"apks/" + */match.Value);
            Log("Save Apk: " + fullfileName);

            using (FileStream streamWriter = new FileStream(fullfileName, FileMode.Create, FileAccess.Write))
            {
                streamWriter.Write(downloader.bytes, 0, downloader.bytesDownloaded);
            }

            cachefullfileName = fullfileName;
            ShowGoToInstall(true);
            downloader.Dispose();
            //CallInstall();
        }
        else
        {
            downloader.Dispose();
        }
    }

    GameObject mConfirmUI;

    public void ShowGoToInstall(bool show = true)
    {
        //LogError("ShowConfirm"); 
        mConfirmUI = mConfirmUI ?? GameObject.Find("ConfirmRoot");
        if (mConfirmUI)
        {
            mConfirmUI.transform.GetChild(1).gameObject.SetActive(show);
        }
    }

    string cachefullfileName = null;

    public void SetCachedFullFileName(string fullFileName)
    {
        cachefullfileName = fullFileName;
    }

    static public bool InstallApk(string apkPath)
    {
        if (string.IsNullOrEmpty(apkPath))
        {
            return false;
        }

        Debug.Log("install apk:" + apkPath);
#if UNITY_EDITOR
#elif UNITY_ANDROID
        var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        var activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
        activity.CallStatic("installApk", apkPath);
        activity.Dispose();
        unityPlayer.Dispose();
#endif
        return true;
    }

    public void CallInstall()
    {
        Log("CallInstall");

        // 安装
        InstallApk(cachefullfileName);
    }

    Dictionary<string, object> ReadLocalUpdateInfo(string basePersistentPath)
    {
        var localUpdateConfigPath = basePersistentPath + AssetBundleManager.UPDATE_CONFIG;
        if (!File.Exists(localUpdateConfigPath))
        {
            return null;
        }

        try
        {
            string content;
            if (isGarbleUpdateJson)
            {
                var bytes = File.ReadAllBytes(localUpdateConfigPath);
                bytes = Aes.Decryptor(bytes, AssetBundleManager.UpdateConfigEncryptKey,
                    AlgorithmUtils.HashUtf8MD5(AssetBundleManager.UPDATE_CONFIG).Substring(8, 16));
                content = Encoding.Unicode.GetString(bytes);
                Debug.Log("AssetsUpdator ReadLocalUpdateInfo update.json decryptor done. json:" + content);
            }
            else
            {
                content = FileEd.ReadAllText(localUpdateConfigPath);
            }

            var localUpdateInfo = (Dictionary<string, object>)Json.Deserialize(content);

            return localUpdateInfo;
        }
        catch (System.Exception e)
        {
            LogErrorFormat("Read [{0}] failed! error: ", localUpdateConfigPath, e.Message);
            return null;
        }
    }

    string ReadLocalFile(string path)
    {
        if (!File.Exists((string)path))
        {
            return null;
        }

        try
        {
            var content = FileEd.ReadAllText((string)path);
            return content;
        }
        catch (System.Exception e)
        {
            LogErrorFormat("Read [{0}] failed! error: ", (object)path, e.Message);
            return null;
        }
    }

    UnityWebRequest UWRFromServerPure(string path, int tiemout = 5)
    {
        //ipv6含有[]特殊字符，不能参与转换
        //url = System.Uri.EscapeUriString(url);
        var url = IP.Url2IPv6(path);

        var req = UnityWebRequest.Get(url);
        req.timeout = tiemout;
        return req;
        //return new WWW(url);
    }

    WWW WWWFromServer(string path)
    {
        //string url = string.Format("{0}?t={1}", path, System.DateTime.UtcNow.Day / 3);
        //ipv6含有[]特殊字符，不能参与转换
        //url = System.Uri.EscapeUriString(url);
        var url = IP.Url2IPv6(path);
        return new WWW(url);
    }

    string UrlStr(string path)
    {
        //string url = string.Format("{0}?t={1}", path, System.DateTime.UtcNow.Day / 3);
        //ipv6含有[]特殊字符，不能参与转换
        //url = System.Uri.EscapeUriString(url);
        var url = IP.Url2IPv6(path);
        return url;
    }

    void UnpackResToPersistentDataPath(byte[] data, System.Action callBack)
    {
        ZipData zipData = new ZipData();
        zipData.Data = data;

        zipData.callBack = callBack;

        lock (m_QueueToUnpack.SyncRoot)
        {
            m_QueueToUnpack.Enqueue(zipData);
            Monitor.Pulse(m_QueueToUnpack.SyncRoot);
        }
    }

    void StartUnpackToPersistent(object param)
    {
        string basePersistentPath = param as string;
        while (!ms_IsReady)
        {
            ZipData zipData;
            lock (m_QueueToUnpack.SyncRoot)
            {
                while (m_QueueToUnpack.Count <= 0)
                {
                    Monitor.Wait(m_QueueToUnpack.SyncRoot);
                }

                zipData = (ZipData)m_QueueToUnpack.Dequeue();
            }

            ZipInputStream zipFile = new ZipInputStream(new MemoryStream(zipData.Data));
            for (var zipEntry = zipFile.GetNextEntry(); zipEntry != null; zipEntry = zipFile.GetNextEntry())
            {
                if (zipEntry.IsDirectory)
                {
                    continue;
                }
                else
                {
                    string dirName = Path.GetDirectoryName(zipEntry.Name);
                    if (dirName.Length > 0)
                    {
                        dirName = Path.Combine(basePersistentPath, dirName);
                        if (!Directory.Exists(dirName))
                        {
                            Directory.CreateDirectory(dirName);
                        }
                    }

                    string fullfileName = Path.Combine(basePersistentPath, zipEntry.Name);
                    //LogWarningFormat("Extracting file: {0}", fullfileName);
                    using (FileStream streamWriter = new FileStream(fullfileName, FileMode.Create, FileAccess.Write))
                    {
                        byte[] content = new byte[TRUNK_SIZE];

                        for (int readSize = 0;;)
                        {
                            readSize = zipFile.Read(content, 0, TRUNK_SIZE);
                            if (readSize <= 0)
                            {
                                break;
                            }

                            streamWriter.Write(content, 0, readSize);
                        }
                    }
                }
            }

            zipFile.Close();

            if (zipData.callBack != null)
            {
                zipData.callBack();
            }
        }
    }

    void OnDestroy()
    {
        if (m_ThreadUnpack != null)
        {
            m_ThreadUnpack.Abort();
            m_ThreadUnpack = null;
        }

        ms_IsReady = false;
    }

    void RaiseError(ErrCode code, string message = null)
    {
        if (OnError != null)
        {
            OnError(code, message);
        }
    }

    IEnumerator OnUpdateCompleted()
    {
        Log("OnUpdateCompleted:");
        //LogError("OnUpdateComplete");

        //yield return CombineHashCheck(hashRemote, hashPersistent, hashStreamingAssets);
        //CombineHashCheck(hashRemote, hashPersistent, hashStreamingAssets);
        //Log("Updator:CombineHashCheck");

        //gameObject.AddComponent<AssetLoader>();


        TrackEvent("asset_updator_end", new Dictionary<string, object>()
        {
            { "consume", (Time.realtimeSinceStartup - startTime).ToString() },
            { "loop_times", loopTimes.ToString() }
        });
        StartEvent("updateEnd",
            "ver=" + System.DateTime.Now.ToString("yyyy-MM-dd.HH-mm-ss-ff",
                System.Globalization.DateTimeFormatInfo.InvariantInfo));

        if (OnCompleted != null)
        {
            OnCompleted();
        }

        Log("Updator:CompleteCallback");
        ms_IsReady = true;
        //PrintAll();
        yield return null;
    }

    IEnumerator WaitUpdateServerInfo(string url)
    {
        //yield return new WaitForSeconds(10); //server info 下载延时情况下测试无服务器列表时登录问题
        Debug.LogWarning("ServerInfo, wait end, start to load serverinfo");

        var validServerInfo = ServerInfo.Instance.IsValid();
        while (!validServerInfo)
        {
            yield return UpdateServerInfo(url);
            validServerInfo = ServerInfo.Instance.IsValid();
            if (!validServerInfo)
            {
                yield return new WaitForSeconds(1);
            }
        }

        //while (!ServerInfo.Instance.IsValid())
        //{
        //    yield return UpdateServerInfo(url);
        //    yield return new WaitForSeconds(1);
        //}

        Debug.LogWarning("ServerInfo, wait end, load serverinfo end");
    }

    IEnumerator UpdateServerInfo(string url)
    {
        ServerInfo.Instance.Clear();
        if (string.IsNullOrEmpty(url))
        {
            LogError("UpdateServerInfo url is empty");
            yield break;
        }

        Dictionary<string, object> serverInfo = null;

        var tryCount = URL_LENGTH * 6;
        for (int i = 0; i < tryCount; i++)
        {
            CurrStatus = Status.UpdateServerInfo;
            using (var request = UnityWebRequest.Get(url))
            {
                LogFormat("try get server_info remain:{0},url:{1}!", tryCount, url);

                TrackEvent("update_server_start", new Dictionary<string, object>()
                {
                    { "url", url },
                    { "times", i + 1000 * loopTimes },
                });

                request.timeout = GetTimeout(i);
                yield return request.SendWebRequest();

                if (string.IsNullOrEmpty(request.error))
                {
                    var data = request.downloadHandler.data;
                    bool encryptUpdateAndServerInfo = Config.IsTrue("p_encryptUpdateAndServerInfo");
                    if (encryptUpdateAndServerInfo) // 需要解密
                        data = War.Common.AlgorithmUtils.XorEncrypt(data);

                    var serverInfoData = Encoding.UTF8.GetString(data);
                    //ServerInfo.Instance.mConfigJson = serverInfoData;
                    serverInfo = (Dictionary<string, object>)Json.Deserialize(serverInfoData);
                    LogFormat("Get server_info finish:{0},url:{1}!", tryCount, url);
                    Log("serverInfo:" + (serverInfo != null ? serverInfo.Count : 0));

                    TrackEvent("update_server_finish", new Dictionary<string, object>()
                    {
                        { "url", url },
                        { "times", i + 1000 * loopTimes },
                    });
                    break;
                }
                else
                {
                    LogErrorFormat("Get server_info remain:{0},url:{1}!error:{2}", tryCount, url, request.error);
                }
            }
        }

        if (serverInfo == null)
        {
            LogErrorFormat("Failed to parse server info [{0}]!", url);
            yield break;
        }

        object obj;
        if (serverInfo.TryGetValue("server_list", out obj))
        {
            List<object> serverList = (List<object>)obj;
            foreach (var addr in serverList)
            {
                Dictionary<string, object> dic = addr as Dictionary<string, object>;
                int id = System.Convert.ToInt32(dic["server_id"]);

                List<object> oList = (List<object>)dic["tcp_ip_port"];
                List<string> sList = new List<string>();
                foreach (string s in oList)
                {
                    sList.Add(s);
                }

                List<object> oRealList = new List<object>();
                if (dic.ContainsKey("real_ip"))
                    oRealList = (List<object>)dic["real_ip"];
                else
                    oRealList = (List<object>)dic["tcp_ip_port"];

                List<string> sRealList = new List<string>();
                foreach (string s in oRealList)
                {
                    sRealList.Add(s);
                }

                var isContainsKey = dic.ContainsKey("is_repaired");
                var isrepairedValue = false;
                if (isContainsKey)
                {
                    isrepairedValue = (bool)dic["is_repaired"];
                }

                ServerInfo.Instance.AddTcpIpPort(sList, sRealList, id, (string)dic["server_name"], (bool)dic["is_new"],
                    isrepairedValue, Convert.ToInt32(dic["region_id"]));


                oList = (List<object>)dic["udp_ip_port"];
                sList = new List<string>();
                foreach (string s in oList)
                {
                    sList.Add(s);
                }

                ServerInfo.Instance.AddUdpIpPort(sList, id, (string)dic["server_name"], (bool)dic["is_new"],
                    isrepairedValue, Convert.ToInt32(dic["region_id"]));
            }
        }

        object obj1;
        if (serverInfo.TryGetValue("connect_test", out obj1))
        {
            List<object> serverList = (List<object>)obj1;
            List<string> ipsList = new List<string>();
            List<string> domainsList = new List<string>();
            foreach (var addr in serverList)
            {
                Dictionary<string, object> dict = addr as Dictionary<string, object>;
                if (dict.ContainsKey("ip") && dict.ContainsKey("domain"))
                {
                    List<object> ipList = (List<object>)dict["ip"];
                    foreach (string s in ipList)
                    {
                        ipsList.Add(s);
                    }

                    List<object> domainList = (List<object>)dict["domain"];
                    foreach (string s in domainList)
                    {
                        domainsList.Add(s);
                    }
                }
            }

            if (ipsList.Count > 0 && domainsList.Count > 0)
            {
                StartConnectTest(ipsList[0], domainsList[0]);
            }
        }

        Log("UpdateServerInfo-Finish");
    }

    void StartConnectTest(string url1, string url2)
    {
        GameObject connectTestObj = GameObject.Find("/ConnectTest");
        DontDestroyOnLoad(connectTestObj);
        if (!connectTestObj)
        {
            ConnectTest ct = connectTestObj.GetComponent<ConnectTest>();
            if (!ct)
            {
                ct.SendRequest(url1, url2);
            }
        }
    }

    void TrackEventHandler(string eventName, string properties)
    {
        if (Q1.Q1SDK.Instance != null)
        {
            Q1.Q1SDK.Instance.TrackEvent(eventName, properties);
        }
    }

    bool HybridLoadBg()
    {
        if (Launch.LaunchMgr.Ins)
        {
            GameObject loginBg = GameObject.Find("StartupCanvas/Bg/Model/UILoginBg/imgLoginBg");
            if (loginBg != null)
            {
                UnityEngine.UI.RawImage rawImage = loginBg.GetComponent<UnityEngine.UI.RawImage>();
                if (rawImage != null)
                {
                    rawImage.texture = Launch.LaunchMgr.Ins.LaunchUI.bgRawImage.texture;
                    rawImage.enabled = true;
                }
            }

            return true;
        }

        return false;
    }

    //加载StreamingAssets内的loading背景图
    IEnumerator loadStreamingAssetsBg(string prefFile, string imgPath)
    {
        if (HybridLoadBg()) yield break;
        if (string.IsNullOrEmpty(prefFile))
        {
            prefFile = "updatehero1.png";
        }

        if (string.IsNullOrEmpty(imgPath))
        {
            imgPath = "StartupCanvas/Bg/Model/UILoginBg/imgLoginBg";
        }

        string url = prefFile;
        if (prefFile.IndexOf("://") == -1)
        {
            string streamingAssetsPath = Application.streamingAssetsPath;
            if (streamingAssetsPath == "")
            {
                streamingAssetsPath = Application.dataPath + "/StreamingAssets/";
            }

            url = System.IO.Path.Combine(streamingAssetsPath, prefFile);
        }

        url = GetStreamingPath(url);

        //Debug.Log("loadStreamingAssetsBg: " + url);
        WWW www = new WWW(url);
        yield return www;

        if (!string.IsNullOrEmpty(www.error))
        {
            Debug.LogError(www.error);
        }
        else
        {
            Texture2D texture = www.texture;
            GameObject loginBg = GameObject.Find(imgPath);
            if (loginBg != null)
            {
                UnityEngine.UI.RawImage rawImage = loginBg.GetComponent<UnityEngine.UI.RawImage>();
                if (rawImage != null)
                {
                    rawImage.texture = texture;
                    rawImage.enabled = true;
                }
            }
        }

        yield break;
    }

    private void SetGlobalLuaExceptionReport()
    {
        if (null == UpdateConfig || 0 >= UpdateConfig.Count)
        {
            Debug.LogWarning($"[LAUNCH]AssetsUpdator.SetGlobalLuaExceptionReport() : No config.");
            LuaEnv.EnableGlobalLuaExceptionReport = false;
            return;
        }

        if (!UpdateConfig.ContainsKey("enable_global_lua_exception_report"))
        {
            Debug.LogWarning($"[LAUNCH]AssetsUpdator.SetGlobalLuaExceptionReport() : No config key.");
            LuaEnv.EnableGlobalLuaExceptionReport = false;
            return;
        }

        Boolean enableGlobalLuaExceptionReport = false;
        Boolean.TryParse(UpdateConfig["enable_global_lua_exception_report"].ToString(),
            out enableGlobalLuaExceptionReport);
        LuaEnv.EnableGlobalLuaExceptionReport = enableGlobalLuaExceptionReport;
        Debug.LogWarning(
            $"[LAUNCH]AssetsUpdator.SetGlobalLuaExceptionReport() :  enable_global_lua_exception_report={UpdateConfig["enable_global_lua_exception_report"]} EnableGlobalLuaExceptionReport={enableGlobalLuaExceptionReport}");
    }

    public static void PrintDns(string url)
    {
        try
        {
            int startIndex = url.IndexOf("//");
            int endIndex = url.IndexOf("/", startIndex + 2);
            var shortdomain = url.Substring(startIndex + 2, endIndex - startIndex - 2);
            Log(shortdomain);

            var host = System.Net.Dns.BeginGetHostEntry(shortdomain, (ar) =>
            {
                var ie = System.Net.Dns.EndGetHostEntry(ar);

                U3D.Threading.Dispatcher.instance.ToMainThread(() =>
                {
                    Log("dns:" + url + "|" + ie.HostName);

                    foreach (var adres in ie.AddressList)
                    {
                        Log("dns:" + ie.HostName + "|" + adres);
                    }
                });
            }, null);
        }
        catch (Exception e)
        {
            LogError(e.ToString());
        }
        //try
        //{
        //    int startIndex = url.IndexOf("//");
        //    int endIndex = url.IndexOf("/", startIndex + 2);
        //    var shortdomain = url.Substring(startIndex+2,endIndex-startIndex-2);
        //    Log(shortdomain);

        //    var host = System.Net.Dns.GetHostEntry(shortdomain);

        //    foreach (var adres in host.AddressList)
        //    {
        //        Log("dns:" + url + "|" + adres);
        //    }
        //}
        //catch (Exception e)
        //{
        //    LogError(e.ToString());
        //}
    }
    //static DateTime? startTick;
    //static long mStartTick
    //{
    //    get
    //    {
    //        if (startTick == null)
    //        {
    //            startTick = DateTime.UtcNow;

    //            Debug.Log("mStartTick:" + startTick.Value.ToLongDateString() + startTick.Value.ToLongTimeString());
    //        }
    //        return (DateTime.UtcNow - startTick.Value).Ticks / 10000;
    //    }
    //}

    public static void Log(string content)
    {
        LogHelpBase.LogMessage(content);
    }

    public static void LogFormat(string format, params object[] args)
    {
        LogHelpBase.LogMessage(string.Format(format, args));
        //Debug.LogFormat(mStartTick + ":" + format, args);
    }

    public static void LogWarning(object message)
    {
        LogHelpBase.LogMessage(message);
        //Debug.LogWarning(mStartTick + ":" + message);
    }

    public static void LogWarningFormat(string format, params object[] args)
    {
        LogHelpBase.LogMessage(string.Format(format, args));
        //Debug.LogWarningFormat(mStartTick + ":" + format, args);
    }

    public static void LogError(object message)
    {
        LogHelpBase.LogError(message);
        //Debug.LogError(mStartTick + ":" + message);
    }

    public static void LogErrorFormat(string format, params object[] args)
    {
        LogHelpBase.LogErrorFormat(string.Format(format, args));
        //Debug.LogErrorFormat(mStartTick + ":" + format, args);
    }

    public static void RegisterDriverNodeUpdateCb(Action<string> callback)
    {
        OnUpdateDriverNodeUrl += callback;
    }

    public static void RegisterInitDynamicUrlCb(Action callback)
    {
        OnInitDynamicUrl += callback;
    }

    // 给lua调用
    public static string IsGarble(string fileName)
    {
        if (ABPathKey.Instance)
        {
            var gName = ABPathKey.Instance.Get(fileName);
            if (string.IsNullOrEmpty(gName) == false)
            {
                fileName = gName;
                Debug.Log($"AssetsUpdator find {fileName} garble key");
            }
            else
            {
                Debug.Log($"AssetsUpdator not find {fileName} garble key");
            }
        }

        return fileName;
    }

    // 给lua调用
    public static string Decryptor(byte[] bytes, string fileName)
    {
        bytes = Aes.Decryptor(bytes, AssetBundleManager.UpdateConfigEncryptKey,
            AlgorithmUtils.HashUtf8MD5(fileName).Substring(8, 16));
        var json = Encoding.Unicode.GetString(bytes);
        Debug.Log($"AssetsUpdator decryptor {fileName} done. json:" + json);
        return json;
    }

    public static void EnableHybridCLR()
    {
#if UNITY_ANDROID
        var ins = HybridCLRManager.Instance;
        var t = HybridCLRAOTGenericReference.allTypes;
#endif
    }

    // 用 p_stand_alone_server_root_url 去下载本项目支持的独立热更小游戏csv
    //public static string SupportMiniGameStr = "";
    //public static AssetsUpdator Instance;
    //public static void GetSupportMiniGameCSV()
    //{
    //    if (Application.isEditor)
    //    {
    //        string url = "http://172.16.126.185:3122/CasualGame_Pure_Res_105_002/TinyRes/supportgame.csv";
    //        Debug.Log("Editor Test URL==" + url);
    //        Instance.StartCoroutine(DownLoadSupportMiniGameCSV(url));
    //    }
    //    else
    //    {
    //        if (UpdateConfig.ContainsKey("p_stand_alone_server_root_url"))
    //        {
    //            string url = UpdateConfig["p_stand_alone_server_root_url"] as string;
    //            Debug.Log("p_stand_alone_server_root_url==" + url);
    //            url = Path.Combine(url, "supportgame.csv");
    //            Instance.StartCoroutine(DownLoadSupportMiniGameCSV(url));
    //        }
    //        else
    //        {
    //            Debug.Log("p_stand_alone_server_root_url==null");
    //        }
    //    }
    //}
    //static IEnumerator DownLoadSupportMiniGameCSV(string url)
    //{
    //    UnityWebRequest www = UnityWebRequest.Get(url);
    //    var reqTime = Time.realtimeSinceStartup;
    //    Debug.Log("MiniGameCSV  Time.realtimeSinceStartup:" + reqTime + "MiniGameCSVUrl" + url);
    //    yield return www.SendWebRequest();
    //    if (www.error == null)
    //    {
    //        SupportMiniGameStr = www.downloadHandler.text;
    //        Debug.Log("SupportMiniGameStr:" + SupportMiniGameStr + "  Time.realtimeSinceStartup:" + Time.realtimeSinceStartup + " total:" + (Time.realtimeSinceStartup - reqTime));
    //    }
    //    else
    //    {
    //        Debug.LogError("SupportMiniGameStr Error " + www.error);
    //    }
    //}
}